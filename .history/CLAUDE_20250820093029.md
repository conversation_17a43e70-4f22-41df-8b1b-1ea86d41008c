# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🏗️ Project Overview

This is a **technical analysis system (Analyze-system2)** for stock market trend analysis using moving averages, MCSI indicators, and composite scoring. The system consists of multiple components:

- **Core Analysis Engine**: MCSI indicators (MACD, RSI, MMT, TD9, TTM) with Cython optimization
- **Web Interface**: Flask-based dashboard on port 5008 for trend visualization
- **Data Processing**: Hybrid data loader supporting CSV and database sources
- **Trading View Scripts**: Pine Script implementations for TradingView platform

## ⚙️ Development Commands

### Starting Services
```bash
# Start trend analysis web service (port 5008)
./start_trend_service.sh

# Stop trend analysis service
./stop_trend_service.sh

# Start main web application
cd web && python app.py
```

### Testing and Building
```bash
# Run trend analysis tests
python test_trend_analysis.py

# Note: Cython extensions have been removed from this project
# All MCSI indicators now use pure Python implementations
```

### Data Operations
```bash
# Test trend analysis with sample data
python trend_analyzer.py

# Load data using hybrid loader
python -m core.data.hybrid_data_loader
```

## 🧱 Architecture Overview

### Core Structure
```
core/
├── composite/           # Main scoring engine
│   └── scorer.py       # NewCompositeScorer - orchestrates all analysis
├── indicators/         # MCSI technical indicators
│   ├── mcsi_macd*.py   # MACD indicator implementations
│   ├── mcsi_rsi*.py    # RSI indicator implementations  
│   ├── mcsi_mmt*.py    # Momentum indicator implementations
│   └── weekly_aggregator.py  # Market data aggregation
├── scoring_units/      # MCSI scoring units
│   ├── mcsi_macd_scoring.py   # MCSI MACD统一接口 (100% consistent)
│   ├── mcsi_mmt_scoring.py    # MCSI MMT统一接口 (100% consistent)
│   ├── mcsi_rsi_scoring.py    # MCSI RSI统一接口 (100% consistent)
│   └── mcsi_ttm_scoring.py    # MCSI TTM统一接口 (100% consistent)
├── data/              # Data loading and storage
│   ├── hybrid_data_loader.py  # Primary data interface
│   ├── csv_data_loader.py     # CSV file support
│   └── data_loader.py         # Database connectivity
└── config/            # Market and weight configuration
    ├── market_config.py       # Trading hours, market type detection
    └── weight_config_manager.py  # Dynamic weight management
```

### Key Design Patterns
- **Unified MCSI Interface**: All MCSI indicators accessed through individual统一接口单元 (mcsi_*_scoring.py)
- **Market Type Detection**: Automatic detection of stock/futures/crypto based on data patterns
- **Hybrid Data Loading**: Seamless switching between CSV files and database
- **Composite Scoring**: Weighted combination of multiple technical indicators
- **Pure Python Implementation**: All calculations use optimized Python code

### Web Architecture
- **Main App** (`web/app.py`): Full-featured dashboard with group management
- **Trend Service** (`trend_service.py`): Simplified trend analysis service (port 5008)
- **Templates**: Professional charting interface with real-time updates

## 📊 Configuration Management

### Database Configuration
Located in `config.py` - **Note**: Contains hardcoded credentials that should be externalized:
```python
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 5433,
    'database': 'fintech_db'
    # Credentials should be moved to environment variables
}
```

### Weight and Group Configuration
Dynamic configuration stored in `config/` JSON files:
- `group_config.json`: Trading strategy groupings
- `weight_config.json`: Indicator importance weights  
- `composite_config.json`: Overall system parameters

### Moving Average Periods
```python
MA_PERIODS = {
    'short': 5,        # 5-day (weekly trend)
    'medium': 20,      # 20-day (monthly trend) 
    'long': 50,        # 50-day (quarterly trend)
    'ultra_long': 200  # 200-day (yearly trend)
}
```

## 🎯 Scoring System

### Trend Analysis (Primary Algorithm)
- **Score Range**: -6 to +6 points
- **Comparison Matrix**: All combinations of MA5, MA20, MA50, MA200
- **Rating System**: A+ (6pts) to D (-6pts)
- **Market Detection**: Automatic stock/futures/crypto classification

### MCSI Indicators (Secondary Scoring)
- **MCSI-MACD**: Momentum and trend confluence
- **MCSI-RSI**: Oversold/overbought conditions
- **MCSI-MMT**: Multi-timeframe momentum
- **MCSI-TD9**: Tom DeMark sequential patterns
- **MCSI-TTM**: Time-to-market signals

## 🔧 Development Notes

### Data Requirements
- Minimum 200 trading days for MA200 calculation
- Support for multiple data sources (PostgreSQL, CSV files)
- Market-aware data aggregation (handles trading hours)

### Performance Considerations
- Optimized NumPy operations for MCSI calculations
- Batch processing for large datasets
- Memory-efficient data structures for real-time updates

### Security Considerations
- Database credentials should be externalized to environment variables
- No sensitive data in configuration files
- Input validation for all API endpoints

## 📝 Development Guidelines

### Adding New Indicators
1. Implement base class in `core/indicators/`
2. Create scoring unit in `core/scoring_units/`
3. Update composite scorer configuration
4. Add web interface visualization

### Testing Strategy
- Unit tests for individual indicators
- Integration tests for composite scoring
- Performance benchmarks for Python implementations
- End-to-end web interface testing

### Code Organization
- Follow existing module structure
- Use type hints for better code documentation
- Implement proper error handling and logging
- Maintain separation between data/logic/presentation layers
