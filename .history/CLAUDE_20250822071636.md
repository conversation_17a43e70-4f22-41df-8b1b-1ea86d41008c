# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🏗️ Project Overview

This is a **technical analysis system (Analyze-system2)** for stock market trend analysis using moving averages, MCSI indicators, and composite scoring. The system consists of multiple components:

- **Core Analysis Engine**: MCSI indicators (MACD, RSI, MMT, TD9, TTM) with pure Python optimization
- **Web Interface**: Flask-based dashboard on port 5008 for trend visualization
- **Data Processing**: Hybrid data loader supporting CSV and database sources
- **Trading View Scripts**: Pine Script implementations for TradingView platform

### 🎯 MCSI技术指标体系 (Multi-Criteria System Indicator)

**MCSI是本系统的核心技术**，包含五大专业技术指标，所有指标都与Pine Script源代码保持100%一致性：

#### 1. MCSI-MACD (动量趋势指标)
- **算法参数**: EMA(19) - EMA(39), 信号线SMA(9)
- **动态阈值**: 标准差 × 1.5 的自适应阈值系统
- **评分范围**: -100 到 +100 (基于柱状图颜色变化和阈值突破)
- **核心逻辑**: 高位转向卖出信号，低位转向买入信号，相对高度加权评分

#### 2. MCSI-RSI (超买超卖指标)
- **算法参数**: CRSI计算，周期长度14，相位滞后3，扭矩0.618
- **真实周线数据**: 使用`request.security`获取真实周线CRSI数据
- **动态轨道**: 基于循环记忆的上下轨道计算
- **评分范围**: -100 到 +100 (日线70% + 周线30%权重)

#### 3. MCSI-MMT (多时间框架动量指标)
- **核心算法**: iWTT_CSI_processor + 动态轨道 + 背离检测
- **评分权重**: 轨道位置50% + 背离信号50%
- **背离检测**: 支持看涨/看跌背离和隐藏背离的完整检测
- **评分范围**: -100 到 +100

#### 4. MCSI-TD9 (时间序列指标)
- **算法基础**: Tom DeMark序列计算
- **计数逻辑**: 连续上升/下降计数，最大计数9
- **评分映射**: 基于TD计数的特定分值映射规则
- **评分范围**: -100 到 +100

#### 5. MCSI-TTM (时间到市场指标)
- **基础算法**: 基于TD9序列的TTM评分规则
- **信号生成**: 序列完成时的反转信号识别
- **评分范围**: -100 到 +100

## ⚙️ Development Commands

### Starting Services
```bash
# Start trend analysis web service (port 5008)
./start_trend_service.sh

# Stop trend analysis service
./stop_trend_service.sh

# Start main web application
cd web && python app.py
```

### Testing and Building
```bash
# Run trend analysis tests
python test_trend_analysis.py

# Note: Cython extensions have been removed from this project
# All MCSI indicators now use pure Python implementations
```

### Data Operations
```bash
# Test trend analysis with sample data
python trend_analyzer.py

# Load data using hybrid loader
python -m core.data.hybrid_data_loader
```

## 🧱 Architecture Overview

### 🏛️ 三层架构设计

**MCSI指标系统采用严格的三层架构，确保代码质量和一致性**：

#### 第一层：Pine Script源代码 (最权威参考)
```
TV-code/pine-code/
├── MCSI-macd-test.pine    # MACD指标Pine Script源代码
├── MCSI-rsi-test.pine     # RSI指标Pine Script源代码
├── MCSI-mmt-test.pine     # MMT指标Pine Script源代码
├── MCSI-td9-test.pine     # TD9指标Pine Script源代码
└── MCSI.pine              # 综合MCSI指标源代码
```

#### 第二层：Python权威实现 (100%一致性)
```
TV-code/py-code/
├── mcsi_macd.py          # MACD权威Python实现
├── mcsi_rsi.py           # RSI权威Python实现
├── mcsi_mmt.py           # MMT权威Python实现
├── mcsi_td9.py           # TD9权威Python实现
└── mcsi_ttm.py           # TTM权威Python实现
```

#### 第三层：统一接口实现 (生产环境)
```
core/
├── composite/           # Main scoring engine
│   └── scorer.py       # NewCompositeScorer - orchestrates all analysis
├── indicators/         # MCSI technical indicators (mirror of TV-code/py-code)
│   ├── mcsi_macd.py    # MACD indicator implementation
│   ├── mcsi_rsi.py     # RSI indicator implementation
│   ├── mcsi_mmt.py     # MMT indicator implementation
│   ├── mcsi_td9.py     # TD9 indicator implementation
│   └── mcsi_ttm.py     # TTM indicator implementation
├── scoring_units/      # MCSI scoring units (统一接口层)
│   ├── mcsi_macd_scoring.py   # MCSI MACD统一接口 (100% consistent)
│   ├── mcsi_mmt_scoring.py    # MCSI MMT统一接口 (100% consistent)
│   ├── mcsi_rsi_scoring.py    # MCSI RSI统一接口 (100% consistent)
│   ├── mcsi_ttm_scoring.py    # MCSI TTM统一接口 (100% consistent)
│   └── mcsi_authority_units.py # 权威版本直接调用接口
├── groups/             # Group management system
│   ├── group_manager.py       # 分组管理器
│   ├── trend_group.py         # 趋势分组
│   └── oscillation_group.py   # 震荡分组
├── data/              # Data loading and storage
│   ├── hybrid_data_loader.py  # Primary data interface
│   ├── csv_data_loader.py     # CSV file support
│   └── data_loader.py         # Database connectivity
└── config/            # Market and weight configuration
    ├── market_config.py       # Trading hours, market type detection
    └── weight_config_manager.py  # Dynamic weight management
```

### 🎨 核心设计模式

#### 1. 三层一致性保证模式
- **Pine Script源代码**: 作为最权威的算法参考标准
- **Python权威实现**: 完全复制Pine Script的var变量状态管理和计算逻辑
- **统一接口实现**: 直接调用权威实现，提供标准化的评分接口

#### 2. 分组管理模式 (Group Management Pattern)
- **TrendGroup**: 趋势类指标分组 (权重0.6)
- **OscillationGroup**: 震荡类指标分组 (权重0.4)
- **动态权重调整**: 支持实时权重配置和历史记录
- **分组独立性**: 每个分组内的计分单元完全独立

#### 3. 混合数据加载模式 (Hybrid Data Loading)
- **优先级策略**: 数据库 > CSV文件 > 默认数据
- **自动回退机制**: 数据源失败时自动切换到备用数据源
- **数据验证**: 完整的数据质量检查和异常处理

#### 4. 复合评分模式 (Composite Scoring)
- **加权平均**: 基于分组权重的加权平均计算
- **置信度评估**: 基于有效分组数量和数据质量的置信度计算
- **等级映射**: A+到F的直观等级评价系统

#### 5. 状态管理模式 (State Management)
- **var变量模拟**: 完全模拟Pine Script的var变量在K线间的状态保持
- **信号持续时间**: 精确控制信号的持续时间和衰减
- **历史记录**: 完整的计分历史和配置变更记录

### 🌐 Web架构设计

#### 双服务架构
- **Main App** (`web/app.py`): 完整的仪表板，支持分组管理和专业图表 (端口50505)
- **Trend Service** (`trend_service.py`): 简化的趋势分析服务 (端口5008)

#### 专业图表系统
- **LightweightCharts集成**: 专业级金融图表库
- **MCSI权威版本对比**: 实时对比统一接口与权威版本的评分一致性
- **Pine Script数据转换**: 支持Pine Script格式的上下轨道、画线、信号标记
- **垂直布局设计**: 8指标垂直排列，时间轴同步

#### API接口设计
- **并行计算支持**: 8个MCSI指标的并行计算
- **日线级别精度**: 消除稀疏采样，确保评分变化与技术指标图表同步
- **实时数据更新**: 支持实时数据推送和图表更新

## 📊 Configuration Management

### Database Configuration
Located in `config.py` - **Note**: Contains hardcoded credentials that should be externalized:
```python
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 5433,
    'database': 'fintech_db'
    # Credentials should be moved to environment variables
}
```

### Weight and Group Configuration
Dynamic configuration stored in `config/` JSON files:
- `group_config.json`: Trading strategy groupings
- `weight_config.json`: Indicator importance weights  
- `composite_config.json`: Overall system parameters

### Moving Average Periods
```python
MA_PERIODS = {
    'short': 5,        # 5-day (weekly trend)
    'medium': 20,      # 20-day (monthly trend) 
    'long': 50,        # 50-day (quarterly trend)
    'ultra_long': 200  # 200-day (yearly trend)
}
```

## 🎯 Scoring System

### Trend Analysis (Primary Algorithm)
- **Score Range**: -6 to +6 points
- **Comparison Matrix**: All combinations of MA5, MA20, MA50, MA200
- **Rating System**: A+ (6pts) to D (-6pts)
- **Market Detection**: Automatic stock/futures/crypto classification

### 🎯 MCSI指标评分系统 (核心评分算法)

#### MCSI-MACD评分逻辑
```python
# 评分范围: -100 到 +100
# 核心逻辑: 基于柱状图颜色变化和动态阈值突破
if histNearZero:
    macdScore = 0  # 忽略阈值附近的信号
elif histAboveThreshold and histA_IsUp[1] and histA_IsDown:
    # 高位由上升转下降（卖出信号）
    relativeHeight = abs(hist / dynamicThreshold)
    extraScore = min(50, (relativeHeight - 1) * 25)
    macdScore = -(50 + extraScore)  # 负分，最低-100
elif histBelowThreshold and histB_IsDown[1] and histB_IsUp:
    # 低位由下降转上升（买入信号）
    relativeHeight = abs(hist / dynamicThreshold)
    extraScore = min(50, (relativeHeight - 1) * 25)
    macdScore = 50 + extraScore  # 正分，最高+100
```

#### MCSI-RSI评分逻辑
```python
# 评分范围: -100 到 +100
# 权重分配: 日线70% + 周线30%
# 核心逻辑: 基于CRSI突破动态轨道的信号强度
if crsi突破下轨且上升:
    dailyScore = 67  # 强买入信号
elif crsi跌破上轨且下降:
    dailyScore = -67  # 强卖出信号
elif crsi在下轨之下:
    dailyScore = 27 if 上升 else 13  # 弱买入信号
elif crsi在上轨之上:
    dailyScore = -27 if 下降 else -13  # 弱卖出信号
```

#### MCSI-MMT评分逻辑
```python
# 评分范围: -100 到 +100
# 权重分配: 轨道位置50% + 背离信号50%
# 轨道评分逻辑:
if CSI从高轨上方跌破高轨:
    channelScore = -100  # 强卖出
elif CSI从低轨下方突破低轨:
    channelScore = 100   # 强买入
# 背离评分逻辑:
if 检测到看涨背离:
    divergenceScore = 100
elif 检测到看跌背离:
    divergenceScore = -100
```

#### MCSI-TD9/TTM评分逻辑
```python
# 评分范围: -100 到 +100
# 基于TD序列计数的特定分值映射
# TD计数达到9时产生反转信号
if TD上升序列完成(计数=9):
    td9Score = -100  # 顶部反转信号
elif TD下降序列完成(计数=9):
    td9Score = 100   # 底部反转信号
```

## 🔧 Development Notes

### Data Requirements
- Minimum 200 trading days for MA200 calculation
- Support for multiple data sources (PostgreSQL, CSV files)
- Market-aware data aggregation (handles trading hours)

### Performance Considerations
- Optimized NumPy operations for MCSI calculations
- Batch processing for large datasets
- Memory-efficient data structures for real-time updates

### Security Considerations
- Database credentials should be externalized to environment variables
- No sensitive data in configuration files
- Input validation for all API endpoints

## 📝 Development Guidelines

### Adding New Indicators
1. Implement base class in `core/indicators/`
2. Create scoring unit in `core/scoring_units/`
3. Update composite scorer configuration
4. Add web interface visualization

### Testing Strategy
- Unit tests for individual indicators
- Integration tests for composite scoring
- Performance benchmarks for Python implementations
- End-to-end web interface testing

### Code Organization
- Follow existing module structure
- Use type hints for better code documentation
- Implement proper error handling and logging
- Maintain separation between data/logic/presentation layers
