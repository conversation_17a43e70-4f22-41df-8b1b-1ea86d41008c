# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🏗️ Project Overview

This is a **technical analysis system (Analyze-system2)** for stock market trend analysis using moving averages, MCSI indicators, and composite scoring. The system consists of multiple components:

- **Core Analysis Engine**: MCSI indicators (MACD, RSI, MMT, TD9, TTM) with pure Python optimization
- **Web Interface**: Flask-based dashboard on port 5008 for trend visualization
- **Data Processing**: Hybrid data loader supporting CSV and database sources
- **Trading View Scripts**: Pine Script implementations for TradingView platform

### 🎯 MCSI技术指标体系 (Multi-Criteria System Indicator)

**MCSI是本系统的核心技术**，包含五大专业技术指标，所有指标都与Pine Script源代码保持100%一致性：

#### 1. MCSI-MACD (动量趋势指标)
- **算法参数**: EMA(19) - EMA(39), 信号线SMA(9)
- **动态阈值**: 标准差 × 1.5 的自适应阈值系统
- **评分范围**: -100 到 +100 (基于柱状图颜色变化和阈值突破)
- **核心逻辑**: 高位转向卖出信号，低位转向买入信号，相对高度加权评分

#### 2. MCSI-RSI (超买超卖指标)
- **算法参数**: CRSI计算，周期长度14，相位滞后3，扭矩0.618
- **真实周线数据**: 使用`request.security`获取真实周线CRSI数据
- **动态轨道**: 基于循环记忆的上下轨道计算
- **评分范围**: -100 到 +100 (日线70% + 周线30%权重)

#### 3. MCSI-MMT (多时间框架动量指标)
- **核心算法**: iWTT_CSI_processor + 动态轨道 + 背离检测
- **评分权重**: 轨道位置50% + 背离信号50%
- **背离检测**: 支持看涨/看跌背离和隐藏背离的完整检测
- **评分范围**: -100 到 +100

#### 4. MCSI-TD9 (时间序列指标)
- **算法基础**: Tom DeMark序列计算
- **计数逻辑**: 连续上升/下降计数，最大计数9
- **评分映射**: 基于TD计数的特定分值映射规则
- **评分范围**: -100 到 +100

#### 5. MCSI-TTM (时间到市场指标)
- **基础算法**: 基于TD9序列的TTM评分规则
- **信号生成**: 序列完成时的反转信号识别
- **评分范围**: -100 到 +100

## ⚙️ Development Commands

### Starting Services
```bash
# Start trend analysis web service (port 5008)
./start_trend_service.sh

# Stop trend analysis service
./stop_trend_service.sh

# Start main web application
cd web && python app.py
```

### Testing and Building
```bash
# Run trend analysis tests
python test_trend_analysis.py

# Note: Cython extensions have been removed from this project
# All MCSI indicators now use pure Python implementations
```

### Data Operations
```bash
# Test trend analysis with sample data
python trend_analyzer.py

# Load data using hybrid loader
python -m core.data.hybrid_data_loader
```

## 🧱 Architecture Overview

### 🏛️ 三层架构设计

**MCSI指标系统采用严格的三层架构，确保代码质量和一致性**：

#### 第一层：Pine Script源代码 (最权威参考)
```
TV-code/pine-code/
├── MCSI-macd-test.pine    # MACD指标Pine Script源代码
├── MCSI-rsi-test.pine     # RSI指标Pine Script源代码
├── MCSI-mmt-test.pine     # MMT指标Pine Script源代码
├── MCSI-td9-test.pine     # TD9指标Pine Script源代码
└── MCSI.pine              # 综合MCSI指标源代码
```

#### 第二层：Python权威实现 (100%一致性)
```
TV-code/py-code/
├── mcsi_macd.py          # MACD权威Python实现
├── mcsi_rsi.py           # RSI权威Python实现
├── mcsi_mmt.py           # MMT权威Python实现
├── mcsi_td9.py           # TD9权威Python实现
└── mcsi_ttm.py           # TTM权威Python实现
```

#### 第三层：统一接口实现 (生产环境)
```
core/
├── composite/           # Main scoring engine
│   └── scorer.py       # NewCompositeScorer - orchestrates all analysis
├── indicators/         # MCSI technical indicators (mirror of TV-code/py-code)
│   ├── mcsi_macd.py    # MACD indicator implementation
│   ├── mcsi_rsi.py     # RSI indicator implementation
│   ├── mcsi_mmt.py     # MMT indicator implementation
│   ├── mcsi_td9.py     # TD9 indicator implementation
│   └── mcsi_ttm.py     # TTM indicator implementation
├── scoring_units/      # MCSI scoring units (统一接口层)
│   ├── mcsi_macd_scoring.py   # MCSI MACD统一接口 (100% consistent)
│   ├── mcsi_mmt_scoring.py    # MCSI MMT统一接口 (100% consistent)
│   ├── mcsi_rsi_scoring.py    # MCSI RSI统一接口 (100% consistent)
│   ├── mcsi_ttm_scoring.py    # MCSI TTM统一接口 (100% consistent)
│   └── mcsi_authority_units.py # 权威版本直接调用接口
├── groups/             # Group management system
│   ├── group_manager.py       # 分组管理器
│   ├── trend_group.py         # 趋势分组
│   └── oscillation_group.py   # 震荡分组
├── data/              # Data loading and storage
│   ├── hybrid_data_loader.py  # Primary data interface
│   ├── csv_data_loader.py     # CSV file support
│   └── data_loader.py         # Database connectivity
└── config/            # Market and weight configuration
    ├── market_config.py       # Trading hours, market type detection
    └── weight_config_manager.py  # Dynamic weight management
```

### Key Design Patterns
- **Unified MCSI Interface**: All MCSI indicators accessed through individual统一接口单元 (mcsi_*_scoring.py)
- **Market Type Detection**: Automatic detection of stock/futures/crypto based on data patterns
- **Hybrid Data Loading**: Seamless switching between CSV files and database
- **Composite Scoring**: Weighted combination of multiple technical indicators
- **Pure Python Implementation**: All calculations use optimized Python code

### Web Architecture
- **Main App** (`web/app.py`): Full-featured dashboard with group management
- **Trend Service** (`trend_service.py`): Simplified trend analysis service (port 5008)
- **Templates**: Professional charting interface with real-time updates

## 📊 Configuration Management

### Database Configuration
Located in `config.py` - **Note**: Contains hardcoded credentials that should be externalized:
```python
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 5433,
    'database': 'fintech_db'
    # Credentials should be moved to environment variables
}
```

### Weight and Group Configuration
Dynamic configuration stored in `config/` JSON files:
- `group_config.json`: Trading strategy groupings
- `weight_config.json`: Indicator importance weights  
- `composite_config.json`: Overall system parameters

### Moving Average Periods
```python
MA_PERIODS = {
    'short': 5,        # 5-day (weekly trend)
    'medium': 20,      # 20-day (monthly trend) 
    'long': 50,        # 50-day (quarterly trend)
    'ultra_long': 200  # 200-day (yearly trend)
}
```

## 🎯 Scoring System

### Trend Analysis (Primary Algorithm)
- **Score Range**: -6 to +6 points
- **Comparison Matrix**: All combinations of MA5, MA20, MA50, MA200
- **Rating System**: A+ (6pts) to D (-6pts)
- **Market Detection**: Automatic stock/futures/crypto classification

### MCSI Indicators (Secondary Scoring)
- **MCSI-MACD**: Momentum and trend confluence
- **MCSI-RSI**: Oversold/overbought conditions
- **MCSI-MMT**: Multi-timeframe momentum
- **MCSI-TD9**: Tom DeMark sequential patterns
- **MCSI-TTM**: Time-to-market signals

## 🔧 Development Notes

### Data Requirements
- Minimum 200 trading days for MA200 calculation
- Support for multiple data sources (PostgreSQL, CSV files)
- Market-aware data aggregation (handles trading hours)

### Performance Considerations
- Optimized NumPy operations for MCSI calculations
- Batch processing for large datasets
- Memory-efficient data structures for real-time updates

### Security Considerations
- Database credentials should be externalized to environment variables
- No sensitive data in configuration files
- Input validation for all API endpoints

## 📝 Development Guidelines

### Adding New Indicators
1. Implement base class in `core/indicators/`
2. Create scoring unit in `core/scoring_units/`
3. Update composite scorer configuration
4. Add web interface visualization

### Testing Strategy
- Unit tests for individual indicators
- Integration tests for composite scoring
- Performance benchmarks for Python implementations
- End-to-end web interface testing

### Code Organization
- Follow existing module structure
- Use type hints for better code documentation
- Implement proper error handling and logging
- Maintain separation between data/logic/presentation layers
