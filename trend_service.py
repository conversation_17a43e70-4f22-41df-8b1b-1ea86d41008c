#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
趋势分析服务
独立的趋势分析服务，端口5008
"""

from flask import Flask, jsonify, request, render_template_string
from flask_cors import CORS
import logging
import threading
import time
from datetime import datetime
from typing import Dict, List
import importlib.util
import os
spec = importlib.util.spec_from_file_location("root_config", os.path.join(os.path.dirname(__file__), "config.py"))
root_config = importlib.util.module_from_spec(spec)
spec.loader.exec_module(root_config)
SERVICE_CONFIG = root_config.SERVICE_CONFIG
from trend_analyzer import TrendAnalyzer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

app = Flask(__name__)
CORS(app)

# 全局变量
analyzer = None
analysis_results = []
analysis_status = {
    'is_running': False,
    'progress': 0,
    'total': 0,
    'current_stock': '',
    'start_time': None,
    'end_time': None,
    'last_update': None
}

def init_analyzer():
    """初始化分析器"""
    global analyzer
    try:
        analyzer = TrendAnalyzer()
        logging.info("趋势分析器初始化成功")
        return True
    except Exception as e:
        logging.error(f"趋势分析器初始化失败: {str(e)}")
        return False

@app.route('/')
def index():
    """主页"""
    return render_template_string('''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>趋势分析系统</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
            .header { text-align: center; margin-bottom: 30px; }
            .controls { margin-bottom: 20px; text-align: center; }
            .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
            .btn-primary { background: #007bff; color: white; }
            .btn-success { background: #28a745; color: white; }
            .btn-warning { background: #ffc107; color: black; }
            .status { margin: 20px 0; padding: 15px; border-radius: 4px; }
            .status.running { background: #d4edda; border: 1px solid #c3e6cb; }
            .status.idle { background: #f8f9fa; border: 1px solid #dee2e6; }
            .progress { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; }
            .progress-bar { height: 100%; background: #007bff; transition: width 0.3s; }
            .results { margin-top: 20px; }
            .stock-item { padding: 10px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; display: flex; justify-content: space-between; align-items: center; }
            .score-positive { color: #28a745; font-weight: bold; }
            .score-negative { color: #dc3545; font-weight: bold; }
            .score-neutral { color: #6c757d; font-weight: bold; }
            .grade { padding: 2px 8px; border-radius: 12px; color: white; font-size: 12px; }
            .grade-a { background: #28a745; }
            .grade-b { background: #17a2b8; }
            .grade-c { background: #ffc107; color: black; }
            .grade-d { background: #dc3545; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔍 趋势分析系统</h1>
                <p>基于移动平均线的股票趋势流分析</p>
            </div>
            
            <div class="controls">
                <button class="btn btn-primary" onclick="startAnalysis()">开始分析</button>
                <button class="btn btn-success" onclick="refreshResults()">刷新结果</button>
                <button class="btn btn-warning" onclick="getStatistics()">查看统计</button>
            </div>
            
            <div id="status" class="status idle">
                <h3>系统状态</h3>
                <p id="statusText">就绪</p>
                <div class="progress" id="progressContainer" style="display: none;">
                    <div class="progress-bar" id="progressBar" style="width: 0%;"></div>
                </div>
                <p id="progressText"></p>
            </div>
            
            <div class="results">
                <h3>分析结果 <span id="resultCount">(0)</span></h3>
                <div id="resultsList"></div>
            </div>
        </div>
        
        <script>
            let updateInterval;
            
            function startAnalysis() {
                fetch('/api/analyze', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('分析已启动');
                            startStatusUpdate();
                        } else {
                            alert('启动失败: ' + data.error);
                        }
                    })
                    .catch(error => {
                        alert('请求失败: ' + error);
                    });
            }
            
            function refreshResults() {
                loadResults();
            }
            
            function getStatistics() {
                fetch('/api/statistics')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const stats = data.statistics;
                            alert(`统计信息:\\n总股票数: ${stats.total_stocks}\\n平均评分: ${stats.avg_score}\\n正向趋势: ${stats.positive_trend_count}\\n负向趋势: ${stats.negative_trend_count}`);
                        }
                    });
            }
            
            function startStatusUpdate() {
                updateInterval = setInterval(updateStatus, 2000);
            }
            
            function stopStatusUpdate() {
                if (updateInterval) {
                    clearInterval(updateInterval);
                }
            }
            
            function updateStatus() {
                fetch('/api/status')
                    .then(response => response.json())
                    .then(data => {
                        const status = data.status;
                        const statusDiv = document.getElementById('status');
                        const statusText = document.getElementById('statusText');
                        const progressContainer = document.getElementById('progressContainer');
                        const progressBar = document.getElementById('progressBar');
                        const progressText = document.getElementById('progressText');
                        
                        if (status.is_running) {
                            statusDiv.className = 'status running';
                            statusText.textContent = '正在分析...';
                            progressContainer.style.display = 'block';
                            
                            const progress = status.total > 0 ? (status.progress / status.total * 100) : 0;
                            progressBar.style.width = progress + '%';
                            progressText.textContent = `进度: ${status.progress}/${status.total} - 当前: ${status.current_stock}`;
                        } else {
                            statusDiv.className = 'status idle';
                            statusText.textContent = '就绪';
                            progressContainer.style.display = 'none';
                            progressText.textContent = '';
                            stopStatusUpdate();
                            loadResults();
                        }
                    });
            }
            
            function loadResults() {
                fetch('/api/results')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            displayResults(data.results);
                        }
                    });
            }
            
            function displayResults(results) {
                const resultsList = document.getElementById('resultsList');
                const resultCount = document.getElementById('resultCount');
                
                resultCount.textContent = `(${results.length})`;
                
                if (results.length === 0) {
                    resultsList.innerHTML = '<p>暂无分析结果</p>';
                    return;
                }
                
                let html = '';
                results.forEach(result => {
                    const score = result.trend_analysis.score;
                    const grade = result.trend_analysis.trend_grade;
                    const scoreClass = score > 0 ? 'score-positive' : score < 0 ? 'score-negative' : 'score-neutral';
                    const gradeClass = grade.startsWith('A') ? 'grade-a' : grade.startsWith('B') ? 'grade-b' : grade.startsWith('C') ? 'grade-c' : 'grade-d';
                    
                    html += `
                        <div class="stock-item">
                            <div>
                                <strong>${result.symbol}</strong> - ${result.name}
                                <small>(${result.category})</small>
                            </div>
                            <div>
                                <span class="grade ${gradeClass}">${grade}</span>
                                <span class="${scoreClass}">${score}/6</span>
                                <small>${result.trend_analysis.trend_description}</small>
                            </div>
                        </div>
                    `;
                });
                
                resultsList.innerHTML = html;
            }
            
            // 页面加载时获取结果
            window.onload = function() {
                loadResults();
            };
        </script>
    </body>
    </html>
    ''')

@app.route('/api/analyze', methods=['POST'])
def start_analysis():
    """启动趋势分析"""
    global analysis_status
    
    if analysis_status['is_running']:
        return jsonify({
            'success': False,
            'error': '分析正在进行中，请等待完成'
        })
    
    try:
        # 在后台线程中运行分析
        thread = threading.Thread(target=run_analysis)
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True,
            'message': '趋势分析已启动'
        })
        
    except Exception as e:
        logging.error(f"启动分析失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

def run_analysis():
    """运行分析的后台函数"""
    global analysis_results, analysis_status, analyzer
    
    try:
        analysis_status.update({
            'is_running': True,
            'progress': 0,
            'total': 0,
            'current_stock': '',
            'start_time': datetime.now(),
            'end_time': None
        })
        
        if not analyzer:
            if not init_analyzer():
                raise Exception("分析器初始化失败")
        
        # 获取股票列表
        stocks = analyzer.data_loader.get_stock_list()
        analysis_status['total'] = len(stocks)
        
        results = []
        for i, stock in enumerate(stocks, 1):
            analysis_status.update({
                'progress': i,
                'current_stock': f"{stock['symbol']}({stock['name']})"
            })
            
            result = analyzer.analyze_single_stock(stock)
            
            if result:
                results.append(result)
        
        # 按评分排序
        results.sort(key=lambda x: x['trend_analysis']['score'], reverse=True)
        analysis_results = results
        
        analysis_status.update({
            'is_running': False,
            'end_time': datetime.now(),
            'last_update': datetime.now()
        })
        
        logging.info(f"分析完成，共分析 {len(results)} 只股票")
        
    except Exception as e:
        logging.error(f"分析过程出错: {str(e)}")
        analysis_status.update({
            'is_running': False,
            'end_time': datetime.now()
        })

@app.route('/api/status')
def get_status():
    """获取分析状态"""
    return jsonify({
        'success': True,
        'status': analysis_status
    })

@app.route('/api/results')
def get_results():
    """获取分析结果"""
    return jsonify({
        'success': True,
        'results': analysis_results,
        'count': len(analysis_results)
    })

@app.route('/api/statistics')
def get_statistics():
    """获取统计信息"""
    if not analyzer or not analysis_results:
        return jsonify({
            'success': False,
            'error': '暂无分析结果'
        })
    
    try:
        stats = analyzer.get_statistics(analysis_results)
        return jsonify({
            'success': True,
            'statistics': stats
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/stock/<symbol>')
def get_stock_detail(symbol):
    """获取单只股票详情"""
    stock_result = next((r for r in analysis_results if r['symbol'] == symbol), None)
    
    if not stock_result:
        return jsonify({
            'success': False,
            'error': '股票不存在'
        })
    
    return jsonify({
        'success': True,
        'stock': stock_result
    })

if __name__ == '__main__':
    logging.info("启动趋势分析服务...")
    
    # 初始化分析器
    if init_analyzer():
        logging.info(f"趋势分析服务启动在端口 {SERVICE_CONFIG['port']}")
        app.run(
            host=SERVICE_CONFIG['host'],
            port=SERVICE_CONFIG['port'],
            debug=SERVICE_CONFIG['debug']
        )
    else:
        logging.error("服务启动失败：分析器初始化失败")
