#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开发模式启动脚本
启用调试模式和热重载
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """开发模式主函数"""
    print("🔧 开发模式启动")
    print("📊 AI市场分析系统 v2.0 (Debug Mode)")
    print("=" * 50)
    
    try:
        from web.app import app, init_scorer
        
        # 初始化评分器
        if init_scorer():
            print("✅ 评分器初始化成功")
            print("🌐 启动开发服务器...")
            print("📱 本地访问: http://127.0.0.1:50505")
            print("📱 网络访问: http://**********:50505")
            print("🔄 热重载已启用")
            print("=" * 50)

            # 启动Flask应用（调试模式）
            app.run(
                host='0.0.0.0',
                port=50505,
                debug=True,
                use_reloader=True
            )
        else:
            print("❌ 评分器初始化失败")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
