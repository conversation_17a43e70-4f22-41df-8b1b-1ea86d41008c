#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
趋势分析引擎
整合数据获取、移动平均线计算和趋势评分功能
"""

import logging
from typing import Dict, List, Optional
from datetime import datetime
from data_loader import DataLoader
from ma_calculator import MACalculator
from trend_scorer import TrendScorer

class TrendAnalyzer:
    """趋势分析引擎"""
    
    def __init__(self):
        self.data_loader = DataLoader()
        self.ma_calculator = MACalculator()
        self.trend_scorer = TrendScorer()
        self.logger = logging.getLogger(__name__)
    
    def analyze_single_stock(self, stock_info: Dict) -> Optional[Dict]:
        """分析单只股票的趋势"""
        try:
            symbol = stock_info['symbol']
            name = stock_info['name']
            table_name = stock_info['table_name']
            category = stock_info['category']

            self.logger.info(f"开始分析股票: {symbol}({name})")

            # 检查数据表是否存在
            if not self.data_loader.check_table_exists(table_name):
                self.logger.warning(f"股票 {symbol}({name}) 数据表不存在")
                return None

            # 获取股票数据
            df = self.data_loader.get_stock_data(table_name)
            if df is None or df.empty:
                self.logger.warning(f"股票 {symbol}({name}) 没有数据")
                return None

            # 检查数据量是否足够计算200日均线
            if len(df) < 200:
                self.logger.warning(f"股票 {symbol}({name}) 数据量不足({len(df)}条)，需要至少200条数据")
                return None

            # 计算移动平均线
            ma_data = self.ma_calculator.calculate_all_ma(df)
            if not ma_data:
                self.logger.warning(f"股票 {symbol}({name}) 移动平均线计算失败")
                return None

            # 计算趋势评分
            trend_result = self.trend_scorer.calculate_trend_score(ma_data)

            # 组装完整结果
            result = {
                'symbol': symbol,
                'name': name,
                'category': category,
                'table_name': table_name,
                'data_count': len(df),
                'last_update': ma_data.get('date', datetime.now()),
                'ma_data': ma_data,
                'trend_analysis': trend_result,
                'analysis_time': datetime.now()
            }

            self.logger.info(f"股票 {symbol}({name}) 分析完成，趋势评分: {trend_result['score']}/6")
            return result

        except Exception as e:
            self.logger.error(f"分析股票 {symbol}({name}) 失败: {str(e)}")
            return None
    
    def analyze_all_stocks(self) -> List[Dict]:
        """分析所有可用的股票"""
        self.logger.info("开始批量分析所有股票")

        # 获取股票列表（直接从数据库查询）
        stocks = self.data_loader.get_stock_list()
        if not stocks:
            self.logger.warning("没有找到可用的股票数据表")
            return []

        results = []
        success_count = 0
        failed_count = 0

        for i, stock in enumerate(stocks, 1):
            try:
                self.logger.info(f"进度: {i}/{len(stocks)} - 分析 {stock['symbol']}({stock['name']})")

                result = self.analyze_single_stock(stock)

                if result:
                    results.append(result)
                    success_count += 1
                else:
                    failed_count += 1

            except Exception as e:
                self.logger.error(f"分析股票 {stock['symbol']} 时出错: {str(e)}")
                failed_count += 1

        # 按趋势评分排序（从高到低）
        results.sort(key=lambda x: x['trend_analysis']['score'], reverse=True)

        self.logger.info(f"批量分析完成: 成功 {success_count} 只，失败 {failed_count} 只，总计 {len(results)} 只")
        return results
    
    def get_top_stocks(self, results: List[Dict], top_n: int = 20) -> List[Dict]:
        """获取趋势评分最高的股票"""
        if not results:
            return []
        
        # 已经按评分排序，直接取前N个
        top_stocks = results[:top_n]
        
        self.logger.info(f"获取趋势评分前 {len(top_stocks)} 只股票")
        return top_stocks
    
    def get_bottom_stocks(self, results: List[Dict], bottom_n: int = 20) -> List[Dict]:
        """获取趋势评分最低的股票"""
        if not results:
            return []
        
        # 取后N个（评分最低）
        bottom_stocks = results[-bottom_n:] if len(results) >= bottom_n else results
        
        self.logger.info(f"获取趋势评分后 {len(bottom_stocks)} 只股票")
        return bottom_stocks
    
    def filter_by_category(self, results: List[Dict], category: str) -> List[Dict]:
        """按分类筛选股票"""
        filtered = [r for r in results if r['category'] == category]
        self.logger.info(f"筛选分类 {category}: {len(filtered)} 只股票")
        return filtered
    
    def filter_by_score_range(self, results: List[Dict], min_score: int = None, max_score: int = None) -> List[Dict]:
        """按评分范围筛选股票"""
        filtered = results
        
        if min_score is not None:
            filtered = [r for r in filtered if r['trend_analysis']['score'] >= min_score]
        
        if max_score is not None:
            filtered = [r for r in filtered if r['trend_analysis']['score'] <= max_score]
        
        self.logger.info(f"按评分筛选 [{min_score}, {max_score}]: {len(filtered)} 只股票")
        return filtered
    
    def get_statistics(self, results: List[Dict]) -> Dict:
        """获取分析统计信息"""
        if not results:
            return {}
        
        scores = [r['trend_analysis']['score'] for r in results]
        categories = {}
        
        for result in results:
            category = result['category']
            if category not in categories:
                categories[category] = 0
            categories[category] += 1
        
        stats = {
            'total_stocks': len(results),
            'avg_score': round(sum(scores) / len(scores), 2),
            'max_score': max(scores),
            'min_score': min(scores),
            'positive_trend_count': len([s for s in scores if s > 0]),
            'negative_trend_count': len([s for s in scores if s < 0]),
            'neutral_count': len([s for s in scores if s == 0]),
            'categories': categories,
            'analysis_time': datetime.now()
        }
        
        return stats
    
    def close(self):
        """关闭资源"""
        if self.data_loader:
            self.data_loader.close_db()
        self.logger.info("趋势分析引擎已关闭")
