[2025-08-19 15:14:55,198] WARNING - MCSI高级组件导入失败: No module named 'core.scoring_units.mcsi_rsi_scoring_cython'
[2025-08-19 15:14:55,198] INFO - ✅ 成功加载MACD源代码实现
[2025-08-19 15:14:55,198] INFO - ✅ 成功加载RSI源代码实现
[2025-08-19 15:14:55,198] INFO - ✅ 成功加载MMT源代码实现
[2025-08-19 15:14:55,198] INFO - ✅ 成功加载TTM源代码实现
[2025-08-19 15:14:55,199] INFO - ✅ 所有MCSI统一接口直接实现可用
[2025-08-19 15:14:55,199] INFO - ✅ MCSI统一接口直接实现已导入到Web服务 (100%一致版本)
[2025-08-19 15:14:55,199] INFO - ✅ 成功加载RSI源代码实现
[2025-08-19 15:14:55,199] INFO - ✅ 成功加载MACD源代码实现
[2025-08-19 15:14:55,199] INFO - ✅ 成功加载MMT源代码实现
[2025-08-19 15:14:55,199] INFO - ✅ 成功加载TTM源代码实现
[2025-08-19 15:14:55,199] INFO - 🎉 所有MCSI统一接口单元已成功加载并验证可用
[2025-08-19 15:14:55,202] INFO - 权重配置已从 config/weight_config.json 加载
[2025-08-19 15:14:55,202] INFO - 权重配置已从 config/weight_config.json 加载
[2025-08-19 15:14:55,202] INFO - 配置已从 config/group_config.json 加载
[2025-08-19 15:14:55,202] INFO - 添加计分单元 trend_unit 到分组 trend_group，权重: 1.0
[2025-08-19 15:14:55,202] INFO - 已添加默认趋势计分单元
[2025-08-19 15:14:55,202] INFO - 添加分组: trend_group (权重: 0.7)
[2025-08-19 15:14:55,202] INFO - 添加计分单元 rsi_unit 到分组 oscillation_group，权重: 1.0
[2025-08-19 15:14:55,202] INFO - 添加计分单元 macd_unit 到分组 oscillation_group，权重: 0.8
[2025-08-19 15:14:55,202] INFO - 添加计分单元 wave_unit 到分组 oscillation_group，权重: 0.6
[2025-08-19 15:14:55,202] WARNING - ⚠️ MCSI高级组件不可用，跳过高级单元加载
[2025-08-19 15:14:55,202] INFO - 已添加默认震荡计分单元（包含高级MCSI指标）
[2025-08-19 15:14:55,202] INFO - 添加分组: oscillation_group (权重: 0.3)
[2025-08-19 15:14:55,202] INFO - 默认分组初始化完成
[2025-08-19 15:14:55,202] INFO - 分析结果文件不存在，将进行首次分析
[2025-08-19 15:14:55,202] INFO - 没有找到已保存的分析结果
[2025-08-19 15:14:55,202] INFO - 历史记录文件不存在，从空记录开始
[2025-08-19 15:14:55,202] INFO - 分析结果文件不存在，将进行首次分析
[2025-08-19 15:14:55,202] INFO - 没有找到已保存的分析结果
[2025-08-19 15:14:55,222] INFO - 数据库连接成功（SQLAlchemy引擎）
[2025-08-19 15:14:55,222] INFO - 数据库连接成功，将优先使用数据库数据源
[2025-08-19 15:14:55,222] INFO - CSV数据加载器初始化成功
[2025-08-19 15:14:55,222] INFO - 已设置混合数据加载器
[2025-08-19 15:14:55,222] INFO - 混合数据加载器初始化成功，可用数据源: ['database', 'csv']
[2025-08-19 15:14:55,222] INFO - 数据库可用: True, CSV可用: True
[2025-08-19 15:14:55,222] INFO - ✅ 评分器初始化成功
[2025-08-19 15:14:55,225] INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:50505
 * Running on http://**********:50505
[2025-08-19 15:14:55,225] INFO - [33mPress CTRL+C to quit[0m
[2025-08-19 15:35:15,927] INFO - ✅ 成功加载MACD源代码实现
[2025-08-19 15:35:15,927] INFO - ✅ 成功加载RSI源代码实现
[2025-08-19 15:35:15,927] INFO - ✅ 成功加载MMT源代码实现
[2025-08-19 15:35:15,927] INFO - ✅ 成功加载TTM源代码实现
[2025-08-19 15:35:15,927] INFO - ✅ 所有MCSI统一接口直接实现可用
[2025-08-19 15:35:15,927] INFO - ✅ MCSI统一接口直接实现已导入到Web服务 (100%一致版本)
[2025-08-19 15:35:15,927] INFO - ✅ 成功加载RSI源代码实现
[2025-08-19 15:35:15,927] INFO - ✅ 成功加载MACD源代码实现
[2025-08-19 15:35:15,927] INFO - ✅ 成功加载MMT源代码实现
[2025-08-19 15:35:15,927] INFO - ✅ 成功加载TTM源代码实现
[2025-08-19 15:35:15,927] INFO - 🎉 所有MCSI统一接口单元已成功加载并验证可用
[2025-08-19 15:35:15,930] INFO - 权重配置已从 config/weight_config.json 加载
[2025-08-19 15:35:15,930] INFO - 权重配置已从 config/weight_config.json 加载
[2025-08-19 15:35:15,930] INFO - 配置已从 config/group_config.json 加载
[2025-08-19 15:35:15,930] INFO - 添加计分单元 trend_unit 到分组 trend_group，权重: 1.0
[2025-08-19 15:35:15,930] INFO - 已添加默认趋势计分单元
[2025-08-19 15:35:15,931] INFO - 添加分组: trend_group (权重: 0.7)
[2025-08-19 15:35:15,931] INFO - 添加计分单元 rsi_unit 到分组 oscillation_group，权重: 1.0
[2025-08-19 15:35:15,931] INFO - 添加计分单元 macd_unit 到分组 oscillation_group，权重: 0.8
[2025-08-19 15:35:15,931] INFO - 添加计分单元 wave_unit 到分组 oscillation_group，权重: 0.6
[2025-08-19 15:35:15,931] WARNING - ⚠️ 无法导入MCSI高级计分单元: No module named 'core.scoring_units.mcsi_premium_units'
[2025-08-19 15:35:15,931] INFO - 已添加默认震荡计分单元（包含高级MCSI指标）
[2025-08-19 15:35:15,931] INFO - 添加分组: oscillation_group (权重: 0.3)
[2025-08-19 15:35:15,931] INFO - 默认分组初始化完成
[2025-08-19 15:35:15,931] INFO - 分析结果文件不存在，将进行首次分析
[2025-08-19 15:35:15,931] INFO - 没有找到已保存的分析结果
[2025-08-19 15:35:15,931] INFO - 历史记录文件不存在，从空记录开始
[2025-08-19 15:35:15,931] INFO - 分析结果文件不存在，将进行首次分析
[2025-08-19 15:35:15,931] INFO - 没有找到已保存的分析结果
[2025-08-19 15:35:15,950] INFO - 数据库连接成功（SQLAlchemy引擎）
[2025-08-19 15:35:15,950] INFO - 数据库连接成功，将优先使用数据库数据源
[2025-08-19 15:35:15,950] INFO - CSV数据加载器初始化成功
[2025-08-19 15:35:15,950] INFO - 已设置混合数据加载器
[2025-08-19 15:35:15,950] INFO - 混合数据加载器初始化成功，可用数据源: ['database', 'csv']
[2025-08-19 15:35:15,950] INFO - 数据库可用: True, CSV可用: True
[2025-08-19 15:35:15,950] INFO - ✅ 评分器初始化成功
[2025-08-19 15:35:15,953] INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:50505
 * Running on http://**********:50505
[2025-08-19 15:35:15,953] INFO - [33mPress CTRL+C to quit[0m
