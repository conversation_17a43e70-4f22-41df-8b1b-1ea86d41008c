Traceback (most recent call last):
  File "/home/<USER>/Analyze-system2/trend_service.py", line 21, in <module>
    from trend_analyzer import TrendAnalyzer
  File "/home/<USER>/Analyze-system2/trend_analyzer.py", line 11, in <module>
    from data_loader import DataLoader
  File "/home/<USER>/Analyze-system2/data_loader.py", line 12, in <module>
    from config import DB_CONFIG
ImportError: cannot import name 'DB_CONFIG' from 'config' (/home/<USER>/Analyze-system2/config/__init__.py)
