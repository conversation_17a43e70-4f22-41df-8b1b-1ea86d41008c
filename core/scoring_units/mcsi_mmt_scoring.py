#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCSI MMT评分单元 - 纯Python实现，与源代码100%一致
基于TV-code/py-code/mcsi_mmt.py的完整实现
"""

import sys
import numpy as np
import pandas as pd
from typing import List, Optional, Dict, Any, Union
import logging
from pathlib import Path

# 添加源代码路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent / 'TV-code' / 'py-code'))

from .base_scoring_unit import BaseScoringUnit, ScoringResult

class MCSIMMTScoringUnit(BaseScoringUnit):
    """MCSI MMT评分单元 - 基于源代码实现，确保与Pine Script 100%一致"""
    
    def __init__(self, cyclic_memory=34, leveling=10.0):
        """初始化MCSI MMT评分单元"""
        super().__init__(
            unit_id='mcsi_mmt',
            name='MCSI MMT评分单元',
            description='基于源代码TV-code/py-code/mcsi_mmt.py的MMT评分，与Pine Script 100%一致',
            min_score=-100.0,
            max_score=100.0,
            enabled=True
        )
        
        # MMT参数
        self.cyclic_memory = cyclic_memory
        self.leveling = leveling
        
        # 导入源代码实现
        try:
            from mcsi_mmt import MCSIMMTIndicator
            self.source_indicator = MCSIMMTIndicator(
                cyclic_memory=cyclic_memory,
                leveling=leveling
            )
            self.logger.info("✅ 成功加载MMT源代码实现")
            self.source_available = True
        except ImportError as e:
            self.logger.error(f"❌ 无法导入MMT源代码: {e}")
            self.source_indicator = None
            self.source_available = False

    def calculate_score(self, 
                       data: Union[pd.DataFrame, Dict] = None,
                       db_conn: Optional[object] = None,
                       symbol: Optional[str] = None, 
                       start_date: Optional[str] = None,
                       end_date: Optional[str] = None,
                       ohlc: Optional[pd.DataFrame] = None,
                       period: str = 'daily',
                       seasonal_factors: Optional[Dict] = None) -> ScoringResult:
        """统一接口的MCSI MMT评分计算"""
        try:
            if not self.source_available:
                return ScoringResult(
                    score=0.0,
                    signal='neutral',
                    confidence=0.0,
                    description='MMT源代码实现不可用',
                    metadata={'error': 'source_unavailable'}
                )
            
            # 数据获取优先级：data > ohlc > db_conn
            if data is not None and isinstance(data, pd.DataFrame):
                input_data = data
            elif ohlc is not None and isinstance(ohlc, pd.DataFrame):
                input_data = ohlc
            elif db_conn is not None and symbol is not None:
                input_data = self._get_data_from_db(db_conn, symbol, start_date, end_date)
            else:
                return ScoringResult(
                    score=0.0,
                    signal='neutral',
                    confidence=0.0,
                    description='缺少有效的数据输入',
                    metadata={'error': 'no_data'}
                )
            
            # 验证数据
            if not self.validate_data(input_data):
                return ScoringResult(
                    score=0.0,
                    signal='neutral',
                    confidence=0.0,
                    description='数据验证失败',
                    metadata={'error': 'validation_failed'}
                )
            
            # 调用源代码计算（MMT需要HLC数据）
            result = self.source_indicator.calculate(
                input_data['close'].values,
                input_data['high'].values,
                input_data['low'].values
            )
            mmt_scores = result.get('mmt_score', [])
            
            if len(mmt_scores) == 0:
                return ScoringResult(
                    score=0.0,
                    signal='neutral',
                    confidence=0.0,
                    description='MMT计算返回空结果',
                    metadata={'error': 'empty_result'}
                )
            
            # 获取最新分数
            latest_score = float(mmt_scores[-1])
            
            # 确保分数在有效范围内
            latest_score = max(-100.0, min(100.0, latest_score))
            
            # 确定信号类型
            if latest_score >= 85:
                signal = 'strong_bullish'
            elif latest_score >= 50:
                signal = 'bullish'
            elif latest_score >= 10:
                signal = 'weak_bullish'
            elif latest_score <= -85:
                signal = 'strong_bearish'
            elif latest_score <= -50:
                signal = 'bearish'
            elif latest_score <= -10:
                signal = 'weak_bearish'
            else:
                signal = 'neutral'
            
            # 计算置信度
            confidence = min(abs(latest_score) / 100.0, 1.0)
            
            # 生成描述
            description = f'MCSI MMT分析: {latest_score:.2f}'
            
            # 准备元数据
            metadata = {
                'indicator': 'MMT',
                'period': period,
                'final_score': latest_score,
                'score_series': [float(x) for x in mmt_scores],
                'parameters': {
                    'cyclic_memory': self.cyclic_memory,
                    'leveling': self.leveling
                },
                'data_points': len(input_data),
                'source': 'TV-code/py-code/mcsi_mmt.py'
            }
            
            return ScoringResult(
                score=latest_score,
                raw_value=latest_score,
                signal=signal,
                confidence=confidence,
                description=description,
                metadata=metadata
            )
            
        except Exception as e:
            self.logger.error(f"MCSI MMT评分计算失败: {str(e)}")
            return ScoringResult(
                score=0.0,
                signal='neutral',
                confidence=0.0,
                description=f'计算错误: {str(e)}',
                metadata={'error': str(e)}
            )

    def _get_data_from_db(self, db_conn, symbol, start_date, end_date):
        """从数据库获取数据（暂时使用模拟数据）"""
        self.logger.warning("使用模拟数据库数据，请实现真实的数据库查询逻辑")
        
        # 生成模拟数据
        np.random.seed(hash(symbol) % 2**32)
        length = 100
        base_price = 100.0
        
        close_prices = base_price + np.cumsum(np.random.normal(0, 0.02, length))
        
        return pd.DataFrame({
            'open': close_prices * (1 + np.random.uniform(-0.01, 0.01, length)),
            'high': close_prices * (1 + np.random.uniform(0.005, 0.02, length)),
            'low': close_prices * (1 + np.random.uniform(-0.02, -0.005, length)),
            'close': close_prices,
            'volume': np.random.randint(1000, 10000, length)
        })

    def validate_data(self, data: pd.DataFrame) -> bool:
        """验证输入数据"""
        try:
            # 检查基本结构
            if not isinstance(data, pd.DataFrame):
                self.logger.warning("数据不是DataFrame格式")
                return False
            
            # 检查必需列（MMT需要HLC数据）
            required_columns = ['high', 'low', 'close']
            for col in required_columns:
                if col not in data.columns:
                    self.logger.warning(f"缺少必需列: {col}")
                    return False
            
            # 检查数据长度
            min_length = self.cyclic_memory + 20
            if len(data) < min_length:
                self.logger.warning(f"数据长度不足: 需要{min_length}行，实际{len(data)}行")
                return False
            
            # 检查数据质量
            for col in required_columns:
                if data[col].isnull().any():
                    self.logger.warning(f"{col}包含空值")
                    return False
                
                if (data[col] <= 0).any():
                    self.logger.warning(f"{col}包含非正值")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据验证异常: {e}")
            return False

    def get_required_columns(self) -> List[str]:
        """获取必需的数据列"""
        return ['high', 'low', 'close']
    
    def get_min_data_points(self) -> int:
        """获取最小数据点数"""
        return self.cyclic_memory + 20
