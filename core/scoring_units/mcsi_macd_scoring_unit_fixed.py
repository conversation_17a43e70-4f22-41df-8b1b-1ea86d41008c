#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版MCSI MACD计分单元
基于接口分析结果的正确实现
"""

import pandas as pd
import numpy as np
import sys
import os
from typing import List
import logging

# 添加TV-code路径
tv_code_path = os.path.join(os.path.dirname(__file__), '../../TV-code/py-code')
if tv_code_path not in sys.path:
    sys.path.insert(0, tv_code_path)

try:
    from .base_scoring_unit import BaseScoringUnit, ScoringResult
except ImportError:
    from base_scoring_unit import BaseScoringUnit, ScoringResult


class MCSIMACDScoringUnit(BaseScoringUnit):
    """
    修复版MCSI MACD计分单元
    
    基于接口分析的正确实现：
    - 使用numpy.ndarray作为输入
    - 处理NaN值和数据长度问题
    - 正确映射分数范围
    """
    
    def __init__(self, 
                 unit_id: str = 'mcsi_macd_unit',
                 name: str = 'MCSI MACD计分单元',
                 description: str = '基于MCSI MACD指标的增强版计分单元',
                 fast_length: int = 19,
                 slow_length: int = 39,
                 signal_length: int = 9,
                 lookback_period: int = 20):
        """
        初始化MCSI MACD计分单元
        """
        super().__init__(
            unit_id=unit_id,
            name=name,
            description=description,
            min_score=-10.0,
            max_score=10.0
        )
        
        # MCSI MACD参数
        self.fast_length = fast_length
        self.slow_length = slow_length
        self.signal_length = signal_length
        self.lookback_period = lookback_period
        
        # 延迟导入MCSI指标
        self._indicator = None
        
        # 更新配置
        self.config = {
            'fast_length': fast_length,
            'slow_length': slow_length,
            'signal_length': signal_length,
            'lookback_period': lookback_period
        }
    
    def _get_indicator(self):
        """延迟初始化MCSI指标"""
        if self._indicator is None:
            try:
                from mcsi_macd import MCSIMACDIndicator
                self._indicator = MCSIMACDIndicator(
                    fast_length=self.fast_length,
                    slow_length=self.slow_length,
                    signal_length=self.signal_length,
                    lookback_period=self.lookback_period
                )
            except ImportError as e:
                self.logger.error(f"无法导入MCSI MACD指标: {e}")
                self._indicator = None
        return self._indicator
    
    def calculate_score(self, data: pd.DataFrame) -> ScoringResult:
        """
        计算MCSI MACD分数
        """
        try:
            # 获取指标实例
            indicator = self._get_indicator()
            if indicator is None:
                return self._create_fallback_result("MCSI MACD指标导入失败")
            
            # 准备数据：转换为numpy数组
            close_prices = data['close'].values.astype(np.float64)
            
            # 检查数据长度
            min_required = max(self.slow_length, self.lookback_period) + self.signal_length + 10
            if len(close_prices) < min_required:
                return self._create_fallback_result(f"数据长度不足，需要{min_required}，实际{len(close_prices)}")
            
            # 调用MCSI MACD指标
            mcsi_result = indicator.calculate(close_prices)
            
            # 获取最新的MACD分数
            macd_scores = mcsi_result.get('macd_score', np.array([0.0]))
            if len(macd_scores) == 0:
                latest_score = 0.0
            else:
                latest_score = macd_scores[-1]
                # 处理NaN值
                if np.isnan(latest_score) or np.isinf(latest_score):
                    latest_score = 0.0
            
            # 将-100到+100的分数映射到-10到+10
            normalized_score = np.clip(latest_score / 10.0, -10.0, 10.0)
            
            # 确定信号类型
            if latest_score >= 50:
                signal = 'strong_bullish'
            elif latest_score >= 25:
                signal = 'bullish'
            elif latest_score > 0:
                signal = 'weak_bullish'
            elif latest_score <= -50:
                signal = 'strong_bearish'
            elif latest_score <= -25:
                signal = 'bearish'
            elif latest_score < 0:
                signal = 'weak_bearish'
            else:
                signal = 'neutral'
            
            # 计算置信度
            confidence = min(1.0, abs(latest_score) / 100.0)
            
            # 获取其他指标值
            histogram = mcsi_result.get('histogram', np.array([0.0]))[-1] if len(mcsi_result.get('histogram', [])) > 0 else 0.0
            dynamic_threshold = mcsi_result.get('dynamic_threshold', np.array([0.0]))[-1] if len(mcsi_result.get('dynamic_threshold', [])) > 0 else 0.0
            
            # 处理NaN值
            if np.isnan(histogram) or np.isinf(histogram):
                histogram = 0.0
            if np.isnan(dynamic_threshold) or np.isinf(dynamic_threshold):
                dynamic_threshold = 0.0
            
            # 生成描述
            description = f"MCSI MACD分析({normalized_score:.2f}): 柱状图{histogram:.4f}, 动态阈值{dynamic_threshold:.4f}"
            
            return ScoringResult(
                score=float(normalized_score),
                raw_value=float(latest_score),
                signal=signal,
                confidence=float(confidence),
                description=description,
                metadata={
                    'macd': float(mcsi_result.get('macd', np.array([0.0]))[-1] if len(mcsi_result.get('macd', [])) > 0 else 0.0),
                    'signal_line': float(mcsi_result.get('signal', np.array([0.0]))[-1] if len(mcsi_result.get('signal', [])) > 0 else 0.0),
                    'histogram': float(histogram),
                    'dynamic_threshold': float(dynamic_threshold),
                    'original_score': float(latest_score),
                    'fast_length': self.fast_length,
                    'slow_length': self.slow_length,
                    'signal_length': self.signal_length,
                    'lookback_period': self.lookback_period
                }
            )
            
        except Exception as e:
            self.logger.error(f"MCSI MACD计分计算失败: {str(e)}")
            return self._create_fallback_result(f"计算错误: {str(e)}")
    
    def _create_fallback_result(self, error_msg: str) -> ScoringResult:
        """创建降级结果"""
        return ScoringResult(
            score=0.0,
            signal='neutral',
            confidence=0.0,
            description=f'MCSI MACD计分单元: {error_msg}',
            metadata={'error': error_msg}
        )
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """验证输入数据是否有效"""
        try:
            # 检查必需的列
            required_columns = self.get_required_columns()
            if not all(col in data.columns for col in required_columns):
                return False
            
            # 检查数据长度
            if len(data) < self.get_min_data_points():
                return False
            
            # 检查数据是否包含有效值
            if data['close'].isna().all():
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {str(e)}")
            return False
    
    def get_required_columns(self) -> List[str]:
        """获取计算所需的数据列"""
        return ['close']
    
    def get_min_data_points(self) -> int:
        """获取计算所需的最少数据点数量"""
        return max(self.slow_length, self.lookback_period) + self.signal_length + 10
