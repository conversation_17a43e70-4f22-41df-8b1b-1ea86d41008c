#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCSI TD9计分单元
基于MCSI TD9指标的TD序列计分单元
"""

import pandas as pd
import numpy as np
from typing import List
import logging

from .base_scoring_unit import BaseScoringUnit, ScoringResult
from core.indicators.mcsi_td9_indicator import MCSITD9Indicator


class MCSITD9ScoringUnit(BaseScoringUnit):
    """
    MCSI TD9计分单元
    
    基于MCSI TD9指标的TD序列计分：
    - 识别价格序列模式
    - 支持TD计数评分系统
    - 提供-100到+100的原始分数，映射到-8到+8
    """
    
    def __init__(self, 
                 unit_id: str = 'mcsi_td9_unit',
                 name: str = 'MCSI TD9计分单元',
                 description: str = '基于MCSI TD9指标的TD序列计分单元',
                 comparison_period: int = 4):
        """
        初始化MCSI TD9计分单元
        
        Args:
            unit_id: 唯一标识符
            name: 计分单元名称
            description: 描述信息
            comparison_period: 比较周期，默认4
        """
        super().__init__(
            unit_id=unit_id,
            name=name,
            description=description,
            min_score=-8.0,
            max_score=8.0
        )
        
        # MCSI TD9参数
        self.comparison_period = comparison_period
        
        # 初始化MCSI TD9指标
        self.mcsi_td9_indicator = MCSITD9Indicator(
            comparison_period=comparison_period
        )
        
        # 更新配置
        self.config = {
            'comparison_period': comparison_period
        }
    
    def calculate_score(self, data: pd.DataFrame) -> ScoringResult:
        """
        计算MCSI TD9分数
        
        Args:
            data: 股票数据DataFrame，包含close字段
            
        Returns:
            ScoringResult: 计分结果对象
        """
        try:
            # 获取收盘价
            close_prices = data['close'].values
            
            # 计算MCSI TD9指标
            td9_result = self.mcsi_td9_indicator.calculate(close_prices)
            
            # 获取最新的TD9分数
            td9_scores = td9_result['td9_score']
            latest_score = td9_scores[-1] if len(td9_scores) > 0 else 0.0
            
            # 将-100到+100的分数映射到-8到+8
            normalized_score = latest_score * 8.0 / 100.0
            
            # 获取TD计数
            td_up_count = td9_result['td_up_count'][-1] if len(td9_result['td_up_count']) > 0 else 0
            td_down_count = td9_result['td_down_count'][-1] if len(td9_result['td_down_count']) > 0 else 0
            
            # 确定信号类型
            if latest_score >= 80:
                signal = 'strong_bullish'
            elif latest_score >= 50:
                signal = 'bullish'
            elif latest_score > 0:
                signal = 'weak_bullish'
            elif latest_score <= -80:
                signal = 'strong_bearish'
            elif latest_score <= -50:
                signal = 'bearish'
            elif latest_score < 0:
                signal = 'weak_bearish'
            else:
                signal = 'neutral'
            
            # 计算置信度
            confidence = min(1.0, abs(latest_score) / 100.0)
            
            # 生成描述
            if td_down_count > 0:
                description = f"MCSI TD9分析({normalized_score:.2f}): TD下降计数{td_down_count} (买入信号)"
            elif td_up_count > 0:
                description = f"MCSI TD9分析({normalized_score:.2f}): TD上升计数{td_up_count} (卖出信号)"
            else:
                description = f"MCSI TD9分析({normalized_score:.2f}): 无明显TD序列信号"
            
            return ScoringResult(
                score=normalized_score,
                raw_value=latest_score,
                signal=signal,
                confidence=confidence,
                description=description,
                metadata={
                    'td_up_count': td_up_count,
                    'td_down_count': td_down_count,
                    'original_score': latest_score,
                    'comparison_period': self.comparison_period
                }
            )
            
        except Exception as e:
            self.logger.error(f"MCSI TD9计分计算失败: {str(e)}")
            return ScoringResult(
                score=0.0,
                signal='neutral',
                description=f'计算错误: {str(e)}'
            )
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """
        验证输入数据是否有效
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            bool: 数据是否有效
        """
        try:
            # 检查必需的列
            required_columns = self.get_required_columns()
            if not all(col in data.columns for col in required_columns):
                return False
            
            # 检查数据长度
            if len(data) < self.get_min_data_points():
                return False
            
            # 检查数据是否包含有效值
            if data['close'].isna().all():
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {str(e)}")
            return False
    
    def get_required_columns(self) -> List[str]:
        """获取计算所需的数据列"""
        return ['close']
    
    def get_min_data_points(self) -> int:
        """获取计算所需的最少数据点数量"""
        return self.comparison_period + 20  # 需要足够的数据进行TD序列计算
