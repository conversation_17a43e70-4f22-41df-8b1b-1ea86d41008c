#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCSI RSI计分单元
基于MCSI RSI指标的增强版RSI计分单元
"""

import pandas as pd
import numpy as np
from typing import List
import logging

from .base_scoring_unit import BaseScoringUnit, ScoringResult
from core.indicators.mcsi_rsi_indicator import MCSIRSIIndicator


class MCSIRSIScoringUnit(BaseScoringUnit):
    """
    MCSI RSI计分单元
    
    基于MCSI RSI指标的增强版RSI计分：
    - 支持日线和周线双时间框架分析
    - 使用动态轨道判断超买超卖
    - 提供-100到+100的原始分数，映射到-10到+10
    """
    
    def __init__(self, 
                 unit_id: str = 'mcsi_rsi_unit',
                 name: str = 'MCSI RSI计分单元',
                 description: str = '基于MCSI RSI指标的增强版计分单元',
                 dom_cycle: int = 14,
                 vibration: int = 10,
                 leveling: float = 10.0):
        """
        初始化MCSI RSI计分单元
        
        Args:
            unit_id: 唯一标识符
            name: 计分单元名称
            description: 描述信息
            dom_cycle: 主导周期长度，默认14
            vibration: 振动参数，默认10
            leveling: 水平化参数，默认10.0
        """
        super().__init__(
            unit_id=unit_id,
            name=name,
            description=description,
            min_score=-10.0,
            max_score=10.0
        )
        
        # MCSI RSI参数
        self.dom_cycle = dom_cycle
        self.vibration = vibration
        self.leveling = leveling
        
        # 初始化MCSI RSI指标
        self.mcsi_rsi_indicator = MCSIRSIIndicator(
            dom_cycle=dom_cycle,
            vibration=vibration,
            leveling=leveling
        )
        
        # 更新配置
        self.config = {
            'dom_cycle': dom_cycle,
            'vibration': vibration,
            'leveling': leveling
        }
    
    def calculate_score(self, data: pd.DataFrame) -> ScoringResult:
        """
        计算MCSI RSI分数
        
        Args:
            data: 股票数据DataFrame，包含OHLC字段
            
        Returns:
            ScoringResult: 计分结果对象
        """
        try:
            # 准备数据字典
            data_dict = {
                'close': data['close'].values,
                'open': data['open'].values,
                'high': data['high'].values,
                'low': data['low'].values,
                'volume': data.get('volume', [1] * len(data)).values,
                'date': data.index.tolist() if hasattr(data.index, 'tolist') else list(range(len(data)))
            }
            
            # 计算MCSI RSI指标
            rsi_result = self.mcsi_rsi_indicator.calculate(data_dict)
            
            # 获取最新的RSI分数
            rsi_scores = rsi_result['rsi_score']
            latest_score = rsi_scores[-1] if len(rsi_scores) > 0 else 0.0
            
            # 将-100到+100的分数映射到-10到+10
            normalized_score = latest_score / 10.0
            
            # 获取最新的信号状态
            daily_signals = rsi_result['daily_signals']
            weekly_signals = rsi_result['weekly_signals']
            latest_daily_signal = daily_signals[-1] if len(daily_signals) > 0 else 'neutral'
            latest_weekly_signal = weekly_signals[-1] if len(weekly_signals) > 0 else 'neutral'
            
            # 确定信号类型
            if latest_score >= 67:
                signal = 'strong_bullish'
            elif latest_score >= 33:
                signal = 'bullish'
            elif latest_score > 0:
                signal = 'weak_bullish'
            elif latest_score <= -67:
                signal = 'strong_bearish'
            elif latest_score <= -33:
                signal = 'bearish'
            elif latest_score < 0:
                signal = 'weak_bearish'
            else:
                signal = 'neutral'
            
            # 计算置信度
            confidence = min(1.0, abs(latest_score) / 100.0)
            
            # 获取CRSI值
            crsi_daily = rsi_result['crsi_daily'][-1] if len(rsi_result['crsi_daily']) > 0 else 50.0
            crsi_weekly = rsi_result['crsi_weekly'][-1] if len(rsi_result['crsi_weekly']) > 0 else 50.0
            
            # 生成描述
            description = f"MCSI RSI分析({normalized_score:.2f}): 日线{latest_daily_signal}, 周线{latest_weekly_signal}"
            
            return ScoringResult(
                score=normalized_score,
                raw_value=latest_score,
                signal=signal,
                confidence=confidence,
                description=description,
                metadata={
                    'crsi_daily': crsi_daily,
                    'crsi_weekly': crsi_weekly,
                    'daily_signal': latest_daily_signal,
                    'weekly_signal': latest_weekly_signal,
                    'original_score': latest_score,
                    'dom_cycle': self.dom_cycle,
                    'vibration': self.vibration,
                    'leveling': self.leveling
                }
            )
            
        except Exception as e:
            self.logger.error(f"MCSI RSI计分计算失败: {str(e)}")
            return ScoringResult(
                score=0.0,
                signal='neutral',
                description=f'计算错误: {str(e)}'
            )
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """
        验证输入数据是否有效
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            bool: 数据是否有效
        """
        try:
            # 检查必需的列
            required_columns = self.get_required_columns()
            if not all(col in data.columns for col in required_columns):
                return False
            
            # 检查数据长度
            if len(data) < self.get_min_data_points():
                return False
            
            # 检查数据是否包含有效值
            for col in required_columns:
                if data[col].isna().all():
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {str(e)}")
            return False
    
    def get_required_columns(self) -> List[str]:
        """获取计算所需的数据列"""
        return ['close', 'open', 'high', 'low']
    
    def get_min_data_points(self) -> int:
        """获取计算所需的最少数据点数量"""
        return max(self.dom_cycle * 2, 50) + 20  # 需要足够的数据进行双时间框架分析
