#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCSI MMT计分单元
基于MCSI MMT指标的多重复合动量计分单元
"""

import pandas as pd
import numpy as np
from typing import List
import logging

from .base_scoring_unit import BaseScoringUnit, ScoringResult
from core.indicators.mcsi_mmt_indicator import MCSIMMTIndicator


class MCSIMMTScoringUnit(BaseScoringUnit):
    """
    MCSI MMT计分单元
    
    基于MCSI MMT指标的多重复合动量计分：
    - 结合轨道分析和背离检测
    - 支持动态轨道突破信号
    - 提供-100到+100的原始分数，映射到-10到+10
    """
    
    def __init__(self, 
                 unit_id: str = 'mcsi_mmt_unit',
                 name: str = 'MCSI MMT计分单元',
                 description: str = '基于MCSI MMT指标的多重复合动量计分单元',
                 leveling: int = 10,
                 cyclic_memory: int = 34,
                 channel_weight: float = 0.5,
                 divergence_weight: float = 0.5):
        """
        初始化MCSI MMT计分单元
        
        Args:
            unit_id: 唯一标识符
            name: 计分单元名称
            description: 描述信息
            leveling: 水平化参数，默认10
            cyclic_memory: 循环记忆周期，默认34
            channel_weight: 轨道分数权重，默认0.5
            divergence_weight: 背离分数权重，默认0.5
        """
        super().__init__(
            unit_id=unit_id,
            name=name,
            description=description,
            min_score=-10.0,
            max_score=10.0
        )
        
        # MCSI MMT参数
        self.leveling = leveling
        self.cyclic_memory = cyclic_memory
        self.channel_weight = channel_weight
        self.divergence_weight = divergence_weight
        
        # 初始化MCSI MMT指标
        self.mcsi_mmt_indicator = MCSIMMTIndicator(
            leveling=leveling,
            cyclic_memory=cyclic_memory,
            channel_weight=channel_weight,
            divergence_weight=divergence_weight
        )
        
        # 更新配置
        self.config = {
            'leveling': leveling,
            'cyclic_memory': cyclic_memory,
            'channel_weight': channel_weight,
            'divergence_weight': divergence_weight
        }
    
    def calculate_score(self, data: pd.DataFrame) -> ScoringResult:
        """
        计算MCSI MMT分数
        
        Args:
            data: 股票数据DataFrame，包含close, high, low字段
            
        Returns:
            ScoringResult: 计分结果对象
        """
        try:
            # 获取价格数据
            close_prices = data['close'].values
            high_prices = data['high'].values
            low_prices = data['low'].values
            
            # 计算MCSI MMT指标
            mmt_result = self.mcsi_mmt_indicator.calculate(close_prices, high_prices, low_prices)
            
            # 获取最新的MMT分数
            mmt_scores = mmt_result['mmt_score']
            latest_score = mmt_scores[-1] if len(mmt_scores) > 0 else 0.0
            
            # 将-100到+100的分数映射到-10到+10
            normalized_score = latest_score / 10.0
            
            # 获取分量分数
            channel_scores = mmt_result['channel_score']
            divergence_scores = mmt_result['divergence_score']
            latest_channel_score = channel_scores[-1] if len(channel_scores) > 0 else 0.0
            latest_divergence_score = divergence_scores[-1] if len(divergence_scores) > 0 else 0.0
            
            # 确定信号类型
            if latest_score >= 80:
                signal = 'strong_bullish'
            elif latest_score >= 40:
                signal = 'bullish'
            elif latest_score > 0:
                signal = 'weak_bullish'
            elif latest_score <= -80:
                signal = 'strong_bearish'
            elif latest_score <= -40:
                signal = 'bearish'
            elif latest_score < 0:
                signal = 'weak_bearish'
            else:
                signal = 'neutral'
            
            # 计算置信度
            confidence = min(1.0, abs(latest_score) / 100.0)
            
            # 检查背离信号
            bull_div = mmt_result['bull_div'][-1] if len(mmt_result['bull_div']) > 0 else False
            bear_div = mmt_result['bear_div'][-1] if len(mmt_result['bear_div']) > 0 else False
            hidden_bull_div = mmt_result['hidden_bull_div'][-1] if len(mmt_result['hidden_bull_div']) > 0 else False
            hidden_bear_div = mmt_result['hidden_bear_div'][-1] if len(mmt_result['hidden_bear_div']) > 0 else False
            
            # 生成描述
            description = f"MCSI MMT分析({normalized_score:.2f}): 轨道{latest_channel_score:.0f}, 背离{latest_divergence_score:.0f}"
            if bull_div or hidden_bull_div:
                description += ", 看涨背离"
            elif bear_div or hidden_bear_div:
                description += ", 看跌背离"
            
            return ScoringResult(
                score=normalized_score,
                raw_value=latest_score,
                signal=signal,
                confidence=confidence,
                description=description,
                metadata={
                    'channel_score': latest_channel_score,
                    'divergence_score': latest_divergence_score,
                    'bull_div': bull_div,
                    'bear_div': bear_div,
                    'hidden_bull_div': hidden_bull_div,
                    'hidden_bear_div': hidden_bear_div,
                    'original_score': latest_score,
                    'leveling': self.leveling,
                    'cyclic_memory': self.cyclic_memory,
                    'channel_weight': self.channel_weight,
                    'divergence_weight': self.divergence_weight
                }
            )
            
        except Exception as e:
            self.logger.error(f"MCSI MMT计分计算失败: {str(e)}")
            return ScoringResult(
                score=0.0,
                signal='neutral',
                description=f'计算错误: {str(e)}'
            )
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """
        验证输入数据是否有效
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            bool: 数据是否有效
        """
        try:
            # 检查必需的列
            required_columns = self.get_required_columns()
            if not all(col in data.columns for col in required_columns):
                return False
            
            # 检查数据长度
            if len(data) < self.get_min_data_points():
                return False
            
            # 检查数据是否包含有效值
            for col in required_columns:
                if data[col].isna().all():
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {str(e)}")
            return False
    
    def get_required_columns(self) -> List[str]:
        """获取计算所需的数据列"""
        return ['close', 'high', 'low']
    
    def get_min_data_points(self) -> int:
        """获取计算所需的最少数据点数量"""
        return max(50, self.cyclic_memory) + 20  # 需要足够的数据进行CSI计算和轨道分析
