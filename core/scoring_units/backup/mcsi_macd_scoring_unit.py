#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCSI MACD计分单元
基于MCSI MACD指标的增强版MACD计分单元
"""

import pandas as pd
import numpy as np
from typing import List
import logging

from .base_scoring_unit import BaseScoringUnit, ScoringResult
from core.indicators.mcsi_macd_indicator import MCSIMACDIndicator


class MCSIMACDScoringUnit(BaseScoringUnit):
    """
    MCSI MACD计分单元
    
    基于MCSI MACD指标的增强版MACD计分：
    - 使用动态阈值判断信号强度
    - 支持柱状图状态变化检测
    - 提供-100到+100的原始分数，映射到-10到+10
    """
    
    def __init__(self, 
                 unit_id: str = 'mcsi_macd_unit',
                 name: str = 'MCSI MACD计分单元',
                 description: str = '基于MCSI MACD指标的增强版计分单元',
                 fast_length: int = 19,
                 slow_length: int = 39,
                 signal_length: int = 9,
                 lookback_period: int = 20):
        """
        初始化MCSI MACD计分单元
        
        Args:
            unit_id: 唯一标识符
            name: 计分单元名称
            description: 描述信息
            fast_length: 快线长度，默认19
            slow_length: 慢线长度，默认39
            signal_length: 信号长度，默认9
            lookback_period: 动态阈值计算周期，默认20
        """
        super().__init__(
            unit_id=unit_id,
            name=name,
            description=description,
            min_score=-10.0,
            max_score=10.0
        )
        
        # MCSI MACD参数
        self.fast_length = fast_length
        self.slow_length = slow_length
        self.signal_length = signal_length
        self.lookback_period = lookback_period
        
        # 初始化MCSI MACD指标
        self.mcsi_macd_indicator = MCSIMACDIndicator(
            fast_length=fast_length,
            slow_length=slow_length,
            signal_length=signal_length,
            lookback_period=lookback_period
        )
        
        # 更新配置
        self.config = {
            'fast_length': fast_length,
            'slow_length': slow_length,
            'signal_length': signal_length,
            'lookback_period': lookback_period
        }
    
    def calculate_score(self, data: pd.DataFrame) -> ScoringResult:
        """
        计算MCSI MACD分数
        
        Args:
            data: 股票数据DataFrame，包含close字段
            
        Returns:
            ScoringResult: 计分结果对象
        """
        try:
            # 获取收盘价
            close_prices = data['close'].values
            
            # 计算MCSI MACD指标
            mcsi_result = self.mcsi_macd_indicator.calculate(close_prices)
            
            # 获取最新的MACD分数
            macd_scores = mcsi_result['macd_score']
            latest_score = macd_scores[-1] if len(macd_scores) > 0 else 0.0
            
            # 将-100到+100的分数映射到-10到+10
            normalized_score = latest_score / 10.0
            
            # 确定信号类型
            if latest_score >= 50:
                signal = 'strong_bullish'
            elif latest_score >= 25:
                signal = 'bullish'
            elif latest_score > 0:
                signal = 'weak_bullish'
            elif latest_score <= -50:
                signal = 'strong_bearish'
            elif latest_score <= -25:
                signal = 'bearish'
            elif latest_score < 0:
                signal = 'weak_bearish'
            else:
                signal = 'neutral'
            
            # 计算置信度
            confidence = min(1.0, abs(latest_score) / 100.0)
            
            # 生成描述
            histogram = mcsi_result['histogram'][-1] if len(mcsi_result['histogram']) > 0 else 0.0
            dynamic_threshold = mcsi_result['dynamic_threshold'][-1] if len(mcsi_result['dynamic_threshold']) > 0 else 0.0
            
            description = f"MCSI MACD分析({normalized_score:.2f}): 柱状图{histogram:.4f}, 动态阈值{dynamic_threshold:.4f}"
            
            return ScoringResult(
                score=normalized_score,
                raw_value=latest_score,
                signal=signal,
                confidence=confidence,
                description=description,
                metadata={
                    'macd': mcsi_result['macd'][-1] if len(mcsi_result['macd']) > 0 else 0.0,
                    'signal_line': mcsi_result['signal'][-1] if len(mcsi_result['signal']) > 0 else 0.0,
                    'histogram': histogram,
                    'dynamic_threshold': dynamic_threshold,
                    'original_score': latest_score,
                    'fast_length': self.fast_length,
                    'slow_length': self.slow_length,
                    'signal_length': self.signal_length,
                    'lookback_period': self.lookback_period
                }
            )
            
        except Exception as e:
            self.logger.error(f"MCSI MACD计分计算失败: {str(e)}")
            return ScoringResult(
                score=0.0,
                signal='neutral',
                description=f'计算错误: {str(e)}'
            )
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """
        验证输入数据是否有效
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            bool: 数据是否有效
        """
        try:
            # 检查必需的列
            required_columns = self.get_required_columns()
            if not all(col in data.columns for col in required_columns):
                return False
            
            # 检查数据长度
            if len(data) < self.get_min_data_points():
                return False
            
            # 检查数据是否包含有效值
            if data['close'].isna().all():
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {str(e)}")
            return False
    
    def get_required_columns(self) -> List[str]:
        """获取计算所需的数据列"""
        return ['close']
    
    def get_min_data_points(self) -> int:
        """获取计算所需的最少数据点数量"""
        return max(self.slow_length, self.lookback_period) + self.signal_length + 10
