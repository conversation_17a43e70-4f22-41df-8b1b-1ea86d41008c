#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCSI统一接口适配器
MCSI统一接口适配器 - Cython支持已移除
"""

import sys
import numpy as np
import pandas as pd
from typing import List, Optional, Dict, Any, Union
import logging
from pathlib import Path
import importlib.util

from .base_scoring_unit import BaseScoringUnit, ScoringResult

logger = logging.getLogger(__name__)

class MCSIUnifiedAdapter(BaseScoringUnit):
    """MCSI统一接口适配器基类"""
    
    def __init__(self, unit_id: str, name: str, so_module_name: str, class_name: str):
        super().__init__(
            unit_id=unit_id,
            name=name,
            description=f'基于统一接口的{name}，100%与源代码一致',
            min_score=-100.0,
            max_score=100.0,
            enabled=True
        )
        
        self.so_module_name = so_module_name
        self.class_name = class_name
        self._unified_module = None
        self._scoring_unit = None
        self._load_unified_module()
        
    def _load_unified_module(self):
        """Cython支持已移除，此方法已禁用"""
        logger.warning(f"⚠️  Cython支持已移除，{self.so_module_name}不可用")
        self._unified_module = None
        self._scoring_unit = None
                    scoring_class = getattr(self._unified_module, self.class_name)
                    self._scoring_unit = scoring_class()
                    logger.info(f"✅ 成功加载统一接口: {self.so_module_name}")
                else:
                    logger.error(f"❌ 未找到类: {self.class_name}")
            else:
                logger.error(f"❌ 统一接口文件不存在: {so_path}")
                
        except Exception as e:
            logger.error(f"❌ 加载统一接口失败: {e}")
            self._unified_module = None
            self._scoring_unit = None
            
    def is_available(self) -> bool:
        """检查统一接口是否可用"""
        return self._scoring_unit is not None
        
    def calculate_score(self, data: Union[pd.DataFrame, Dict[str, Any]],
                       seasonal_factors: Optional[Dict] = None, period: str = 'daily') -> ScoringResult:
        """计算分数 - 使用统一接口"""
        if not self.is_available():
            return ScoringResult(
                score=0.0, 
                signal='neutral', 
                confidence=0.0,
                description=f'{self.name}统一接口不可用', 
                metadata={'error': 'module_unavailable'}
            )
        
        try:
            # 调用统一接口
            return self._scoring_unit.calculate_score(
                ohlc=data if isinstance(data, pd.DataFrame) else None,
                data=data if isinstance(data, dict) else None,
                period=period
            )
        except Exception as e:
            logger.error(f"{self.name}计算失败: {e}")
            return ScoringResult(
                score=0.0,
                signal='neutral',
                confidence=0.0,
                description=f'计算错误: {str(e)}',
                metadata={'error': str(e)}
            )

    def score_with_validation(self, data: pd.DataFrame) -> ScoringResult:
        """带验证的评分计算（兼容旧接口）"""
        return self.calculate_score(data)

    def get_required_columns(self) -> List[str]:
        """获取必需的数据列"""
        if self._scoring_unit and hasattr(self._scoring_unit, 'get_required_columns'):
            return self._scoring_unit.get_required_columns()
        return ['close']

    def get_min_data_points(self) -> int:
        """获取最小数据点数"""
        if self._scoring_unit and hasattr(self._scoring_unit, 'get_min_data_points'):
            return self._scoring_unit.get_min_data_points()
        return 50

    def validate_data(self, data: pd.DataFrame) -> bool:
        """验证输入数据"""
        if self._scoring_unit and hasattr(self._scoring_unit, 'validate_data'):
            return self._scoring_unit.validate_data(data)

        # 基本验证
        try:
            if not isinstance(data, pd.DataFrame):
                return False

            required_columns = self.get_required_columns()
            for col in required_columns:
                if col not in data.columns:
                    return False

            if len(data) < self.get_min_data_points():
                return False

            return True
        except:
            return False


class MCSIUnifiedMACDUnit(MCSIUnifiedAdapter):
    """MCSI统一接口MACD单元"""
    
    def __init__(self):
        super().__init__(
            unit_id="mcsi_unified_macd",
            name="MCSI统一接口MACD指标",
            so_module_name="mcsi_macd_scoring_compiled",
            class_name="MCSIMACDScoringUnit"
        )


class MCSIUnifiedRSIUnit(MCSIUnifiedAdapter):
    """MCSI统一接口RSI单元"""
    
    def __init__(self):
        super().__init__(
            unit_id="mcsi_unified_rsi",
            name="MCSI统一接口RSI指标",
            so_module_name="mcsi_rsi_scoring_compiled",
            class_name="MCSIRSIScoringUnit"
        )


class MCSIUnifiedMMTUnit(MCSIUnifiedAdapter):
    """MCSI统一接口MMT单元"""
    
    def __init__(self):
        super().__init__(
            unit_id="mcsi_unified_mmt",
            name="MCSI统一接口MMT指标",
            so_module_name="mcsi_mmt_scoring_compiled",
            class_name="MCSIMMTScoringUnit"
        )


class MCSIUnifiedTTMUnit(MCSIUnifiedAdapter):
    """MCSI统一接口TTM单元"""
    
    def __init__(self):
        super().__init__(
            unit_id="mcsi_unified_ttm",
            name="MCSI统一接口TTM指标",
            so_module_name="mcsi_ttm_scoring_compiled",
            class_name="MCSITTMScoringUnit"
        )


# 检查统一接口可用性
MCSI_UNIFIED_AVAILABLE = True
try:
    # 尝试实例化所有单元
    _test_macd = MCSIUnifiedMACDUnit()
    _test_rsi = MCSIUnifiedRSIUnit()
    _test_mmt = MCSIUnifiedMMTUnit()
    _test_ttm = MCSIUnifiedTTMUnit()
    
    if not all([_test_macd.is_available(), _test_rsi.is_available(), 
                _test_mmt.is_available(), _test_ttm.is_available()]):
        MCSI_UNIFIED_AVAILABLE = False
        logger.warning("⚠️ 部分MCSI统一接口不可用")
    else:
        logger.info("✅ 所有MCSI统一接口可用")
        
except Exception as e:
    MCSI_UNIFIED_AVAILABLE = False
    logger.error(f"❌ MCSI统一接口初始化失败: {e}")
