#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
趋势计分单元
基于移动平均线的趋势分析计分单元
"""

from typing import List
import pandas as pd
import numpy as np
from .base_scoring_unit import BaseScoringUnit, ScoringResult
# MA计算器内嵌实现
class MACalculator:
    """移动平均线计算器"""

    @staticmethod
    def calculate_ma(data: pd.Series, period: int) -> pd.Series:
        """计算移动平均线"""
        return data.rolling(window=period, min_periods=1).mean()


class TrendScoringUnit(BaseScoringUnit):
    """
    趋势计分单元
    
    基于移动平均线多空排列计算趋势分数：
    - MA5 > MA20 > MA50 > MA200: 强多头排列
    - MA5 < MA20 < MA50 < MA200: 强空头排列
    - 混合排列: 根据具体情况给分
    """
    
    def __init__(self, 
                 unit_id: str = 'trend_unit',
                 name: str = '趋势计分单元',
                 description: str = '基于移动平均线的趋势计分单元',
                 ma_periods: dict = None):
        """
        初始化趋势计分单元
        
        Args:
            unit_id: 唯一标识符
            name: 计分单元名称
            description: 描述信息
            ma_periods: 移动平均线周期配置
        """
        super().__init__(
            unit_id=unit_id,
            name=name,
            description=description,
            min_score=-6.0,
            max_score=6.0
        )
        
        # 默认移动平均线周期
        self.ma_periods = ma_periods or {
            'ma5': 5,
            'ma20': 20,
            'ma50': 50,
            'ma200': 200
        }
        
        # 初始化MA计算器
        self.ma_calculator = MACalculator()
        
        # 更新配置
        self.config = {
            'ma_periods': self.ma_periods
        }
    
    def get_required_columns(self) -> List[str]:
        """获取计算所需的数据列"""
        return ['close']
    
    def get_min_data_points(self) -> int:
        """获取计算所需的最少数据点数量"""
        max_period = max(self.ma_periods.values())
        return max_period + 10  # 需要最长周期的MA加上缓冲
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """
        验证输入数据是否有效
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            bool: 数据是否有效
        """
        try:
            # 检查必需列
            required_columns = self.get_required_columns()
            if not all(col in data.columns for col in required_columns):
                self.logger.warning(f"缺少必需列: {required_columns}")
                return False
            
            # 检查数据量
            if len(data) < self.get_min_data_points():
                self.logger.warning(f"数据量不足，需要至少 {self.get_min_data_points()} 个数据点")
                return False
            
            # 检查数据有效性
            if data['close'].isna().all():
                self.logger.warning("收盘价数据全部为空")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {str(e)}")
            return False
    
    def calculate_score(self, data: pd.DataFrame) -> ScoringResult:
        """
        计算趋势分数
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            ScoringResult: 计分结果
        """
        try:
            # 计算所有移动平均线
            ma_values = {}
            for ma_name, period in self.ma_periods.items():
                ma_series = self.ma_calculator.calculate_ma(data['close'], period)
                if len(ma_series) > 0 and not ma_series.isna().all():
                    ma_values[ma_name] = ma_series.iloc[-1]
                else:
                    self.logger.warning(f"无法计算 {ma_name}({period})")
                    return ScoringResult(
                        score=0.0,
                        signal='neutral',
                        description=f'移动平均线计算失败: {ma_name}'
                    )
            
            # 获取当前价格
            current_price = data['close'].iloc[-1]
            
            # 计算趋势分数
            score, signal, description = self._calculate_trend_score(current_price, ma_values)
            
            return ScoringResult(
                score=score,
                raw_value=current_price,
                signal=signal,
                confidence=self._calculate_confidence(current_price, ma_values),
                description=description,
                metadata={
                    'current_price': current_price,
                    'ma_values': ma_values,
                    'ma_periods': self.ma_periods
                }
            )
            
        except Exception as e:
            self.logger.error(f"趋势计分计算失败: {str(e)}")
            return ScoringResult(
                score=0.0,
                signal='neutral',
                description=f'计算错误: {str(e)}'
            )
    
    def _calculate_trend_score(self, current_price: float, ma_values: dict) -> tuple:
        """
        根据移动平均线排列计算趋势分数
        
        Args:
            current_price: 当前价格
            ma_values: 移动平均线值字典
            
        Returns:
            tuple: (分数, 信号, 描述)
        """
        score = 0.0
        signal = 'neutral'
        description_parts = []
        
        # 获取MA值
        ma5 = ma_values.get('ma5', 0)
        ma20 = ma_values.get('ma20', 0)
        ma50 = ma_values.get('ma50', 0)
        ma200 = ma_values.get('ma200', 0)
        
        # 按照规则文档的6个比较项进行评分
        # 每个比较项 +1分 或 -1分，总分范围 -6分 到 +6分
        total_score = 0

        # 1. MA5 vs MA20
        if ma5 > ma20:
            total_score += 1
            description_parts.append('短期上升趋势')
        elif ma5 < ma20:
            total_score -= 1
            description_parts.append('短期下降趋势')

        # 2. MA5 vs MA50
        if ma5 > ma50:
            total_score += 1
            description_parts.append('中短期强势')
        elif ma5 < ma50:
            total_score -= 1
            description_parts.append('中短期弱势')

        # 3. MA5 vs MA200
        if ma5 > ma200:
            total_score += 1
            description_parts.append('整体趋势向上')
        elif ma5 < ma200:
            total_score -= 1
            description_parts.append('整体趋势向下')

        # 4. MA20 vs MA50
        if ma20 > ma50:
            total_score += 1
            description_parts.append('中期上升趋势')
        elif ma20 < ma50:
            total_score -= 1
            description_parts.append('中期下降趋势')

        # 5. MA20 vs MA200
        if ma20 > ma200:
            total_score += 1
            description_parts.append('中长期强势')
        elif ma20 < ma200:
            total_score -= 1
            description_parts.append('中长期弱势')

        # 6. MA50 vs MA200
        if ma50 > ma200:
            total_score += 1
            description_parts.append('长期上升趋势')
        elif ma50 < ma200:
            total_score -= 1
            description_parts.append('长期下降趋势')

        # 总分范围：-6 到 +6
        
        # 4. 确定信号类型（基于-6到+6的分数范围）
        if total_score == 6:
            signal = 'strong_bullish'  # 完美多头排列
        elif total_score >= 4:
            signal = 'bullish'  # 强势上升趋势
        elif total_score >= 2:
            signal = 'neutral_bullish'  # 上升趋势
        elif total_score >= 1:
            signal = 'neutral_bullish'  # 弱势上升
        elif total_score == -6:
            signal = 'strong_bearish'  # 完美空头排列
        elif total_score <= -4:
            signal = 'bearish'  # 强势下降趋势
        elif total_score <= -2:
            signal = 'neutral_bearish'  # 下降趋势
        elif total_score <= -1:
            signal = 'neutral_bearish'  # 弱势下降
        else:
            signal = 'neutral'  # 震荡
        
        # 5. 生成描述
        if total_score > 0:
            trend_desc = '多头趋势'
        elif total_score < 0:
            trend_desc = '空头趋势'
        else:
            trend_desc = '震荡趋势'
        
        description = f"趋势分析({total_score:.1f}): {trend_desc}"
        
        return total_score, signal, description
    
    def _calculate_confidence(self, current_price: float, ma_values: dict) -> float:
        """
        计算信号置信度
        
        Args:
            current_price: 当前价格
            ma_values: 移动平均线值字典
            
        Returns:
            float: 置信度 (0.0-1.0)
        """
        # 计算价格与各MA的距离比例
        ma_distances = []
        for ma_name, ma_value in ma_values.items():
            if ma_value > 0:
                distance_ratio = abs(current_price - ma_value) / ma_value
                ma_distances.append(distance_ratio)
        
        if not ma_distances:
            return 0.5
        
        # 平均距离越大，置信度越高（趋势越明确）
        avg_distance = np.mean(ma_distances)
        
        # 将距离转换为置信度 (0-10%距离对应0.5-1.0置信度)
        confidence = 0.5 + min(0.5, avg_distance * 5)
        
        return confidence
    
    def update_config(self, config: dict) -> None:
        """
        更新配置参数
        
        Args:
            config: 新的配置参数
        """
        super().update_config(config)
        
        # 更新MA周期
        if 'ma_periods' in config:
            self.ma_periods.update(config['ma_periods'])
