#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MACD计分单元
基于MACD指标的独立计分单元
"""

from typing import List
import pandas as pd
import numpy as np
from .base_scoring_unit import BaseScoringUnit, ScoringResult
# MACD计算器内嵌实现
class MACDCalculator:
    """MACD计算器"""

    @staticmethod
    def calculate_macd(data: pd.Series, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9):
        """计算MACD指标"""
        ema_fast = data.ewm(span=fast_period).mean()
        ema_slow = data.ewm(span=slow_period).mean()
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal_period).mean()
        histogram = macd_line - signal_line

        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }


class MACDScoringUnit(BaseScoringUnit):
    """
    MACD计分单元
    
    基于MACD指标计算分数：
    - 金叉信号: 正分
    - 死叉信号: 负分
    - 柱状图趋势: 额外加分/减分
    - MACD线位置: 零轴上下影响分数
    """
    
    def __init__(self, 
                 unit_id: str = 'macd_unit',
                 name: str = 'MACD计分单元',
                 description: str = '基于MACD指标的计分单元',
                 fast_period: int = 12,
                 slow_period: int = 26,
                 signal_period: int = 9):
        """
        初始化MACD计分单元
        
        Args:
            unit_id: 唯一标识符
            name: 计分单元名称
            description: 描述信息
            fast_period: 快线周期
            slow_period: 慢线周期
            signal_period: 信号线周期
        """
        super().__init__(
            unit_id=unit_id,
            name=name,
            description=description,
            min_score=-6.0,
            max_score=6.0
        )
        
        # MACD参数
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period
        
        # MACD计算器是静态方法，不需要实例化
        self.macd_calculator = MACDCalculator
        
        # 更新配置
        self.config = {
            'fast_period': fast_period,
            'slow_period': slow_period,
            'signal_period': signal_period
        }
    
    def get_required_columns(self) -> List[str]:
        """获取计算所需的数据列"""
        return ['close']
    
    def get_min_data_points(self) -> int:
        """获取计算所需的最少数据点数量"""
        return self.slow_period + self.signal_period + 10  # MACD需要足够的数据点
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """
        验证输入数据是否有效
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            bool: 数据是否有效
        """
        try:
            # 检查必需列
            required_columns = self.get_required_columns()
            if not all(col in data.columns for col in required_columns):
                self.logger.warning(f"缺少必需列: {required_columns}")
                return False
            
            # 检查数据量
            if len(data) < self.get_min_data_points():
                self.logger.warning(f"数据量不足，需要至少 {self.get_min_data_points()} 个数据点")
                return False
            
            # 检查数据有效性
            if data['close'].isna().all():
                self.logger.warning("收盘价数据全部为空")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {str(e)}")
            return False
    
    def calculate_score(self, data: pd.DataFrame) -> ScoringResult:
        """
        计算MACD分数
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            ScoringResult: 计分结果
        """
        try:
            # 计算MACD指标
            macd_data = self.macd_calculator.calculate_macd(
                data['close'],
                self.fast_period,
                self.slow_period,
                self.signal_period
            )

            if (macd_data is None or
                macd_data['macd'].empty or
                macd_data['signal'].empty or
                macd_data['histogram'].empty):
                return ScoringResult(
                    score=0.0,
                    signal='neutral',
                    description='MACD计算失败'
                )

            # 获取最新的MACD数据
            latest_macd = macd_data['macd'].iloc[-1]
            latest_signal = macd_data['signal'].iloc[-1]
            latest_histogram = macd_data['histogram'].iloc[-1]

            # 检查数据有效性
            if (pd.isna(latest_macd) or pd.isna(latest_signal) or pd.isna(latest_histogram)):
                return ScoringResult(
                    score=0.0,
                    signal='neutral',
                    description='MACD数据无效'
                )

            # 分析信号类型和趋势
            signal_type = self._analyze_signal_type(macd_data)
            crossover = self._detect_crossover(macd_data)
            histogram_trend = self._analyze_histogram_trend(macd_data)
            
            # 计算分数
            score, signal, description = self._calculate_macd_score(
                latest_macd, latest_signal, latest_histogram,
                signal_type, crossover, histogram_trend
            )
            
            return ScoringResult(
                score=score,
                raw_value=latest_macd,
                signal=signal,
                confidence=self._calculate_confidence(signal_type, crossover, histogram_trend),
                description=description,
                metadata={
                    'macd': latest_macd,
                    'signal_line': latest_signal,
                    'histogram': latest_histogram,
                    'signal_type': signal_type,
                    'crossover': crossover,
                    'histogram_trend': histogram_trend,
                    'fast_period': self.fast_period,
                    'slow_period': self.slow_period,
                    'signal_period': self.signal_period
                }
            )
            
        except Exception as e:
            self.logger.error(f"MACD计分计算失败: {str(e)}")
            return ScoringResult(
                score=0.0,
                signal='neutral',
                description=f'计算错误: {str(e)}'
            )
    
    def _calculate_macd_score(self, macd_value: float, signal_value: float, 
                             histogram_value: float, signal_type: str,
                             crossover: str, histogram_trend: str) -> tuple:
        """
        根据MACD指标计算分数
        
        Args:
            macd_value: MACD线值
            signal_value: 信号线值
            histogram_value: 柱状图值
            signal_type: 信号类型
            crossover: 交叉信号
            histogram_trend: 柱状图趋势
            
        Returns:
            tuple: (分数, 信号, 描述)
        """
        base_score = 0.0
        signal = 'neutral'
        description_parts = []
        
        # 1. 交叉信号评分 (按照旧系统逻辑)
        # 旧系统评分规则：
        # 'strong_buy': 2,    # MACD金叉且柱状图转正: +2分
        # 'buy': 1,           # MACD金叉: +1分
        # 'strong_sell': -2,  # MACD死叉且柱状图转负: -2分
        # 'sell': -1,         # MACD死叉: -1分
        # 'hold': 0           # 无明显信号: 0分

        if crossover == 'golden_cross':
            if histogram_value > 0:
                base_score += 2.0  # MACD金叉且柱状图转正
                signal = 'bullish'
                description_parts.append('MACD金叉且柱状图转正')
            else:
                base_score += 1.0  # MACD金叉
                signal = 'bullish'
                description_parts.append('MACD金叉')
        elif crossover == 'death_cross':
            if histogram_value < 0:
                base_score -= 2.0  # MACD死叉且柱状图转负
                signal = 'bearish'
                description_parts.append('MACD死叉且柱状图转负')
            else:
                base_score -= 1.0  # MACD死叉
                signal = 'bearish'
                description_parts.append('MACD死叉')
        
        # 如果没有交叉信号，保持中性
        if signal == 'neutral':
            base_score = 0.0
            description_parts.append('无明显信号')
        
        # 组合描述
        if not description_parts:
            description_parts.append('MACD中性')
        
        description = f"MACD({macd_value:.4f}): {', '.join(description_parts)}"
        
        return base_score, signal, description
    
    def _calculate_confidence(self, signal_type: str, crossover: str, 
                             histogram_trend: str) -> float:
        """
        计算信号置信度
        
        Args:
            signal_type: 信号类型
            crossover: 交叉信号
            histogram_trend: 柱状图趋势
            
        Returns:
            float: 置信度 (0.0-1.0)
        """
        confidence = 0.5  # 基础置信度
        
        # 交叉信号增加置信度
        if crossover in ['golden_cross', 'death_cross']:
            confidence += 0.3
        
        # 柱状图趋势一致性增加置信度
        if histogram_trend in ['increasing', 'decreasing']:
            confidence += 0.2
        
        # 强信号增加置信度
        if signal_type in ['strong_bullish', 'strong_bearish']:
            confidence += 0.2
        
        return min(1.0, confidence)
    
    def update_config(self, config: dict) -> None:
        """
        更新配置参数
        
        Args:
            config: 新的配置参数
        """
        super().update_config(config)
        
        # 更新MACD参数
        if any(key in config for key in ['fast_period', 'slow_period', 'signal_period']):
            self.fast_period = config.get('fast_period', self.fast_period)
            self.slow_period = config.get('slow_period', self.slow_period)
            self.signal_period = config.get('signal_period', self.signal_period)
            
            # MACD计算器是静态方法，不需要重新实例化

    def _analyze_signal_type(self, macd_data: dict) -> str:
        """分析MACD信号类型"""
        latest_macd = macd_data['macd'].iloc[-1]
        latest_signal = macd_data['signal'].iloc[-1]

        if latest_macd > latest_signal and latest_macd > 0:
            return 'strong_bullish'
        elif latest_macd > latest_signal and latest_macd <= 0:
            return 'weak_bullish'
        elif latest_macd < latest_signal and latest_macd < 0:
            return 'strong_bearish'
        elif latest_macd < latest_signal and latest_macd >= 0:
            return 'weak_bearish'
        else:
            return 'neutral'

    def _detect_crossover(self, macd_data: dict) -> str:
        """检测MACD交叉信号"""
        if len(macd_data['macd']) < 2:
            return 'none'

        current_macd = macd_data['macd'].iloc[-1]
        current_signal = macd_data['signal'].iloc[-1]
        prev_macd = macd_data['macd'].iloc[-2]
        prev_signal = macd_data['signal'].iloc[-2]

        # 金叉：MACD线从下方穿越信号线
        if prev_macd <= prev_signal and current_macd > current_signal:
            return 'golden_cross'
        # 死叉：MACD线从上方穿越信号线
        elif prev_macd >= prev_signal and current_macd < current_signal:
            return 'death_cross'
        else:
            return 'none'

    def _analyze_histogram_trend(self, macd_data: dict) -> str:
        """分析MACD柱状图趋势"""
        if len(macd_data['histogram']) < 3:
            return 'neutral'

        recent_hist = macd_data['histogram'].iloc[-3:].values

        if all(recent_hist[i] < recent_hist[i+1] for i in range(len(recent_hist)-1)):
            return 'increasing'
        elif all(recent_hist[i] > recent_hist[i+1] for i in range(len(recent_hist)-1)):
            return 'decreasing'
        else:
            return 'neutral'
