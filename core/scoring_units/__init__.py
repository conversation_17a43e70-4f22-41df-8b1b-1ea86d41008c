#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
计分单元模块
提供独立的技术指标计分单元
"""

from .base_scoring_unit import BaseScoringUnit
from .rsi_scoring_unit import RSIScoringUnit
from .macd_scoring_unit import MACDScoringUnit
from .trend_scoring_unit import TrendScoringUnit
from .wave_scoring_unit import WaveScoringUnit
# 使用适配器来加载MCSI计分单元（支持保护代码）
from .mcsi_adapter import MCSIMACDScoringUnit, MCSIMMTScoringUnit, MCSIRSIScoringUnit, MCSITD9ScoringUnit

# 导入高级MCSI计分单元（基于打包的wheel组件）
try:
    from .mcsi_premium_units import (
        MCSIPremiumRSIUnit, 
        MCSIPremiumMACDUnit,
        MCSIPremiumMMTUnit,
        MCSIPremiumTTMUnit,
        MCSI_PREMIUM_AVAILABLE
    )
    _PREMIUM_IMPORTS_SUCCESS = True
except ImportError as e:
    _PREMIUM_IMPORTS_SUCCESS = False
    print(f"Warning: MCSI Premium units import failed: {e}")

__all__ = [
    'BaseScoringUnit',
    'RSIScoringUnit',
    'MACDScoringUnit',
    'TrendScoringUnit',
    'WaveScoringUnit',
    'MCSIMACDScoringUnit',
    'MCSIMMTScoringUnit',
    'MCSIRSIScoringUnit',
    'MCSITD9ScoringUnit'
]

# 如果高级组件可用，添加到导出列表
if _PREMIUM_IMPORTS_SUCCESS:
    __all__.extend([
        'MCSIPremiumRSIUnit',
        'MCSIPremiumMACDUnit', 
        'MCSIPremiumMMTUnit',
        'MCSIPremiumTTMUnit'
    ])
