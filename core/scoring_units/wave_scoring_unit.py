#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
波段计分单元
基于短期波动和动量的计分单元
"""

from typing import List
import pandas as pd
import numpy as np
from .base_scoring_unit import BaseScoringUnit, ScoringResult


class WaveScoringUnit(BaseScoringUnit):
    """
    波段计分单元
    
    基于短期价格波动和动量计算分数：
    - 价格动量
    - 成交量配合
    - 短期波动率
    - 支撑阻力位突破
    """
    
    def __init__(self, 
                 unit_id: str = 'wave_unit',
                 name: str = '波段计分单元',
                 description: str = '基于短期波动和动量的计分单元',
                 momentum_period: int = 5,
                 volatility_period: int = 10):
        """
        初始化波段计分单元
        
        Args:
            unit_id: 唯一标识符
            name: 计分单元名称
            description: 描述信息
            momentum_period: 动量计算周期
            volatility_period: 波动率计算周期
        """
        super().__init__(
            unit_id=unit_id,
            name=name,
            description=description,
            min_score=-4.0,
            max_score=4.0
        )
        
        # 波段参数
        self.momentum_period = momentum_period
        self.volatility_period = volatility_period
        
        # 更新配置
        self.config = {
            'momentum_period': momentum_period,
            'volatility_period': volatility_period
        }
    
    def get_required_columns(self) -> List[str]:
        """获取计算所需的数据列"""
        return ['close', 'high', 'low', 'volume']
    
    def get_min_data_points(self) -> int:
        """获取计算所需的最少数据点数量"""
        return max(self.momentum_period, self.volatility_period) + 10
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """
        验证输入数据是否有效
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            bool: 数据是否有效
        """
        try:
            # 检查必需列
            required_columns = self.get_required_columns()
            if not all(col in data.columns for col in required_columns):
                self.logger.warning(f"缺少必需列: {required_columns}")
                return False
            
            # 检查数据量
            if len(data) < self.get_min_data_points():
                self.logger.warning(f"数据量不足，需要至少 {self.get_min_data_points()} 个数据点")
                return False
            
            # 检查数据有效性
            for col in required_columns:
                if data[col].isna().all():
                    self.logger.warning(f"{col} 数据全部为空")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {str(e)}")
            return False
    
    def calculate_score(self, data: pd.DataFrame) -> ScoringResult:
        """
        计算波段分数
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            ScoringResult: 计分结果
        """
        try:
            # 计算各种波段指标
            momentum_score = self._calculate_momentum_score(data)
            volume_score = self._calculate_volume_score(data)
            volatility_score = self._calculate_volatility_score(data)
            breakout_score = self._calculate_breakout_score(data)
            
            # 综合评分
            total_score = (
                momentum_score * 0.4 +
                volume_score * 0.3 +
                volatility_score * 0.2 +
                breakout_score * 0.1
            )
            
            # 确定信号
            if total_score >= 2.0:
                signal = 'strong_bullish'
            elif total_score >= 1.0:
                signal = 'bullish'
            elif total_score >= 0.5:
                signal = 'neutral_bullish'
            elif total_score <= -2.0:
                signal = 'strong_bearish'
            elif total_score <= -1.0:
                signal = 'bearish'
            elif total_score <= -0.5:
                signal = 'neutral_bearish'
            else:
                signal = 'neutral'
            
            # 生成描述
            description = f"波段分析({total_score:.2f}): 动量{momentum_score:.1f}, 量能{volume_score:.1f}, 波动{volatility_score:.1f}, 突破{breakout_score:.1f}"
            
            return ScoringResult(
                score=total_score,
                raw_value=data['close'].iloc[-1],
                signal=signal,
                confidence=self._calculate_confidence(total_score),
                description=description,
                metadata={
                    'momentum_score': momentum_score,
                    'volume_score': volume_score,
                    'volatility_score': volatility_score,
                    'breakout_score': breakout_score,
                    'momentum_period': self.momentum_period,
                    'volatility_period': self.volatility_period
                }
            )
            
        except Exception as e:
            self.logger.error(f"波段计分计算失败: {str(e)}")
            return ScoringResult(
                score=0.0,
                signal='neutral',
                description=f'计算错误: {str(e)}'
            )
    
    def _calculate_momentum_score(self, data: pd.DataFrame) -> float:
        """计算动量分数"""
        try:
            closes = data['close'].tail(self.momentum_period + 1)
            if len(closes) < 2:
                return 0.0
            
            # 计算价格变化率
            price_change = (closes.iloc[-1] - closes.iloc[0]) / closes.iloc[0]
            
            # 转换为分数 (-2 到 +2)
            momentum_score = price_change * 20  # 10%变化对应2分
            return max(-2.0, min(2.0, momentum_score))
            
        except Exception:
            return 0.0
    
    def _calculate_volume_score(self, data: pd.DataFrame) -> float:
        """计算成交量分数"""
        try:
            volumes = data['volume'].tail(self.momentum_period + 5)
            closes = data['close'].tail(self.momentum_period + 5)
            
            if len(volumes) < 5:
                return 0.0
            
            # 计算最近几天的平均成交量
            recent_volume = volumes.tail(3).mean()
            historical_volume = volumes.head(-3).mean()
            
            if historical_volume == 0:
                return 0.0
            
            # 成交量放大比例
            volume_ratio = recent_volume / historical_volume
            
            # 价格方向
            price_direction = 1 if closes.iloc[-1] > closes.iloc[-4] else -1
            
            # 量价配合评分
            if volume_ratio > 1.5:  # 放量
                volume_score = 1.0 * price_direction
            elif volume_ratio > 1.2:
                volume_score = 0.5 * price_direction
            elif volume_ratio < 0.7:  # 缩量
                volume_score = -0.5 * price_direction
            else:
                volume_score = 0.0
            
            return max(-1.0, min(1.0, volume_score))
            
        except Exception:
            return 0.0
    
    def _calculate_volatility_score(self, data: pd.DataFrame) -> float:
        """计算波动率分数"""
        try:
            closes = data['close'].tail(self.volatility_period)
            if len(closes) < 5:
                return 0.0
            
            # 计算日收益率
            returns = closes.pct_change().dropna()
            
            if len(returns) < 3:
                return 0.0
            
            # 计算波动率
            volatility = returns.std()
            
            # 波动率评分 (适度波动为正，过高或过低波动为负)
            if 0.01 <= volatility <= 0.03:  # 1%-3%日波动率为适度
                volatility_score = 0.5
            elif 0.005 <= volatility <= 0.05:  # 0.5%-5%为可接受
                volatility_score = 0.2
            else:  # 过高或过低波动率
                volatility_score = -0.3
            
            return volatility_score
            
        except Exception:
            return 0.0
    
    def _calculate_breakout_score(self, data: pd.DataFrame) -> float:
        """计算突破分数"""
        try:
            if len(data) < 20:
                return 0.0
            
            # 计算最近20天的高低点
            recent_data = data.tail(20)
            resistance = recent_data['high'].max()
            support = recent_data['low'].min()
            
            current_price = data['close'].iloc[-1]
            
            # 突破评分
            if current_price > resistance * 1.01:  # 突破阻力位
                breakout_score = 0.5
            elif current_price < support * 0.99:  # 跌破支撑位
                breakout_score = -0.5
            else:
                breakout_score = 0.0
            
            return breakout_score
            
        except Exception:
            return 0.0
    
    def _calculate_confidence(self, total_score: float) -> float:
        """
        计算信号置信度
        
        Args:
            total_score: 总分数
            
        Returns:
            float: 置信度 (0.0-1.0)
        """
        # 分数绝对值越大，置信度越高
        abs_score = abs(total_score)
        
        if abs_score >= 3.0:
            return 1.0
        elif abs_score >= 2.0:
            return 0.8
        elif abs_score >= 1.0:
            return 0.6
        else:
            return 0.4
    
    def update_config(self, config: dict) -> None:
        """
        更新配置参数
        
        Args:
            config: 新的配置参数
        """
        super().update_config(config)
        
        # 更新波段参数
        if 'momentum_period' in config:
            self.momentum_period = config['momentum_period']
        
        if 'volatility_period' in config:
            self.volatility_period = config['volatility_period']
