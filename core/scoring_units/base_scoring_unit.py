#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础计分单元
所有技术指标计分单元的基类
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
import pandas as pd
import logging
from datetime import datetime


class ScoringResult:
    """计分结果类"""
    
    def __init__(self, 
                 score: float,
                 raw_value: Optional[float] = None,
                 signal: str = 'neutral',
                 confidence: float = 1.0,
                 description: str = '',
                 metadata: Optional[Dict[str, Any]] = None):
        self.score = score
        self.raw_value = raw_value
        self.signal = signal  # 'bullish', 'bearish', 'neutral'
        self.confidence = confidence  # 0.0 - 1.0
        self.description = description
        self.metadata = metadata or {}
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        def make_json_safe(obj):
            """递归地将对象转换为JSON安全格式"""
            import numpy as np

            if isinstance(obj, dict):
                return {k: make_json_safe(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [make_json_safe(item) for item in obj]
            elif isinstance(obj, tuple):
                return [make_json_safe(item) for item in obj]
            elif isinstance(obj, np.ndarray):
                return make_json_safe(obj.tolist())
            elif isinstance(obj, np.bool_):
                return bool(obj)  # 转换NumPy布尔类型为Python布尔类型
            elif isinstance(obj, (np.integer, np.int8, np.int16, np.int32, np.int64)):
                return int(obj)  # 转换NumPy整数类型为Python整数类型
            elif isinstance(obj, (np.floating, np.float16, np.float32, np.float64)):
                if np.isinf(obj):
                    return 999999.0 if obj > 0 else -999999.0
                elif np.isnan(obj):
                    return 0.0
                else:
                    return float(obj)  # 转换NumPy浮点类型为Python浮点类型
            elif isinstance(obj, float):
                if obj == float('inf'):
                    return 999999.0
                elif obj == float('-inf'):
                    return -999999.0
                elif obj != obj:  # NaN检查
                    return 0.0
                else:
                    return obj
            elif obj is None:
                return None
            else:
                return obj

        return {
            'score': make_json_safe(self.score),
            'raw_value': make_json_safe(self.raw_value),
            'signal': self.signal,
            'confidence': make_json_safe(self.confidence),
            'description': self.description,
            'metadata': make_json_safe(self.metadata),
            'timestamp': self.timestamp.isoformat()
        }


class BaseScoringUnit(ABC):
    """
    基础计分单元抽象类
    
    所有技术指标计分单元都必须继承此类并实现抽象方法
    确保每个计分单元都是独立的个体，互不影响
    """
    
    def __init__(self, 
                 unit_id: str,
                 name: str,
                 description: str = '',
                 min_score: float = -10.0,
                 max_score: float = 10.0,
                 enabled: bool = True):
        """
        初始化计分单元
        
        Args:
            unit_id: 唯一标识符
            name: 计分单元名称
            description: 描述信息
            min_score: 最小分数
            max_score: 最大分数
            enabled: 是否启用
        """
        self.unit_id = unit_id
        self.name = name
        self.description = description
        self.min_score = min_score
        self.max_score = max_score
        self.enabled = enabled
        self.logger = logging.getLogger(f"{__name__}.{unit_id}")
        
        # 计分历史记录
        self.scoring_history: List[ScoringResult] = []
        
        # 配置参数
        self.config = {}
        
    @abstractmethod
    def calculate_score(self, data: pd.DataFrame) -> ScoringResult:
        """
        计算技术指标分数
        
        Args:
            data: 股票数据DataFrame，包含OHLCV等字段
            
        Returns:
            ScoringResult: 计分结果对象
        """
        pass
    
    @abstractmethod
    def validate_data(self, data: pd.DataFrame) -> bool:
        """
        验证输入数据是否有效
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            bool: 数据是否有效
        """
        pass
    
    @abstractmethod
    def get_required_columns(self) -> List[str]:
        """
        获取计算所需的数据列
        
        Returns:
            List[str]: 必需的列名列表
        """
        pass
    
    @abstractmethod
    def get_min_data_points(self) -> int:
        """
        获取计算所需的最少数据点数量
        
        Returns:
            int: 最少数据点数量
        """
        pass
    
    def normalize_score(self, raw_score: float) -> float:
        """
        标准化分数到指定范围
        
        Args:
            raw_score: 原始分数
            
        Returns:
            float: 标准化后的分数
        """
        return max(self.min_score, min(self.max_score, raw_score))
    
    def update_config(self, config: Dict[str, Any]) -> None:
        """
        更新配置参数
        
        Args:
            config: 新的配置参数
        """
        self.config.update(config)
        self.logger.info(f"计分单元 {self.unit_id} 配置已更新")
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取当前配置
        
        Returns:
            Dict[str, Any]: 当前配置参数
        """
        return self.config.copy()
    
    def get_info(self) -> Dict[str, Any]:
        """
        获取计分单元信息
        
        Returns:
            Dict[str, Any]: 计分单元信息
        """
        return {
            'unit_id': self.unit_id,
            'name': self.name,
            'description': self.description,
            'min_score': self.min_score,
            'max_score': self.max_score,
            'enabled': self.enabled,
            'required_columns': self.get_required_columns(),
            'min_data_points': self.get_min_data_points(),
            'config': self.config
        }
    
    def enable(self) -> None:
        """启用计分单元"""
        self.enabled = True
        self.logger.info(f"计分单元 {self.unit_id} 已启用")
    
    def disable(self) -> None:
        """禁用计分单元"""
        self.enabled = False
        self.logger.info(f"计分单元 {self.unit_id} 已禁用")
    
    def add_to_history(self, result: ScoringResult) -> None:
        """
        添加计分结果到历史记录
        
        Args:
            result: 计分结果
        """
        self.scoring_history.append(result)
        
        # 保持历史记录在合理范围内
        if len(self.scoring_history) > 1000:
            self.scoring_history = self.scoring_history[-500:]
    
    def get_latest_result(self) -> Optional[ScoringResult]:
        """
        获取最新的计分结果
        
        Returns:
            Optional[ScoringResult]: 最新的计分结果，如果没有则返回None
        """
        return self.scoring_history[-1] if self.scoring_history else None
    
    def score_with_validation(self, data: pd.DataFrame) -> ScoringResult:
        """
        带验证的计分方法
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            ScoringResult: 计分结果
        """
        try:
            # 检查是否启用
            if not self.enabled:
                return ScoringResult(
                    score=0.0,
                    signal='neutral',
                    description=f"计分单元 {self.unit_id} 已禁用"
                )
            
            # 验证数据
            if not self.validate_data(data):
                return ScoringResult(
                    score=0.0,
                    signal='neutral',
                    description=f"数据验证失败: {self.unit_id}"
                )
            
            # 计算分数
            result = self.calculate_score(data)
            
            # 标准化分数
            result.score = self.normalize_score(result.score)
            
            # 添加到历史记录
            self.add_to_history(result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"计分单元 {self.unit_id} 计算失败: {str(e)}")
            return ScoringResult(
                score=0.0,
                signal='neutral',
                description=f"计算错误: {str(e)}"
            )
