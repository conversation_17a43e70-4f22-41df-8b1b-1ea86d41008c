#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCSI MMT评分单元
标准化的ScoringUnit实现，继承自BaseScoringUnit
"""

import numpy as np
import pandas as pd
from typing import List, Optional, Dict, Any
import logging
from .base_scoring_unit import BaseScoringUnit, ScoringResult

class MCSIMMTScoringUnit(BaseScoringUnit):
    """MCSI MMT动量技术指标评分单元"""
    
    def __init__(self):
        """初始化MCSI MMT评分单元"""
        super().__init__(
            unit_id='mcsi_mmt',
            name='MCSI MMT评分单元',
            description='基于动量和背离检测的评分系统',
            min_score=-100.0,
            max_score=100.0,
            enabled=True
        )
        
        # MMT参数
        self.leveling = 10
        self.cyclic_memory = 34
        self.channel_weight = 0.5
        self.divergence_weight = 0.5
        
        # 状态变量（对应Pine Script的var变量）
        self.last_channel_score = 0.0
        self.last_divergence_score = 0.0
        self.channel_signal_duration = 0
        self.divergence_signal_duration = 0
        
    def calculate_score(self, data: pd.DataFrame, seasonal_factors: Optional[Dict] = None) -> ScoringResult:
        """
        计算MCSI MMT评分
        
        Args:
            data: 包含OHLCV数据的DataFrame
            seasonal_factors: 可选的季节性因子
            
        Returns:
            ScoringResult对象
        """
        try:
            # 验证数据
            if not self.validate_data(data):
                return ScoringResult(
                    score=0.0,
                    signal='neutral',
                    confidence=0.0,
                    description='数据验证失败'
                )
            
            # 提取价格数据
            close_prices = data['close'].values
            high_prices = data['high'].values
            low_prices = data['low'].values
            
            # 计算MMT指标
            mmt_data = self._calculate_mmt(close_prices, high_prices, low_prices)
            
            # 获取最新分数
            latest_score = float(mmt_data['mmt_score'][-1])
            
            # 确定信号类型
            if latest_score > 50:
                signal = 'bullish'
            elif latest_score < -50:
                signal = 'bearish'
            else:
                signal = 'neutral'
            
            # 计算置信度
            confidence = min(abs(latest_score) / 100.0, 1.0)
            
            # 生成描述
            description = self._generate_description(latest_score, mmt_data)
            
            # 准备元数据
            metadata = {
                'channel_score': float(mmt_data['channel_score'][-1]),
                'divergence_score': float(mmt_data['divergence_score'][-1]),
                'momentum': float(mmt_data['momentum'][-1]),
                'csi_buffer': float(mmt_data['csi_buffer'][-1]),
                'score_history': mmt_data['mmt_score'][-10:].tolist()
            }
            
            return ScoringResult(
                score=latest_score,
                raw_value=float(mmt_data['momentum'][-1]),
                signal=signal,
                confidence=confidence,
                description=description,
                metadata=metadata
            )
            
        except Exception as e:
            self.logger.error(f"MCSI MMT评分计算失败: {str(e)}")
            return ScoringResult(
                score=0.0,
                signal='neutral',
                confidence=0.0,
                description=f'计算错误: {str(e)}'
            )
    
    def _calculate_mmt(self, close_prices: np.ndarray, high_prices: np.ndarray, low_prices: np.ndarray) -> Dict:
        """
        内部MMT计算逻辑（简化版本）
        """
        # 计算CSI缓冲区
        csi_buffer = self._calculate_csi_buffer(close_prices, high_prices, low_prices)
        
        # 计算动态轨道
        high_band, low_band = self._calculate_bands(csi_buffer)
        
        # 计算动量
        momentum = self._calculate_momentum(csi_buffer)
        
        # 检测背离
        divergences = self._detect_divergences(csi_buffer, high_prices, low_prices)
        
        # 计算评分
        channel_score, divergence_score, mmt_score = self._calculate_scores(
            csi_buffer, high_band, low_band, momentum, divergences
        )
        
        return {
            'csi_buffer': csi_buffer,
            'high_band': high_band,
            'low_band': low_band,
            'momentum': momentum,
            'channel_score': channel_score,
            'divergence_score': divergence_score,
            'mmt_score': mmt_score,
            'bull_div': divergences['bull_div'],
            'bear_div': divergences['bear_div']
        }
    
    def _calculate_csi_buffer(self, close_prices: np.ndarray, high_prices: np.ndarray, low_prices: np.ndarray) -> np.ndarray:
        """计算CSI缓冲区（简化实现）"""
        # 使用典型价格的动量作为CSI缓冲区
        typical_price = (high_prices + low_prices + close_prices) / 3
        
        # 计算快慢推力的差值
        thrust1 = self._iwtt_csi_simplified(typical_price, 1)
        thrust10 = self._iwtt_csi_simplified(typical_price, 10)
        
        return thrust1 - thrust10
    
    def _iwtt_csi_simplified(self, data: np.ndarray, period: int) -> np.ndarray:
        """简化的IWTT CSI处理器"""
        # 使用EMA近似IWTT
        alpha = 2.0 / (period + 1)
        result = np.zeros(len(data))
        result[0] = data[0]
        
        for i in range(1, len(data)):
            result[i] = alpha * data[i] + (1 - alpha) * result[i-1]
        
        return result
    
    def _calculate_bands(self, csi_buffer: np.ndarray) -> tuple:
        """计算动态轨道"""
        period = self.cyclic_memory
        percent = self.leveling / 100.0
        
        high_band = np.full(len(csi_buffer), np.nan)
        low_band = np.full(len(csi_buffer), np.nan)
        
        for i in range(period, len(csi_buffer)):
            window = csi_buffer[i-period:i]
            sorted_window = np.sort(window)
            
            # 计算分位数
            low_idx = int(period * percent)
            high_idx = int(period * (1 - percent))
            
            low_band[i] = sorted_window[low_idx] if low_idx < len(sorted_window) else sorted_window[0]
            high_band[i] = sorted_window[high_idx] if high_idx < len(sorted_window) else sorted_window[-1]
        
        return high_band, low_band
    
    def _calculate_momentum(self, csi_buffer: np.ndarray) -> np.ndarray:
        """计算动量"""
        momentum = np.zeros(len(csi_buffer))
        for i in range(1, len(csi_buffer)):
            momentum[i] = csi_buffer[i] - csi_buffer[i-1]
        return momentum
    
    def _detect_divergences(self, csi_buffer: np.ndarray, high_prices: np.ndarray, low_prices: np.ndarray) -> Dict:
        """检测背离（简化版本）"""
        length = len(csi_buffer)
        bull_div = np.zeros(length, dtype=bool)
        bear_div = np.zeros(length, dtype=bool)
        
        # 简化的背离检测
        lookback = 20
        for i in range(lookback, length):
            # 检测看涨背离：价格创新低但指标没有
            if i >= lookback:
                price_window = low_prices[i-lookback:i+1]
                csi_window = csi_buffer[i-lookback:i+1]
                
                if low_prices[i] == np.min(price_window) and csi_buffer[i] > np.min(csi_window):
                    bull_div[i] = True
                
                # 检测看跌背离：价格创新高但指标没有
                price_window = high_prices[i-lookback:i+1]
                if high_prices[i] == np.max(price_window) and csi_buffer[i] < np.max(csi_window):
                    bear_div[i] = True
        
        return {
            'bull_div': bull_div,
            'bear_div': bear_div,
            'hidden_bull_div': np.zeros(length, dtype=bool),
            'hidden_bear_div': np.zeros(length, dtype=bool)
        }
    
    def _calculate_scores(self, csi_buffer: np.ndarray, high_band: np.ndarray, 
                         low_band: np.ndarray, momentum: np.ndarray, divergences: Dict) -> tuple:
        """计算评分"""
        length = len(csi_buffer)
        channel_score = np.zeros(length)
        divergence_score = np.zeros(length)
        
        # 重置状态
        self.last_channel_score = 0.0
        self.last_divergence_score = 0.0
        self.channel_signal_duration = 0
        self.divergence_signal_duration = 0
        
        for i in range(1, length):
            # 轨道位置分数计算
            if (not np.isnan(high_band[i-1]) and not np.isnan(high_band[i]) and
                csi_buffer[i-1] > high_band[i-1] and csi_buffer[i] < high_band[i]):
                channel_score[i] = -100
                self.last_channel_score = -100
                self.channel_signal_duration = 2
            elif (not np.isnan(low_band[i-1]) and not np.isnan(low_band[i]) and
                  csi_buffer[i-1] < low_band[i-1] and csi_buffer[i] > low_band[i]):
                channel_score[i] = 100
                self.last_channel_score = 100
                self.channel_signal_duration = 2
            elif not np.isnan(high_band[i]) and csi_buffer[i] > high_band[i]:
                score = -80 if momentum[i] < 0 else -20
                channel_score[i] = score
                self.last_channel_score = score
                self.channel_signal_duration = 2
            elif not np.isnan(low_band[i]) and csi_buffer[i] < low_band[i]:
                score = 80 if momentum[i] > 0 else 20
                channel_score[i] = score
                self.last_channel_score = score
                self.channel_signal_duration = 2
            else:
                if self.channel_signal_duration > 0:
                    self.channel_signal_duration -= 1
                    channel_score[i] = self.last_channel_score
                else:
                    channel_score[i] = 0
            
            # 背离分数计算
            if divergences['bear_div'][i]:
                divergence_score[i] = -100
                self.last_divergence_score = -100
                self.divergence_signal_duration = 1
            elif divergences['bull_div'][i]:
                divergence_score[i] = 100
                self.last_divergence_score = 100
                self.divergence_signal_duration = 1
            else:
                if self.divergence_signal_duration > 0:
                    self.divergence_signal_duration -= 1
                    divergence_score[i] = self.last_divergence_score
                else:
                    divergence_score[i] = 0
        
        # 计算最终分数
        mmt_score = channel_score * self.channel_weight + divergence_score * self.divergence_weight
        
        return channel_score, divergence_score, mmt_score
    
    def _generate_description(self, score: float, mmt_data: Dict) -> str:
        """生成评分描述"""
        channel = mmt_data['channel_score'][-1]
        divergence = mmt_data['divergence_score'][-1]
        momentum = mmt_data['momentum'][-1]
        
        if score >= 50:
            if divergence > 50:
                return f"强烈买入：检测到看涨背离，动量({momentum:.2f})转正"
            else:
                return f"买入信号：价格突破下轨，上升动能增强"
        elif score <= -50:
            if divergence < -50:
                return f"强烈卖出：检测到看跌背离，动量({momentum:.2f})转负"
            else:
                return f"卖出信号：价格跌破上轨，下降压力增大"
        elif score > 0:
            return f"偏多：动量指标显示上升趋势"
        elif score < 0:
            return f"偏空：动量指标显示下降趋势"
        else:
            return f"中性：动量指标在正常区间内波动"
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """验证输入数据"""
        required_columns = self.get_required_columns()
        
        # 检查必需列
        for col in required_columns:
            if col not in data.columns:
                self.logger.warning(f"缺少必需列: {col}")
                return False
        
        # 检查数据长度
        if len(data) < self.get_min_data_points():
            self.logger.warning(f"数据长度不足: 需要{self.get_min_data_points()}行，实际{len(data)}行")
            return False
        
        # 检查数据质量
        for col in ['close', 'high', 'low']:
            if data[col].isnull().any():
                self.logger.warning(f"{col}列包含空值")
                return False
            
            if (data[col] <= 0).any():
                self.logger.warning(f"{col}列包含非正值")
                return False
        
        # 检查价格逻辑
        if not ((data['high'] >= data['low']).all() and 
                (data['high'] >= data['close']).all() and 
                (data['low'] <= data['close']).all()):
            self.logger.warning("价格数据逻辑错误")
            return False
        
        return True
    
    def get_required_columns(self) -> List[str]:
        """获取必需的数据列"""
        return ['close', 'high', 'low']
    
    def get_min_data_points(self) -> int:
        """获取最小数据点数"""
        return max(50, self.cyclic_memory) + 10