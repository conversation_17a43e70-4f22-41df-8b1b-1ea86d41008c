#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCSI权威版本包装器
将TV-code/py-code中的权威实现包装为与Web应用兼容的计分单元
"""

import sys
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional
import logging
from pathlib import Path

# 添加权威源代码路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent / 'TV-code' / 'py-code'))

logger = logging.getLogger(__name__)

# 简化的ScoringResult类
class ScoringResult:
    """评分结果类"""
    def __init__(self, score: float, signal: str = 'neutral', confidence: float = 0.0, 
                 description: str = '', metadata: Dict = None):
        self.score = score
        self.raw_value = score
        self.signal = signal
        self.confidence = confidence
        self.description = description
        self.metadata = metadata or {}

class AuthorityMCSIMACDUnit:
    """权威版本MCSI MACD单元"""
    
    def __init__(self):
        self.unit_id = 'authority_mcsi_macd'
        self.name = '权威MCSI MACD指标'
        
        # 导入权威源代码实现
        try:
            from mcsi_macd import MCSIMACDIndicator
            self.authority_indicator = MCSIMACDIndicator(
                fast_length=19, slow_length=39, signal_length=9, lookback_period=20
            )
            self.authority_available = True
            logger.info("✅ 成功加载权威MACD实现")
        except ImportError as e:
            logger.error(f"❌ 无法导入权威MACD: {e}")
            self.authority_indicator = None
            self.authority_available = False

    def is_available(self) -> bool:
        """检查是否可用"""
        return self.authority_available

    def calculate_score(self, ohlc: Optional[pd.DataFrame] = None, 
                       data: Optional[pd.DataFrame] = None, **kwargs) -> ScoringResult:
        """计算分数"""
        if not self.authority_available:
            return ScoringResult(0.0, 'neutral', 0.0, '权威MACD不可用')
        
        # 数据获取
        input_data = ohlc if ohlc is not None else data
        if input_data is None or len(input_data) < 68:
            return ScoringResult(0.0, 'neutral', 0.0, '数据不足')
        
        try:
            # 调用权威代码计算
            result = self.authority_indicator.calculate(input_data['close'].values)
            macd_scores = result.get('macd_score', [])
            
            if len(macd_scores) == 0:
                return ScoringResult(0.0, 'neutral', 0.0, '权威MACD计算返回空结果')
            
            latest_score = float(macd_scores[-1])
            latest_score = max(-100.0, min(100.0, latest_score))
            
            # 确定信号类型
            if latest_score >= 50:
                signal = 'bullish'
            elif latest_score <= -50:
                signal = 'bearish'
            else:
                signal = 'neutral'
            
            confidence = min(abs(latest_score) / 100.0, 1.0)
            
            return ScoringResult(
                score=latest_score,
                signal=signal,
                confidence=confidence,
                description=f'权威MCSI MACD: {latest_score:.2f}',
                metadata={
                    'indicator': 'Authority MACD', 
                    'source': 'TV-code/py-code/mcsi_macd.py',
                    'version': 'authority'
                }
            )
            
        except Exception as e:
            logger.error(f"权威MACD计算失败: {e}")
            return ScoringResult(0.0, 'neutral', 0.0, f'计算错误: {str(e)}')

    def score_with_validation(self, data: pd.DataFrame) -> ScoringResult:
        """带验证的评分计算（兼容旧接口）"""
        return self.calculate_score(data=data)
    
    def calculate_full_data(self, ohlc: Optional[pd.DataFrame] = None, 
                           data: Optional[pd.DataFrame] = None, **kwargs) -> Dict:
        """计算权威MCSI MACD完整数据（包含所有Pine Script原生元素）"""
        if not self.authority_available:
            return None
            
        # 数据获取
        input_data = ohlc if ohlc is not None else data
        if input_data is None or len(input_data) < 68:
            return None
        
        try:
            # 调用权威代码计算完整数据
            result = self.authority_indicator.calculate(input_data['close'].values)
            
            if result is not None:
                # 添加额外的Pine Script兼容性标识
                result['pine_script_compatible'] = True
                result['indicator_type'] = 'macd'
                result['has_upper_lower_bands'] = True
                result['has_signals'] = True
                return result
            else:
                return None
                
        except Exception as e:
            logger.error(f"权威MCSI MACD完整数据计算失败: {e}")
            return None

class AuthorityMCSIRSIUnit:
    """权威版本MCSI RSI单元"""
    
    def __init__(self):
        self.unit_id = 'authority_mcsi_rsi'
        self.name = '权威MCSI RSI指标'
        
        # 导入权威源代码实现
        try:
            from mcsi_rsi import MCSIRSIIndicator
            self.authority_indicator = MCSIRSIIndicator(
                dom_cycle=14, vibration=10, leveling=10.0
            )
            self.authority_available = True
            logger.info("✅ 成功加载权威RSI实现")
        except ImportError as e:
            logger.error(f"❌ 无法导入权威RSI: {e}")
            self.authority_indicator = None
            self.authority_available = False

    def is_available(self) -> bool:
        """检查是否可用"""
        return self.authority_available

    def calculate_score(self, ohlc: Optional[pd.DataFrame] = None, 
                       data: Optional[pd.DataFrame] = None, **kwargs) -> ScoringResult:
        """计算分数"""
        if not self.authority_available:
            return ScoringResult(0.0, 'neutral', 0.0, '权威RSI不可用')
        
        # 数据获取
        input_data = ohlc if ohlc is not None else data
        if input_data is None or len(input_data) < 56:
            return ScoringResult(0.0, 'neutral', 0.0, '数据不足')
        
        try:
            # 准备完整的数据字典
            data_dict = {
                'close': input_data['close'].values,
                'open': input_data['open'].values,
                'high': input_data['high'].values,
                'low': input_data['low'].values,
                'date': [f"2024-01-{i+1:02d}" for i in range(len(input_data))]
            }
            
            # 调用权威代码计算
            result = self.authority_indicator.calculate(data_dict)
            rsi_scores = result.get('rsi_score', [])
            
            if len(rsi_scores) == 0:
                return ScoringResult(0.0, 'neutral', 0.0, '权威RSI计算返回空结果')
            
            latest_score = float(rsi_scores[-1])
            latest_score = max(-100.0, min(100.0, latest_score))
            
            # 确定信号类型（基于RSI的67/-67突破逻辑）
            if latest_score >= 67:
                signal = 'bullish'
            elif latest_score <= -67:
                signal = 'bearish'
            elif latest_score >= 27:
                signal = 'weak_bullish'
            elif latest_score <= -27:
                signal = 'weak_bearish'
            else:
                signal = 'neutral'
            
            confidence = min(abs(latest_score) / 100.0, 1.0)
            
            return ScoringResult(
                score=latest_score,
                signal=signal,
                confidence=confidence,
                description=f'权威MCSI RSI: {latest_score:.2f}',
                metadata={
                    'indicator': 'Authority RSI', 
                    'source': 'TV-code/py-code/mcsi_rsi.py',
                    'version': 'authority'
                }
            )
            
        except Exception as e:
            logger.error(f"权威RSI计算失败: {e}")
            return ScoringResult(0.0, 'neutral', 0.0, f'计算错误: {str(e)}')

    def score_with_validation(self, data: pd.DataFrame) -> ScoringResult:
        """带验证的评分计算（兼容旧接口）"""
        return self.calculate_score(data=data)
    
    def calculate_full_data(self, ohlc: Optional[pd.DataFrame] = None, 
                           data: Optional[pd.DataFrame] = None, **kwargs) -> Dict:
        """计算权威MCSI RSI完整数据（包含所有Pine Script原生元素）"""
        if not self.authority_available:
            return None
            
        # 数据获取
        input_data = ohlc if ohlc is not None else data
        if input_data is None or len(input_data) < 56:
            return None
        
        try:
            # 准备完整的数据字典
            data_dict = {
                'close': input_data['close'].values,
                'open': input_data['open'].values,
                'high': input_data['high'].values,
                'low': input_data['low'].values,
                'date': [f"2024-01-{i+1:02d}" for i in range(len(input_data))]
            }
            
            # 调用权威代码计算完整数据
            result = self.authority_indicator.calculate(data_dict)
            
            if result is not None:
                # 添加额外的Pine Script兼容性标识
                result['pine_script_compatible'] = True
                result['indicator_type'] = 'rsi'
                result['has_upper_lower_bands'] = True
                result['has_signals'] = True
                return result
            else:
                return None
                
        except Exception as e:
            logger.error(f"权威MCSI RSI完整数据计算失败: {e}")
            return None

class AuthorityMCSIMMTUnit:
    """权威版本MCSI MMT单元"""
    
    def __init__(self):
        self.unit_id = 'authority_mcsi_mmt'
        self.name = '权威MCSI MMT指标'
        
        # 导入权威源代码实现
        try:
            from mcsi_mmt import MCSIMMTIndicator
            self.authority_indicator = MCSIMMTIndicator(
                leveling=10, cyclic_memory=34, 
                channel_weight=0.5, divergence_weight=0.5
            )
            self.authority_available = True
            logger.info("✅ 成功加载权威MMT实现")
        except ImportError as e:
            logger.error(f"❌ 无法导入权威MMT: {e}")
            self.authority_indicator = None
            self.authority_available = False

    def is_available(self) -> bool:
        """检查是否可用"""
        return self.authority_available

    def calculate_score(self, ohlc: Optional[pd.DataFrame] = None, 
                       data: Optional[pd.DataFrame] = None, **kwargs) -> ScoringResult:
        """计算分数"""
        if not self.authority_available:
            return ScoringResult(0.0, 'neutral', 0.0, '权威MMT不可用')
        
        # 数据获取
        input_data = ohlc if ohlc is not None else data
        if input_data is None or len(input_data) < 84:  # 至少需要足够的数据
            return ScoringResult(0.0, 'neutral', 0.0, '数据不足')
        
        try:
            # 调用权威代码计算（MMT需要HLC数据）
            result = self.authority_indicator.calculate(
                input_data['close'].values,
                input_data['high'].values,
                input_data['low'].values
            )
            mmt_scores = result.get('mmt_score', [])
            
            if len(mmt_scores) == 0:
                return ScoringResult(0.0, 'neutral', 0.0, '权威MMT计算返回空结果')
            
            latest_score = float(mmt_scores[-1])
            latest_score = max(-100.0, min(100.0, latest_score))
            
            # 确定信号类型
            if latest_score >= 85:
                signal = 'strong_bullish'
            elif latest_score >= 50:
                signal = 'bullish'
            elif latest_score <= -85:
                signal = 'strong_bearish'
            elif latest_score <= -50:
                signal = 'bearish'
            else:
                signal = 'neutral'
            
            confidence = min(abs(latest_score) / 100.0, 1.0)
            
            return ScoringResult(
                score=latest_score,
                signal=signal,
                confidence=confidence,
                description=f'权威MCSI MMT: {latest_score:.2f}',
                metadata={
                    'indicator': 'Authority MMT', 
                    'source': 'TV-code/py-code/mcsi_mmt.py',
                    'version': 'authority'
                }
            )
            
        except Exception as e:
            logger.error(f"权威MMT计算失败: {e}")
            return ScoringResult(0.0, 'neutral', 0.0, f'计算错误: {str(e)}')

    def score_with_validation(self, data: pd.DataFrame) -> ScoringResult:
        """带验证的评分计算（兼容旧接口）"""
        return self.calculate_score(data=data)
    
    def calculate_full_data(self, ohlc: Optional[pd.DataFrame] = None, 
                           data: Optional[pd.DataFrame] = None, **kwargs) -> Dict:
        """计算权威MCSI MMT完整数据（包含所有Pine Script原生元素）"""
        if not self.authority_available:
            return None
            
        # 数据获取
        input_data = ohlc if ohlc is not None else data
        if input_data is None or len(input_data) < 84:
            return None
        
        try:
            # 调用权威代码计算完整数据（MMT需要HLC数据）
            result = self.authority_indicator.calculate(
                input_data['close'].values,
                input_data['high'].values,
                input_data['low'].values
            )
            
            if result is not None:
                # 添加额外的Pine Script兼容性标识
                result['pine_script_compatible'] = True
                result['indicator_type'] = 'mmt'
                result['has_upper_lower_bands'] = True
                result['has_divergence_signals'] = True
                return result
            else:
                return None
                
        except Exception as e:
            logger.error(f"权威MCSI MMT完整数据计算失败: {e}")
            return None

class AuthorityMCSITTMUnit:
    """权威版本MCSI TTM单元"""
    
    def __init__(self):
        self.unit_id = 'authority_mcsi_ttm'
        self.name = '权威MCSI TTM指标'
        
        # 导入权威源代码实现
        try:
            from mcsi_ttm import MCSITTMIndicator
            self.authority_indicator = MCSITTMIndicator(comparison_period=4)
            self.authority_available = True
            logger.info("✅ 成功加载权威TTM实现")
        except ImportError as e:
            logger.error(f"❌ 无法导入权威TTM: {e}")
            self.authority_indicator = None
            self.authority_available = False

    def is_available(self) -> bool:
        """检查是否可用"""
        return self.authority_available

    def calculate_score(self, ohlc: Optional[pd.DataFrame] = None, 
                       data: Optional[pd.DataFrame] = None, **kwargs) -> ScoringResult:
        """计算分数"""
        if not self.authority_available:
            return ScoringResult(0.0, 'neutral', 0.0, '权威TTM不可用')
        
        # 数据获取
        input_data = ohlc if ohlc is not None else data
        if input_data is None or len(input_data) < 20:
            return ScoringResult(0.0, 'neutral', 0.0, '数据不足')
        
        try:
            # 调用权威代码计算
            result = self.authority_indicator.calculate(input_data['close'].values)
            ttm_scores = result.get('ttm_score', [])
            
            if len(ttm_scores) == 0:
                return ScoringResult(0.0, 'neutral', 0.0, '权威TTM计算返回空结果')
            
            latest_score = float(ttm_scores[-1])
            latest_score = max(-100.0, min(100.0, latest_score))
            
            # 确定信号类型（基于TTM的TD序列逻辑）
            if latest_score >= 100:
                signal = 'extreme_bullish'
            elif latest_score >= 80:
                signal = 'strong_bullish'
            elif latest_score <= -100:
                signal = 'extreme_bearish'
            elif latest_score <= -80:
                signal = 'strong_bearish'
            else:
                signal = 'neutral'
            
            confidence = min(abs(latest_score) / 100.0, 1.0)
            
            return ScoringResult(
                score=latest_score,
                signal=signal,
                confidence=confidence,
                description=f'权威MCSI TTM: {latest_score:.2f}',
                metadata={
                    'indicator': 'Authority TTM', 
                    'source': 'TV-code/py-code/mcsi_ttm.py',
                    'version': 'authority'
                }
            )
            
        except Exception as e:
            logger.error(f"权威TTM计算失败: {e}")
            return ScoringResult(0.0, 'neutral', 0.0, f'计算错误: {str(e)}')

    def score_with_validation(self, data: pd.DataFrame) -> ScoringResult:
        """带验证的评分计算（兼容旧接口）"""
        return self.calculate_score(data=data)
    
    def calculate_full_data(self, ohlc: Optional[pd.DataFrame] = None, 
                           data: Optional[pd.DataFrame] = None, **kwargs) -> Dict:
        """计算权威MCSI TTM完整数据（包含所有Pine Script原生元素）"""
        if not self.authority_available:
            return None
            
        # 数据获取
        input_data = ohlc if ohlc is not None else data
        if input_data is None or len(input_data) < 20:
            return None
        
        try:
            # 调用权威代码计算完整数据
            result = self.authority_indicator.calculate(input_data['close'].values)
            
            if result is not None:
                # 添加额外的Pine Script兼容性标识
                result['pine_script_compatible'] = True
                result['indicator_type'] = 'ttm'
                result['has_td_sequences'] = True
                result['has_signals'] = True
                return result
            else:
                return None
                
        except Exception as e:
            logger.error(f"权威MCSI TTM完整数据计算失败: {e}")
            return None

# 检查权威单元可用性
AUTHORITY_MCSI_AVAILABLE = True
try:
    # 尝试实例化所有权威单元
    _test_auth_macd = AuthorityMCSIMACDUnit()
    _test_auth_rsi = AuthorityMCSIRSIUnit()
    _test_auth_mmt = AuthorityMCSIMMTUnit()
    _test_auth_ttm = AuthorityMCSITTMUnit()
    
    if not all([_test_auth_macd.is_available(), _test_auth_rsi.is_available(), 
                _test_auth_mmt.is_available(), _test_auth_ttm.is_available()]):
        AUTHORITY_MCSI_AVAILABLE = False
        logger.warning("⚠️ 部分权威MCSI单元不可用")
    else:
        logger.info("✅ 所有权威MCSI单元可用")
        
except Exception as e:
    AUTHORITY_MCSI_AVAILABLE = False
    logger.error(f"❌ 权威MCSI单元初始化失败: {e}")