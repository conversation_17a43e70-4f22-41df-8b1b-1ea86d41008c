#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCSI MMT评分单元（修正版）
完全复制原始逻辑，确保与基准一致
"""

import numpy as np
import pandas as pd
from typing import List, Optional, Dict, Any, Tuple
import logging
from .base_scoring_unit import BaseScoringUnit, ScoringResult

class MCSIMMTScoringUnit(BaseScoringUnit):
    """MCSI MMT动量技术指标评分单元（精确实现）"""
    
    def __init__(self):
        """初始化MCSI MMT评分单元"""
        super().__init__(
            unit_id='mcsi_mmt',
            name='MCSI MMT评分单元',
            description='基于动量和背离检测的评分系统',
            min_score=-100.0,
            max_score=100.0,
            enabled=True
        )
        
        # MMT参数（与原始完全一致）
        self.leveling = 10
        self.cyclic_memory = 34
        self.channel_weight = 0.5
        self.divergence_weight = 0.5
        
        # 状态变量（对应Pine Script的var变量）
        self.last_channel_score = 0.0
        self.last_divergence_score = 0.0
        self.channel_signal_duration = 0
        self.divergence_signal_duration = 0
        
    def calculate_score(self, data: pd.DataFrame, seasonal_factors: Optional[Dict] = None) -> ScoringResult:
        """
        计算MCSI MMT评分
        
        Args:
            data: 包含OHLCV数据的DataFrame
            seasonal_factors: 可选的季节性因子
            
        Returns:
            ScoringResult对象
        """
        try:
            # 验证数据
            if not self.validate_data(data):
                return ScoringResult(
                    score=0.0,
                    signal='neutral',
                    confidence=0.0,
                    description='数据验证失败'
                )
            
            # 提取价格数据
            close_prices = data['close'].values
            high_prices = data['high'].values
            low_prices = data['low'].values
            
            # 使用原始逻辑计算MMT
            mmt_data = self._calculate_mmt_original(close_prices, high_prices, low_prices)
            
            # 获取最新分数
            latest_score = float(mmt_data['mmt_score'][-1])
            
            # 确定信号类型
            if latest_score > 50:
                signal = 'bullish'
            elif latest_score < -50:
                signal = 'bearish'
            else:
                signal = 'neutral'
            
            # 计算置信度
            confidence = min(abs(latest_score) / 100.0, 1.0)
            
            # 生成描述
            description = self._generate_description(latest_score, mmt_data)
            
            # 准备元数据
            metadata = {
                'channel_score': float(mmt_data['channel_score'][-1]),
                'divergence_score': float(mmt_data['divergence_score'][-1]),
                'momentum': float(mmt_data['momentum'][-1]),
                'csi_buffer': float(mmt_data['csi_buffer'][-1]),
                'score_history': mmt_data['mmt_score'][-10:].tolist()
            }
            
            return ScoringResult(
                score=latest_score,
                raw_value=float(mmt_data['momentum'][-1]),
                signal=signal,
                confidence=confidence,
                description=description,
                metadata=metadata
            )
            
        except Exception as e:
            self.logger.error(f"MCSI MMT评分计算失败: {str(e)}")
            return ScoringResult(
                score=0.0,
                signal='neutral',
                confidence=0.0,
                description=f'计算错误: {str(e)}'
            )
    
    def _calculate_mmt_original(self, close_prices: np.ndarray, high_prices: np.ndarray, low_prices: np.ndarray) -> Dict:
        """
        使用原始逻辑计算MMT（完全复制）
        """
        # 计算CSI缓冲区（使用原始IWTT）
        thrust1 = self._iwtt_csi_processor(close_prices, 1)
        thrust2 = self._iwtt_csi_processor(close_prices, 10)
        csi_buffer = thrust1 - thrust2
        
        # 计算动态轨道（使用原始banding）
        high_band, low_band = self._banding(csi_buffer, self.cyclic_memory, self.leveling)
        
        # 计算动量
        momentum = self._calculate_momentum(csi_buffer)
        
        # 检测背离（使用原始逻辑）
        divergences = self._detect_divergences(csi_buffer, high_prices, low_prices)
        
        # 重置状态变量
        self.last_channel_score = 0.0
        self.last_divergence_score = 0.0
        self.channel_signal_duration = 0
        self.divergence_signal_duration = 0
        
        # 计算评分（使用原始逻辑）
        scores_result = self._calculate_scores_original(
            csi_buffer, high_band, low_band, momentum, divergences
        )
        
        return scores_result
    
    def _cycle1(self, i: int, wave_throttle: float, cycs: int) -> float:
        """Cycle1函数（原始）"""
        ret = 6.0 * wave_throttle + 1.0
        if i == 0:
            ret = 1.0 + wave_throttle
        elif i == 1:
            ret = 1.0 + wave_throttle * 5.0
        elif i == (cycs - 1):
            ret = 1.0 + wave_throttle
        elif i == (cycs - 2):
            ret = 1.0 + wave_throttle * 5.0
        return ret
    
    def _cycle2(self, i: int, wave_throttle: float, cycs: int) -> float:
        """Cycle2函数（原始）"""
        ret = -4.0 * wave_throttle
        if i == 0:
            ret = -2.0 * wave_throttle
        elif i == (cycs - 1):
            ret = 0.0
        elif i == (cycs - 2):
            ret = -2.0 * wave_throttle
        return ret
    
    def _cycle3(self, i: int, wave_throttle: float, cycs: int) -> float:
        """Cycle3函数（原始）"""
        ret = wave_throttle
        if i == (cycs - 1):
            ret = 0.0
        elif i == (cycs - 2):
            ret = 0.0
        return ret
    
    def _iwtt_csi_processor(self, close_prices: np.ndarray, cycle_count: int) -> np.ndarray:
        """iWTT CSI处理器（原始实现）"""
        result = np.zeros(len(close_prices))
        cycs = 50
        wave_throttle = float(160 * cycle_count)
        
        for idx in range(cycs, len(close_prices)):
            wtt1, wtt2, wtt3, wtt4, wtt5 = 0.0, 0.0, 0.0, 0.0, 0.0
            _wtt1, _wtt2, _wtt3, _wtt4, _wtt5 = 0.0, 0.0, 0.0, 0.0, 0.0
            current_val = 0.0
            
            for i in range(cycs):
                swing = self._cycle1(i, wave_throttle, cycs) - wtt4 * wtt1 - _wtt5 * _wtt2
                if swing == 0:
                    break
                    
                momentum = self._cycle2(i, wave_throttle, cycs)
                _wtt1 = wtt1
                wtt1 = (momentum - wtt4 * wtt2) / swing
                
                acceleration = self._cycle3(i, wave_throttle, cycs)
                _wtt2 = wtt2
                wtt2 = acceleration / swing
                
                current_val = (close_prices[idx - (49 - i)] - _wtt3 * _wtt5 - wtt3 * wtt4) / swing
                _wtt3 = wtt3
                wtt3 = current_val
                wtt4 = momentum - wtt5 * _wtt1
                _wtt5 = wtt5
                wtt5 = acceleration
                
            result[idx] = current_val
            
        return result
    
    def _banding(self, crsi: np.ndarray, period: int, leveling: float) -> Tuple[np.ndarray, np.ndarray]:
        """计算动态轨道（原始实现）"""
        percent = leveling / 100.0
        high_band = np.full(len(crsi), np.nan)
        low_band = np.full(len(crsi), np.nan)
        
        for idx in range(period, len(crsi)):
            # 获取当前周期的数据
            period_data = crsi[idx-period+1:idx+1]
            maxima = np.max(period_data)
            minima = np.min(period_data)
            
            if maxima == minima:
                high_band[idx] = maxima
                low_band[idx] = minima
                continue
                
            step_factor = (maxima - minima) / 100.0
            
            # 计算低轨
            for steps in range(101):
                test_value = minima + step_factor * steps
                below_count = np.sum(period_data < test_value)
                if below_count / period >= percent:
                    low_band[idx] = test_value
                    break
            
            # 计算高轨
            for steps in range(101):
                test_value = maxima - step_factor * steps
                above_count = np.sum(period_data >= test_value)
                if above_count / period >= percent:
                    high_band[idx] = test_value
                    break
                    
        return high_band, low_band
    
    def _calculate_momentum(self, csi_buffer: np.ndarray) -> np.ndarray:
        """计算动量（原始）"""
        momentum = np.zeros(len(csi_buffer))
        for i in range(1, len(csi_buffer)):
            momentum[i] = csi_buffer[i] - csi_buffer[i-1]
        return momentum
    
    def _find_pivots(self, data: np.ndarray, left_bars: int, right_bars: int) -> Tuple[np.ndarray, np.ndarray]:
        """寻找枢轴点（原始实现）"""
        pivot_lows = np.zeros(len(data), dtype=bool)
        pivot_highs = np.zeros(len(data), dtype=bool)
        
        for i in range(left_bars + right_bars, len(data)):
            pivot_idx = i - right_bars
            
            # 检查是否为低点
            is_low = True
            for j in range(pivot_idx - left_bars, pivot_idx + right_bars + 1):
                if j != pivot_idx and j >= 0 and j < len(data):
                    if data[j] <= data[pivot_idx]:
                        is_low = False
                        break
            if is_low:
                pivot_lows[i] = True
            
            # 检查是否为高点
            is_high = True
            for j in range(pivot_idx - left_bars, pivot_idx + right_bars + 1):
                if j != pivot_idx and j >= 0 and j < len(data):
                    if data[j] >= data[pivot_idx]:
                        is_high = False
                        break
            if is_high:
                pivot_highs[i] = True
        
        return pivot_lows, pivot_highs
    
    def _bars_since(self, condition: np.ndarray, current_idx: int) -> int:
        """实现Pine Script的ta.barssince()函数"""
        for i in range(current_idx - 1, -1, -1):
            if condition[i]:
                return current_idx - i
        return current_idx + 1
    
    def _in_range(self, condition: np.ndarray, current_idx: int, range_lower: int, range_upper: int) -> bool:
        """实现Pine Script的_inRange()函数"""
        bars = self._bars_since(condition, current_idx)
        return range_lower <= bars <= range_upper
    
    def _detect_divergences(self, csi_buffer: np.ndarray, high_prices: np.ndarray,
                          low_prices: np.ndarray, lb_l: int = 5, lb_r: int = 2,
                          range_upper: int = 60, range_lower: int = 5) -> Dict:
        """检测背离（原始实现）"""
        pivot_lows, pivot_highs = self._find_pivots(csi_buffer, lb_l, lb_r)
        
        bull_div = np.zeros(len(csi_buffer), dtype=bool)
        bear_div = np.zeros(len(csi_buffer), dtype=bool)
        hidden_bull_div = np.zeros(len(csi_buffer), dtype=bool)
        hidden_bear_div = np.zeros(len(csi_buffer), dtype=bool)
        
        for i in range(range_upper + lb_r, len(csi_buffer)):
            if pivot_lows[i]:
                if self._in_range(pivot_lows, i, range_lower, range_upper):
                    # 寻找前一个低点
                    prev_pivot_idx = None
                    for j in range(i - 1, -1, -1):
                        if pivot_lows[j]:
                            prev_pivot_idx = j
                            break
                    
                    if prev_pivot_idx is not None:
                        actual_prev_idx = prev_pivot_idx - lb_r
                        actual_curr_idx = i - lb_r
                        
                        if actual_prev_idx >= 0 and actual_curr_idx >= 0:
                            # 看涨背离
                            if (csi_buffer[actual_curr_idx] > csi_buffer[actual_prev_idx] and
                                low_prices[actual_curr_idx] < low_prices[actual_prev_idx]):
                                bull_div[i] = True
                            
                            # 隐藏看涨背离
                            if (csi_buffer[actual_curr_idx] < csi_buffer[actual_prev_idx] and
                                low_prices[actual_curr_idx] > low_prices[actual_prev_idx]):
                                hidden_bull_div[i] = True
            
            if pivot_highs[i]:
                if self._in_range(pivot_highs, i, range_lower, range_upper):
                    # 寻找前一个高点
                    prev_pivot_idx = None
                    for j in range(i - 1, -1, -1):
                        if pivot_highs[j]:
                            prev_pivot_idx = j
                            break
                    
                    if prev_pivot_idx is not None:
                        actual_prev_idx = prev_pivot_idx - lb_r
                        actual_curr_idx = i - lb_r
                        
                        if actual_prev_idx >= 0 and actual_curr_idx >= 0:
                            # 看跌背离
                            if (csi_buffer[actual_curr_idx] < csi_buffer[actual_prev_idx] and
                                high_prices[actual_curr_idx] > high_prices[actual_prev_idx]):
                                bear_div[i] = True
                            
                            # 隐藏看跌背离
                            if (csi_buffer[actual_curr_idx] > csi_buffer[actual_prev_idx] and
                                high_prices[actual_curr_idx] < high_prices[actual_prev_idx]):
                                hidden_bear_div[i] = True
        
        return {
            'bull_div': bull_div,
            'bear_div': bear_div,
            'hidden_bull_div': hidden_bull_div,
            'hidden_bear_div': hidden_bear_div
        }
    
    def _calculate_scores_original(self, csi_buffer, high_band, low_band, momentum, divergences):
        """计算评分（完全复制原始逻辑）"""
        channel_score = np.zeros(len(csi_buffer))
        divergence_score = np.zeros(len(csi_buffer))
        
        # 重置状态变量
        self.last_channel_score = 0.0
        self.last_divergence_score = 0.0
        self.channel_signal_duration = 0
        self.divergence_signal_duration = 0
        
        for i in range(1, len(csi_buffer)):
            # === 轨道位置分数计算（严格按照原始逻辑）===
            if (not np.isnan(high_band[i-1]) and not np.isnan(high_band[i]) and
                csi_buffer[i-1] > high_band[i-1] and csi_buffer[i] < high_band[i]):
                # 从高轨上方跌落到高轨下方
                channel_score[i] = -100
                self.last_channel_score = -100
                self.channel_signal_duration = 2
            elif (not np.isnan(low_band[i-1]) and not np.isnan(low_band[i]) and
                  csi_buffer[i-1] < low_band[i-1] and csi_buffer[i] > low_band[i]):
                # 从低轨下方突破到低轨上方
                channel_score[i] = 100
                self.last_channel_score = 100
                self.channel_signal_duration = 2
            elif not np.isnan(high_band[i]) and csi_buffer[i] > high_band[i]:
                # 在高轨之上
                score = -80 if momentum[i] < 0 else -20
                channel_score[i] = score
                self.last_channel_score = score
                self.channel_signal_duration = 2
            elif not np.isnan(low_band[i]) and csi_buffer[i] < low_band[i]:
                # 在低轨之下
                score = 80 if momentum[i] > 0 else 20
                channel_score[i] = score
                self.last_channel_score = score
                self.channel_signal_duration = 2
            else:
                # 在轨道内或信号持续
                if self.channel_signal_duration > 0:
                    self.channel_signal_duration -= 1
                    channel_score[i] = self.last_channel_score
                else:
                    channel_score[i] = 0
            
            # === 背离分数计算 ===
            if divergences['bear_div'][i]:
                divergence_score[i] = -100
                self.last_divergence_score = -100
                self.divergence_signal_duration = 1
            elif divergences['bull_div'][i]:
                divergence_score[i] = 100
                self.last_divergence_score = 100
                self.divergence_signal_duration = 1
            elif divergences['hidden_bear_div'][i]:
                divergence_score[i] = -70
                self.last_divergence_score = -70
                self.divergence_signal_duration = 1
            elif divergences['hidden_bull_div'][i]:
                divergence_score[i] = 70
                self.last_divergence_score = 70
                self.divergence_signal_duration = 1
            else:
                if self.divergence_signal_duration > 0:
                    self.divergence_signal_duration -= 1
                    divergence_score[i] = self.last_divergence_score
                else:
                    divergence_score[i] = 0
        
        # 最终分数计算
        mmt_score = channel_score * self.channel_weight + divergence_score * self.divergence_weight
        
        return {
            'csi_buffer': csi_buffer,
            'high_band': high_band,
            'low_band': low_band,
            'momentum': momentum,
            'channel_score': channel_score,
            'divergence_score': divergence_score,
            'mmt_score': mmt_score,
            'bull_div': divergences['bull_div'],
            'bear_div': divergences['bear_div'],
            'hidden_bull_div': divergences['hidden_bull_div'],
            'hidden_bear_div': divergences['hidden_bear_div']
        }
    
    def _generate_description(self, score: float, mmt_data: Dict) -> str:
        """生成评分描述"""
        channel = mmt_data['channel_score'][-1]
        divergence = mmt_data['divergence_score'][-1]
        momentum = mmt_data['momentum'][-1]
        
        if score >= 50:
            if divergence > 50:
                return f"强烈买入：检测到看涨背离，动量({momentum:.2f})转正"
            else:
                return f"买入信号：价格突破下轨，上升动能增强"
        elif score <= -50:
            if divergence < -50:
                return f"强烈卖出：检测到看跌背离，动量({momentum:.2f})转负"
            else:
                return f"卖出信号：价格跌破上轨，下降压力增大"
        elif score > 0:
            return f"偏多：动量指标显示上升趋势"
        elif score < 0:
            return f"偏空：动量指标显示下降趋势"
        else:
            return f"中性：动量指标在正常区间内波动"
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """验证输入数据"""
        required_columns = self.get_required_columns()
        
        for col in required_columns:
            if col not in data.columns:
                self.logger.warning(f"缺少必需列: {col}")
                return False
        
        if len(data) < self.get_min_data_points():
            self.logger.warning(f"数据长度不足: 需要{self.get_min_data_points()}行，实际{len(data)}行")
            return False
        
        for col in ['close', 'high', 'low']:
            if data[col].isnull().any():
                self.logger.warning(f"{col}列包含空值")
                return False
            
            if (data[col] <= 0).any():
                self.logger.warning(f"{col}列包含非正值")
                return False
        
        if not ((data['high'] >= data['low']).all() and 
                (data['high'] >= data['close']).all() and 
                (data['low'] <= data['close']).all()):
            self.logger.warning("价格数据逻辑错误")
            return False
        
        return True
    
    def get_required_columns(self) -> List[str]:
        """获取必需的数据列"""
        return ['close', 'high', 'low']
    
    def get_min_data_points(self) -> int:
        """获取最小数据点数"""
        return max(50, self.cyclic_memory) + 10