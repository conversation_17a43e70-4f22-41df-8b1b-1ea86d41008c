#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCSI统一接口直接实现
避免相对导入问题，直接使用Python统一接口
"""

import sys
import numpy as np
import pandas as pd
from typing import List, Optional, Dict, Any, Union
import logging
from pathlib import Path

# 添加源代码路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent / 'TV-code' / 'py-code'))

logger = logging.getLogger(__name__)

# 简化的ScoringResult类
class ScoringResult:
    """评分结果类"""
    def __init__(self, score: float, signal: str = 'neutral', confidence: float = 0.0, 
                 description: str = '', metadata: Dict = None):
        self.score = score
        self.raw_value = score
        self.signal = signal
        self.confidence = confidence
        self.description = description
        self.metadata = metadata or {}

class MCSIUnifiedMACDUnit:
    """MCSI统一接口MACD单元"""
    
    def __init__(self):
        self.unit_id = 'mcsi_unified_macd'
        self.name = 'MCSI统一接口MACD指标'
        
        # 导入源代码实现
        try:
            from mcsi_macd import MCSIMACDIndicator
            self.source_indicator = MCSIMACDIndicator(
                fast_length=19, slow_length=39, signal_length=9, lookback_period=20
            )
            self.source_available = True
            logger.info("✅ 成功加载MACD源代码实现")
        except ImportError as e:
            logger.error(f"❌ 无法导入MACD源代码: {e}")
            self.source_indicator = None
            self.source_available = False

    def is_available(self) -> bool:
        """检查是否可用"""
        return self.source_available

    def calculate_score(self, ohlc: Optional[pd.DataFrame] = None, 
                       data: Optional[pd.DataFrame] = None, **kwargs) -> ScoringResult:
        """计算分数"""
        if not self.source_available:
            return ScoringResult(0.0, 'neutral', 0.0, 'MACD源代码不可用')
        
        # 数据获取
        input_data = ohlc if ohlc is not None else data
        if input_data is None or len(input_data) < 68:
            return ScoringResult(0.0, 'neutral', 0.0, '数据不足')
        
        try:
            # 调用源代码计算
            result = self.source_indicator.calculate(input_data['close'].values)
            macd_scores = result.get('macd_score', [])
            
            if len(macd_scores) == 0:
                return ScoringResult(0.0, 'neutral', 0.0, 'MACD计算返回空结果')
            
            latest_score = float(macd_scores[-1])
            latest_score = max(-100.0, min(100.0, latest_score))
            
            # 确定信号类型
            if latest_score >= 50:
                signal = 'bullish'
            elif latest_score <= -50:
                signal = 'bearish'
            else:
                signal = 'neutral'
            
            confidence = min(abs(latest_score) / 100.0, 1.0)
            
            return ScoringResult(
                score=latest_score,
                signal=signal,
                confidence=confidence,
                description=f'MCSI MACD分析: {latest_score:.2f}',
                metadata={'indicator': 'MACD', 'source': 'TV-code/py-code/mcsi_macd.py'}
            )
            
        except Exception as e:
            logger.error(f"MACD计算失败: {e}")
            return ScoringResult(0.0, 'neutral', 0.0, f'计算错误: {str(e)}')

    def score_with_validation(self, data: pd.DataFrame) -> ScoringResult:
        """带验证的评分计算（兼容旧接口）"""
        return self.calculate_score(data=data)

class MCSIUnifiedRSIUnit:
    """MCSI统一接口RSI单元"""
    
    def __init__(self):
        self.unit_id = 'mcsi_unified_rsi'
        self.name = 'MCSI统一接口RSI指标'
        
        # 导入源代码实现
        try:
            from mcsi_rsi import MCSIRSIIndicator
            self.source_indicator = MCSIRSIIndicator(
                dom_cycle=14, vibration=10, leveling=10.0
            )
            self.source_available = True
            logger.info("✅ 成功加载RSI源代码实现")
        except ImportError as e:
            logger.error(f"❌ 无法导入RSI源代码: {e}")
            self.source_indicator = None
            self.source_available = False

    def is_available(self) -> bool:
        """检查是否可用"""
        return self.source_available

    def calculate_score(self, ohlc: Optional[pd.DataFrame] = None, 
                       data: Optional[pd.DataFrame] = None, **kwargs) -> ScoringResult:
        """计算分数"""
        if not self.source_available:
            return ScoringResult(0.0, 'neutral', 0.0, 'RSI源代码不可用')
        
        # 数据获取
        input_data = ohlc if ohlc is not None else data
        if input_data is None or len(input_data) < 56:
            return ScoringResult(0.0, 'neutral', 0.0, '数据不足')
        
        try:
            # 调用源代码计算
            result = self.source_indicator.calculate(input_data['close'].values)
            rsi_scores = result.get('rsi_score', [])
            
            if len(rsi_scores) == 0:
                return ScoringResult(0.0, 'neutral', 0.0, 'RSI计算返回空结果')
            
            latest_score = float(rsi_scores[-1])
            latest_score = max(-100.0, min(100.0, latest_score))
            
            # 确定信号类型（基于RSI的67/-67突破逻辑）
            if latest_score >= 67:
                signal = 'bullish'
            elif latest_score <= -67:
                signal = 'bearish'
            elif latest_score >= 27:
                signal = 'weak_bullish'
            elif latest_score <= -27:
                signal = 'weak_bearish'
            else:
                signal = 'neutral'
            
            confidence = min(abs(latest_score) / 100.0, 1.0)
            
            return ScoringResult(
                score=latest_score,
                signal=signal,
                confidence=confidence,
                description=f'MCSI RSI分析: {latest_score:.2f}',
                metadata={'indicator': 'RSI', 'source': 'TV-code/py-code/mcsi_rsi.py'}
            )
            
        except Exception as e:
            logger.error(f"RSI计算失败: {e}")
            return ScoringResult(0.0, 'neutral', 0.0, f'计算错误: {str(e)}')

    def score_with_validation(self, data: pd.DataFrame) -> ScoringResult:
        """带验证的评分计算（兼容旧接口）"""
        return self.calculate_score(data=data)

class MCSIUnifiedMMTUnit:
    """MCSI统一接口MMT单元"""
    
    def __init__(self):
        self.unit_id = 'mcsi_unified_mmt'
        self.name = 'MCSI统一接口MMT指标'
        
        # 导入源代码实现
        try:
            from mcsi_mmt import MCSIMMTIndicator
            self.source_indicator = MCSIMMTIndicator(
                cyclic_memory=28, leveling=10.0
            )
            self.source_available = True
            logger.info("✅ 成功加载MMT源代码实现")
        except ImportError as e:
            logger.error(f"❌ 无法导入MMT源代码: {e}")
            self.source_indicator = None
            self.source_available = False

    def is_available(self) -> bool:
        """检查是否可用"""
        return self.source_available

    def calculate_score(self, ohlc: Optional[pd.DataFrame] = None, 
                       data: Optional[pd.DataFrame] = None, **kwargs) -> ScoringResult:
        """计算分数"""
        if not self.source_available:
            return ScoringResult(0.0, 'neutral', 0.0, 'MMT源代码不可用')
        
        # 数据获取
        input_data = ohlc if ohlc is not None else data
        if input_data is None or len(input_data) < 48:
            return ScoringResult(0.0, 'neutral', 0.0, '数据不足')
        
        try:
            # 调用源代码计算（MMT需要HLC数据）
            result = self.source_indicator.calculate(
                input_data['close'].values,
                input_data['high'].values,
                input_data['low'].values
            )
            mmt_scores = result.get('mmt_score', [])
            
            if len(mmt_scores) == 0:
                return ScoringResult(0.0, 'neutral', 0.0, 'MMT计算返回空结果')
            
            latest_score = float(mmt_scores[-1])
            latest_score = max(-100.0, min(100.0, latest_score))
            
            # 确定信号类型
            if latest_score >= 85:
                signal = 'strong_bullish'
            elif latest_score >= 50:
                signal = 'bullish'
            elif latest_score <= -85:
                signal = 'strong_bearish'
            elif latest_score <= -50:
                signal = 'bearish'
            else:
                signal = 'neutral'
            
            confidence = min(abs(latest_score) / 100.0, 1.0)
            
            return ScoringResult(
                score=latest_score,
                signal=signal,
                confidence=confidence,
                description=f'MCSI MMT分析: {latest_score:.2f}',
                metadata={'indicator': 'MMT', 'source': 'TV-code/py-code/mcsi_mmt.py'}
            )
            
        except Exception as e:
            logger.error(f"MMT计算失败: {e}")
            return ScoringResult(0.0, 'neutral', 0.0, f'计算错误: {str(e)}')

    def score_with_validation(self, data: pd.DataFrame) -> ScoringResult:
        """带验证的评分计算（兼容旧接口）"""
        return self.calculate_score(data=data)

class MCSIUnifiedTTMUnit:
    """MCSI统一接口TTM单元"""
    
    def __init__(self):
        self.unit_id = 'mcsi_unified_ttm'
        self.name = 'MCSI统一接口TTM指标'
        
        # 导入源代码实现
        try:
            from mcsi_ttm import MCSITTMIndicator
            self.source_indicator = MCSITTMIndicator(comparison_period=4)
            self.source_available = True
            logger.info("✅ 成功加载TTM源代码实现")
        except ImportError as e:
            logger.error(f"❌ 无法导入TTM源代码: {e}")
            self.source_indicator = None
            self.source_available = False

    def is_available(self) -> bool:
        """检查是否可用"""
        return self.source_available

    def calculate_score(self, ohlc: Optional[pd.DataFrame] = None, 
                       data: Optional[pd.DataFrame] = None, **kwargs) -> ScoringResult:
        """计算分数"""
        if not self.source_available:
            return ScoringResult(0.0, 'neutral', 0.0, 'TTM源代码不可用')
        
        # 数据获取
        input_data = ohlc if ohlc is not None else data
        if input_data is None or len(input_data) < 20:
            return ScoringResult(0.0, 'neutral', 0.0, '数据不足')
        
        try:
            # 调用源代码计算
            result = self.source_indicator.calculate(input_data['close'].values)
            ttm_scores = result.get('ttm_score', [])
            
            if len(ttm_scores) == 0:
                return ScoringResult(0.0, 'neutral', 0.0, 'TTM计算返回空结果')
            
            latest_score = float(ttm_scores[-1])
            latest_score = max(-100.0, min(100.0, latest_score))
            
            # 确定信号类型（基于TTM的TD序列逻辑）
            if latest_score >= 100:
                signal = 'extreme_bullish'
            elif latest_score >= 80:
                signal = 'strong_bullish'
            elif latest_score <= -100:
                signal = 'extreme_bearish'
            elif latest_score <= -80:
                signal = 'strong_bearish'
            else:
                signal = 'neutral'
            
            confidence = min(abs(latest_score) / 100.0, 1.0)
            
            return ScoringResult(
                score=latest_score,
                signal=signal,
                confidence=confidence,
                description=f'MCSI TTM分析: {latest_score:.2f}',
                metadata={'indicator': 'TTM', 'source': 'TV-code/py-code/mcsi_ttm.py'}
            )
            
        except Exception as e:
            logger.error(f"TTM计算失败: {e}")
            return ScoringResult(0.0, 'neutral', 0.0, f'计算错误: {str(e)}')

    def score_with_validation(self, data: pd.DataFrame) -> ScoringResult:
        """带验证的评分计算（兼容旧接口）"""
        return self.calculate_score(data=data)

# 检查统一接口可用性
MCSI_UNIFIED_DIRECT_AVAILABLE = True
try:
    # 尝试实例化所有单元
    _test_macd = MCSIUnifiedMACDUnit()
    _test_rsi = MCSIUnifiedRSIUnit()
    _test_mmt = MCSIUnifiedMMTUnit()
    _test_ttm = MCSIUnifiedTTMUnit()
    
    if not all([_test_macd.is_available(), _test_rsi.is_available(), 
                _test_mmt.is_available(), _test_ttm.is_available()]):
        MCSI_UNIFIED_DIRECT_AVAILABLE = False
        logger.warning("⚠️ 部分MCSI统一接口不可用")
    else:
        logger.info("✅ 所有MCSI统一接口直接实现可用")
        
except Exception as e:
    MCSI_UNIFIED_DIRECT_AVAILABLE = False
    logger.error(f"❌ MCSI统一接口直接实现初始化失败: {e}")
