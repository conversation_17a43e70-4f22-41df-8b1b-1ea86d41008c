#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版MCSI MMT计分单元
基于接口分析结果的正确实现
"""

import pandas as pd
import numpy as np
import sys
import os
from typing import List
import logging

# 添加TV-code路径
tv_code_path = os.path.join(os.path.dirname(__file__), '../../TV-code/py-code')
if tv_code_path not in sys.path:
    sys.path.insert(0, tv_code_path)

try:
    from .base_scoring_unit import BaseScoringUnit, ScoringResult
except ImportError:
    from base_scoring_unit import BaseScoringUnit, ScoringResult


class MCSIMMTScoringUnit(BaseScoringUnit):
    """
    修复版MCSI MMT计分单元
    
    基于接口分析的正确实现：
    - 使用close_prices, high_prices, low_prices三个数组参数
    - 正确处理返回结果
    - 正确映射分数范围
    """
    
    def __init__(self, 
                 unit_id: str = 'mcsi_mmt_unit',
                 name: str = 'MCSI MMT计分单元',
                 description: str = '基于MCSI MMT指标的多重复合动量计分单元',
                 leveling: int = 10,
                 cyclic_memory: int = 34,
                 channel_weight: float = 0.5,
                 divergence_weight: float = 0.5):
        """
        初始化MCSI MMT计分单元
        """
        super().__init__(
            unit_id=unit_id,
            name=name,
            description=description,
            min_score=-10.0,
            max_score=10.0
        )
        
        # MCSI MMT参数
        self.leveling = leveling
        self.cyclic_memory = cyclic_memory
        self.channel_weight = channel_weight
        self.divergence_weight = divergence_weight
        
        # 延迟导入MCSI指标
        self._indicator = None
        
        # 更新配置
        self.config = {
            'leveling': leveling,
            'cyclic_memory': cyclic_memory,
            'channel_weight': channel_weight,
            'divergence_weight': divergence_weight
        }
    
    def _get_indicator(self):
        """延迟初始化MCSI指标"""
        if self._indicator is None:
            try:
                from mcsi_mmt import MCSIMMTIndicator
                self._indicator = MCSIMMTIndicator(
                    leveling=self.leveling,
                    cyclic_memory=self.cyclic_memory,
                    channel_weight=self.channel_weight,
                    divergence_weight=self.divergence_weight
                )
            except ImportError as e:
                self.logger.error(f"无法导入MCSI MMT指标: {e}")
                self._indicator = None
        return self._indicator
    
    def calculate_score(self, data: pd.DataFrame) -> ScoringResult:
        """
        计算MCSI MMT分数
        """
        try:
            # 获取指标实例
            indicator = self._get_indicator()
            if indicator is None:
                return self._create_fallback_result("MCSI MMT指标导入失败")
            
            # 准备数据：转换为numpy数组
            close_prices = data['close'].values.astype(np.float64)
            high_prices = data['high'].values.astype(np.float64)
            low_prices = data['low'].values.astype(np.float64)
            
            # 检查数据长度
            min_required = max(50, self.cyclic_memory) + 20
            if len(close_prices) < min_required:
                return self._create_fallback_result(f"数据长度不足，需要{min_required}，实际{len(close_prices)}")
            
            # 调用MCSI MMT指标（正确的三参数调用）
            mmt_result = indicator.calculate(close_prices, high_prices, low_prices)
            
            # 获取最新的MMT分数
            mmt_scores = mmt_result.get('mmt_score', np.array([0.0]))
            if len(mmt_scores) == 0:
                latest_score = 0.0
            else:
                latest_score = mmt_scores[-1]
                # 处理NaN值
                if np.isnan(latest_score) or np.isinf(latest_score):
                    latest_score = 0.0
            
            # 将-100到+100的分数映射到-10到+10
            normalized_score = np.clip(latest_score / 10.0, -10.0, 10.0)
            
            # 获取分量分数
            channel_scores = mmt_result.get('channel_score', np.array([0.0]))
            divergence_scores = mmt_result.get('divergence_score', np.array([0.0]))
            latest_channel_score = channel_scores[-1] if len(channel_scores) > 0 else 0.0
            latest_divergence_score = divergence_scores[-1] if len(divergence_scores) > 0 else 0.0
            
            # 处理NaN值
            if np.isnan(latest_channel_score) or np.isinf(latest_channel_score):
                latest_channel_score = 0.0
            if np.isnan(latest_divergence_score) or np.isinf(latest_divergence_score):
                latest_divergence_score = 0.0
            
            # 确定信号类型
            if latest_score >= 80:
                signal = 'strong_bullish'
            elif latest_score >= 40:
                signal = 'bullish'
            elif latest_score > 0:
                signal = 'weak_bullish'
            elif latest_score <= -80:
                signal = 'strong_bearish'
            elif latest_score <= -40:
                signal = 'bearish'
            elif latest_score < 0:
                signal = 'weak_bearish'
            else:
                signal = 'neutral'
            
            # 计算置信度
            confidence = min(1.0, abs(latest_score) / 100.0)
            
            # 检查背离信号
            bull_div = bool(mmt_result.get('bull_div', np.array([False]))[-1] if len(mmt_result.get('bull_div', [])) > 0 else False)
            bear_div = bool(mmt_result.get('bear_div', np.array([False]))[-1] if len(mmt_result.get('bear_div', [])) > 0 else False)
            hidden_bull_div = bool(mmt_result.get('hidden_bull_div', np.array([False]))[-1] if len(mmt_result.get('hidden_bull_div', [])) > 0 else False)
            hidden_bear_div = bool(mmt_result.get('hidden_bear_div', np.array([False]))[-1] if len(mmt_result.get('hidden_bear_div', [])) > 0 else False)
            
            # 生成描述
            description = f"MCSI MMT分析({normalized_score:.2f}): 轨道{latest_channel_score:.0f}, 背离{latest_divergence_score:.0f}"
            if bull_div or hidden_bull_div:
                description += ", 看涨背离"
            elif bear_div or hidden_bear_div:
                description += ", 看跌背离"
            
            return ScoringResult(
                score=float(normalized_score),
                raw_value=float(latest_score),
                signal=signal,
                confidence=float(confidence),
                description=description,
                metadata={
                    'channel_score': float(latest_channel_score),
                    'divergence_score': float(latest_divergence_score),
                    'bull_div': bull_div,
                    'bear_div': bear_div,
                    'hidden_bull_div': hidden_bull_div,
                    'hidden_bear_div': hidden_bear_div,
                    'original_score': float(latest_score),
                    'leveling': self.leveling,
                    'cyclic_memory': self.cyclic_memory,
                    'channel_weight': self.channel_weight,
                    'divergence_weight': self.divergence_weight
                }
            )
            
        except Exception as e:
            self.logger.error(f"MCSI MMT计分计算失败: {str(e)}")
            return self._create_fallback_result(f"计算错误: {str(e)}")
    
    def _create_fallback_result(self, error_msg: str) -> ScoringResult:
        """创建降级结果"""
        return ScoringResult(
            score=0.0,
            signal='neutral',
            confidence=0.0,
            description=f'MCSI MMT计分单元: {error_msg}',
            metadata={'error': error_msg}
        )
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """验证输入数据是否有效"""
        try:
            # 检查必需的列
            required_columns = self.get_required_columns()
            if not all(col in data.columns for col in required_columns):
                return False
            
            # 检查数据长度
            if len(data) < self.get_min_data_points():
                return False
            
            # 检查数据是否包含有效值
            for col in required_columns:
                if data[col].isna().all():
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {str(e)}")
            return False
    
    def get_required_columns(self) -> List[str]:
        """获取计算所需的数据列"""
        return ['close', 'high', 'low']
    
    def get_min_data_points(self) -> int:
        """获取计算所需的最少数据点数量"""
        return max(50, self.cyclic_memory) + 20
