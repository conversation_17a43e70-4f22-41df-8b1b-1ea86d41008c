#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RSI计分单元
基于RSI指标的独立计分单元
"""

from typing import List
import pandas as pd
import numpy as np
from .base_scoring_unit import BaseScoringUnit, ScoringResult
# RSI计算器内嵌实现
class RSICalculator:
    """RSI计算器 - 与旧系统保持一致的实现"""

    @staticmethod
    def calculate_rsi(data: pd.Series, period: int = 14) -> pd.Series:
        """
        计算RSI指标 - 使用与旧系统相同的算法

        Args:
            data: 价格序列（通常使用收盘价）
            period: RSI计算周期，默认14日

        Returns:
            RSI值序列
        """
        try:
            if len(data) < period + 1:
                return pd.Series(dtype=float)

            # 计算价格变化
            delta = data.diff()

            # 分离上涨和下跌
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)

            # 计算平均收益和平均损失（初始值使用简单移动平均）
            avg_gain = gain.rolling(window=period, min_periods=period).mean()
            avg_loss = loss.rolling(window=period, min_periods=period).mean()

            # 使用指数移动平均进行平滑（与旧系统一致）
            for i in range(period, len(data)):
                avg_gain.iloc[i] = (avg_gain.iloc[i-1] * (period - 1) + gain.iloc[i]) / period
                avg_loss.iloc[i] = (avg_loss.iloc[i-1] * (period - 1) + loss.iloc[i]) / period

            # 计算RS和RSI
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))

            # 处理除零情况
            rsi = rsi.fillna(50)  # 如果无法计算，默认为50（中性）

            return rsi

        except Exception as e:
            return pd.Series(dtype=float)


class RSIScoringUnit(BaseScoringUnit):
    """
    RSI计分单元
    
    基于RSI指标计算分数：
    - RSI < 30: 超卖，正分
    - RSI > 70: 超买，负分
    - 30 <= RSI <= 70: 中性区域，根据位置给分
    """
    
    def __init__(self, 
                 unit_id: str = 'rsi_unit',
                 name: str = 'RSI计分单元',
                 description: str = '基于RSI指标的计分单元',
                 period: int = 14,
                 oversold_threshold: float = 30.0,
                 overbought_threshold: float = 70.0):
        """
        初始化RSI计分单元
        
        Args:
            unit_id: 唯一标识符
            name: 计分单元名称
            description: 描述信息
            period: RSI计算周期
            oversold_threshold: 超卖阈值
            overbought_threshold: 超买阈值
        """
        super().__init__(
            unit_id=unit_id,
            name=name,
            description=description,
            min_score=-5.0,
            max_score=5.0
        )
        
        # RSI参数
        self.period = period
        self.oversold_threshold = oversold_threshold
        self.overbought_threshold = overbought_threshold
        
        # RSI计算器是静态方法，不需要实例化
        self.rsi_calculator = RSICalculator
        
        # 更新配置
        self.config = {
            'period': period,
            'oversold_threshold': oversold_threshold,
            'overbought_threshold': overbought_threshold
        }
    
    def get_required_columns(self) -> List[str]:
        """获取计算所需的数据列"""
        return ['close']
    
    def get_min_data_points(self) -> int:
        """获取计算所需的最少数据点数量"""
        return self.period + 10  # RSI需要额外的数据点来稳定
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """
        验证输入数据是否有效
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            bool: 数据是否有效
        """
        try:
            # 检查必需列
            required_columns = self.get_required_columns()
            if not all(col in data.columns for col in required_columns):
                self.logger.warning(f"缺少必需列: {required_columns}")
                return False
            
            # 检查数据量
            if len(data) < self.get_min_data_points():
                self.logger.warning(f"数据量不足，需要至少 {self.get_min_data_points()} 个数据点")
                return False
            
            # 检查数据有效性
            if data['close'].isna().all():
                self.logger.warning("收盘价数据全部为空")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {str(e)}")
            return False
    
    def calculate_score(self, data: pd.DataFrame) -> ScoringResult:
        """
        计算RSI分数
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            ScoringResult: 计分结果
        """
        try:
            # 计算RSI
            rsi_series = self.rsi_calculator.calculate_rsi(data['close'], self.period)

            if rsi_series is None or rsi_series.empty or rsi_series.isna().all():
                return ScoringResult(
                    score=0.0,
                    signal='neutral',
                    description='RSI计算失败'
                )

            # 获取最新的RSI值
            rsi_value = rsi_series.iloc[-1]

            if pd.isna(rsi_value):
                return ScoringResult(
                    score=0.0,
                    signal='neutral',
                    description='RSI值无效'
                )
            
            # 计算分数
            score, signal, description = self._calculate_rsi_score(rsi_value)
            
            return ScoringResult(
                score=score,
                raw_value=rsi_value,
                signal=signal,
                confidence=self._calculate_confidence(rsi_value),
                description=description,
                metadata={
                    'rsi_value': rsi_value,
                    'period': self.period,
                    'oversold_threshold': self.oversold_threshold,
                    'overbought_threshold': self.overbought_threshold,
                    'signal_type': signal
                }
            )
            
        except Exception as e:
            self.logger.error(f"RSI计分计算失败: {str(e)}")
            return ScoringResult(
                score=0.0,
                signal='neutral',
                description=f'计算错误: {str(e)}'
            )
    
    def _calculate_rsi_score(self, rsi_value: float) -> tuple:
        """
        根据RSI值计算分数（按照规则文档）

        Args:
            rsi_value: RSI值

        Returns:
            tuple: (分数, 信号, 描述)
        """
        # 按照规则文档的RSI评分规则
        if rsi_value >= 80:
            # RSI >= 80: -2分 (严重超买)
            score = -2.0
            signal = 'bearish'
            description = f'RSI超买({rsi_value:.1f}), 看跌信号'

        elif rsi_value >= 70:
            # RSI 70-80: -1分 (超买)
            score = -1.0
            signal = 'bearish'
            description = f'RSI超买({rsi_value:.1f}), 看跌信号'

        elif rsi_value >= 50:
            # RSI 50-70: +1分 (强势)
            score = 1.0
            signal = 'neutral_bullish'
            description = f'RSI中性偏高({rsi_value:.1f}), 轻微看涨'

        elif rsi_value >= 30:
            # RSI 30-50: 0分 (正常)
            score = 0.0
            signal = 'neutral'
            description = f'RSI中性({rsi_value:.1f}), 正常区间'

        elif rsi_value >= 20:
            # RSI 20-30: +1分 (超卖，买入机会)
            score = 1.0
            signal = 'neutral_bullish'
            description = f'RSI中性偏低({rsi_value:.1f}), 轻微看涨'

        else:
            # RSI <= 20: +2分 (严重超卖，强烈买入)
            score = 2.0
            signal = 'bullish'
            description = f'RSI超卖({rsi_value:.1f}), 看涨信号'

        return score, signal, description
    
    def _calculate_confidence(self, rsi_value: float) -> float:
        """
        计算信号置信度
        
        Args:
            rsi_value: RSI值
            
        Returns:
            float: 置信度 (0.0-1.0)
        """
        if rsi_value <= 20 or rsi_value >= 80:
            # 极端区域，高置信度
            return 1.0
        elif rsi_value <= self.oversold_threshold or rsi_value >= self.overbought_threshold:
            # 超买超卖区域，中高置信度
            return 0.8
        else:
            # 中性区域，低置信度
            mid_point = (self.oversold_threshold + self.overbought_threshold) / 2
            distance_from_mid = abs(rsi_value - mid_point)
            max_distance = (self.overbought_threshold - self.oversold_threshold) / 2
            return 0.3 + 0.4 * (distance_from_mid / max_distance)
    
    def update_config(self, config: dict) -> None:
        """
        更新配置参数
        
        Args:
            config: 新的配置参数
        """
        super().update_config(config)
        
        # 更新RSI参数
        if 'period' in config:
            self.period = config['period']
            # RSI计算器是静态方法，不需要重新实例化
        
        if 'oversold_threshold' in config:
            self.oversold_threshold = config['oversold_threshold']
        
        if 'overbought_threshold' in config:
            self.overbought_threshold = config['overbought_threshold']
