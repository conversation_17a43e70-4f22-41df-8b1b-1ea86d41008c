#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV数据加载器
专门用于从stock_data目录加载CSV文件数据
不影响原有的数据库数据加载功能
"""

import os
import pandas as pd
import logging
from typing import Dict, List, Optional
from pathlib import Path
import numpy as np

class CSVDataLoader:
    """CSV数据加载器"""
    
    def __init__(self, csv_dir="stock_data"):
        self.csv_dir = Path(csv_dir)
        self.logger = logging.getLogger(__name__)
        
        # 确保CSV目录存在
        if not self.csv_dir.exists():
            self.logger.error(f"CSV数据目录不存在: {self.csv_dir}")
            raise FileNotFoundError(f"CSV数据目录不存在: {self.csv_dir}")
    
    def get_available_csv_files(self) -> List[Dict]:
        """获取所有可用的CSV文件信息"""
        csv_files = []
        
        try:
            for csv_file in self.csv_dir.glob("*.csv"):
                file_info = self._parse_csv_filename(csv_file.name)
                if file_info:
                    # 检查文件是否有效
                    if self._validate_csv_file(csv_file):
                        file_info['file_path'] = str(csv_file)
                        csv_files.append(file_info)
            
            self.logger.info(f"发现 {len(csv_files)} 个有效的CSV文件")
            return csv_files
            
        except Exception as e:
            self.logger.error(f"扫描CSV文件失败: {str(e)}")
            return []
    
    def _parse_csv_filename(self, filename: str) -> Optional[Dict]:
        """解析CSV文件名获取股票信息"""
        try:
            # 移除.csv扩展名
            name_part = filename.replace('.csv', '')
            
            # 解析不同格式的文件名
            # 格式1: ashares_300584_海辰药业.csv
            # 格式2: crypto_btc_usdt_btc.csv
            # 格式3: commodity_clusd_原油.csv
            
            parts = name_part.split('_')
            if len(parts) >= 3:
                category = parts[0]  # ashares, crypto, commodity, etc.
                symbol = parts[1]    # 股票代码或符号
                name = '_'.join(parts[2:])  # 股票名称
                
                return {
                    'symbol': symbol,
                    'name': name,
                    'category': category,
                    'table_name': name_part,  # 使用完整文件名作为表名
                    'filename': filename
                }
            else:
                self.logger.warning(f"无法解析文件名格式: {filename}")
                return None
                
        except Exception as e:
            self.logger.error(f"解析文件名失败 {filename}: {str(e)}")
            return None
    
    def _validate_csv_file(self, csv_file: Path) -> bool:
        """验证CSV文件是否包含必要的列"""
        try:
            # 读取文件头部检查列名
            df_sample = pd.read_csv(csv_file, nrows=1)
            required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            
            # 检查必要的列是否存在
            missing_columns = [col for col in required_columns if col not in df_sample.columns]
            if missing_columns:
                self.logger.warning(f"文件 {csv_file.name} 缺少必要列: {missing_columns}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证CSV文件失败 {csv_file.name}: {str(e)}")
            return False
    
    def get_stock_data(self, symbol_or_filename: str, limit: int = 250) -> Optional[pd.DataFrame]:
        """获取股票的历史数据"""
        try:
            # 查找对应的CSV文件
            csv_file = self._find_csv_file(symbol_or_filename)
            if not csv_file:
                self.logger.warning(f"未找到股票数据文件: {symbol_or_filename}")
                return None
            
            # 读取CSV文件
            df = pd.read_csv(csv_file)
            
            if df.empty:
                self.logger.warning(f"CSV文件 {csv_file.name} 没有数据")
                return None
            
            # 数据预处理 - 智能处理时间列
            date_column = None
            if 'timestamp' in df.columns:
                date_column = 'timestamp'
            elif 'date' in df.columns:
                date_column = 'date'
            elif 'time' in df.columns:
                date_column = 'time'
            else:
                self.logger.error(f"CSV文件 {csv_file.name} 缺少时间列 (timestamp/date/time)")
                return None

            # 处理时间列
            df[date_column] = pd.to_datetime(df[date_column])

            # 统一列名为'date'
            if date_column != 'date':
                df['date'] = df[date_column]
                df = df.drop(columns=[date_column])

            df = df.sort_values('date').reset_index(drop=True)
            
            # 限制数据量（取最新的数据）
            if len(df) > limit:
                df = df.tail(limit).reset_index(drop=True)
            
            self.logger.debug(f"从CSV文件 {csv_file.name} 获取到 {len(df)} 条数据")
            return df
            
        except Exception as e:
            self.logger.error(f"读取CSV数据失败 {symbol_or_filename}: {str(e)}")
            return None
    
    def _find_csv_file(self, symbol_or_filename: str) -> Optional[Path]:
        """查找对应的CSV文件"""
        # 方法1: 直接匹配文件名
        direct_file = self.csv_dir / f"{symbol_or_filename}.csv"
        if direct_file.exists():
            return direct_file
        
        # 方法2: 如果已经包含.csv扩展名
        if symbol_or_filename.endswith('.csv'):
            direct_file = self.csv_dir / symbol_or_filename
            if direct_file.exists():
                return direct_file
        
        # 方法3: 通过symbol匹配
        available_files = self.get_available_csv_files()
        for file_info in available_files:
            if (file_info['symbol'] == symbol_or_filename or 
                file_info['table_name'] == symbol_or_filename or
                file_info['filename'] == symbol_or_filename):
                return Path(file_info['file_path'])
        
        return None
    
    def get_stock_list(self) -> List[Dict]:
        """获取所有可用的股票列表"""
        return self.get_available_csv_files()
    
    def check_stock_exists(self, symbol_or_filename: str) -> bool:
        """检查股票数据是否存在"""
        return self._find_csv_file(symbol_or_filename) is not None
