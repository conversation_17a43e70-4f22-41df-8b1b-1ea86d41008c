#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV存储管理器
用于保存和加载股票趋势分析结果
"""

import os
import pandas as pd
import logging
from datetime import datetime
from typing import Dict, List, Optional
import json

class CSVStorage:
    """CSV存储管理器"""
    
    def __init__(self, storage_dir="storage"):
        self.storage_dir = storage_dir
        self.results_file = os.path.join(storage_dir, "analysis_results.csv")
        self.metadata_file = os.path.join(storage_dir, "analysis_metadata.json")
        self.logger = logging.getLogger(__name__)
        
        # 确保存储目录存在
        os.makedirs(storage_dir, exist_ok=True)
        
    def save_analysis_results(self, results: List[Dict], data_limit: int = 500) -> bool:
        """保存分析结果到CSV文件"""
        try:
            if not results:
                self.logger.warning("没有分析结果需要保存")
                return False
            
            # 准备DataFrame数据
            csv_data = []
            for result in results:
                row = {
                    'symbol': result['symbol'],
                    'name': result['name'],
                    'category': result['category'],
                    'table_name': result['table_name'],
                    'data_count': result['data_count'],
                    
                    # 趋势分析数据
                    'trend_score': result['trend_analysis']['score'],
                    'trend_grade': result['trend_analysis']['trend_grade'],
                    'trend_description': result['trend_analysis']['trend_description'],
                    'trend_percentage': result['trend_analysis']['score_percentage'],
                    
                    # RSI数据
                    'rsi_score': result.get('rsi_analysis', {}).get('score', 0),
                    'rsi_value': result.get('rsi_analysis', {}).get('rsi', None),
                    'rsi_signal': result.get('rsi_analysis', {}).get('signal', 'unknown'),
                    'rsi_description': result.get('rsi_analysis', {}).get('description', ''),
                    
                    # MACD数据
                    'macd_score': result.get('macd_analysis', {}).get('score', 0),
                    'macd_signal': result.get('macd_analysis', {}).get('signal', 'unknown'),
                    'macd_description': result.get('macd_analysis', {}).get('description', ''),
                    
                    # 综合评分数据
                    'composite_score': result.get('composite_analysis', {}).get('composite_score', result['trend_analysis']['score']),
                    'composite_grade': result.get('composite_analysis', {}).get('composite_grade', result['trend_analysis']['trend_grade']),
                    'composite_description': result.get('composite_analysis', {}).get('description', result['trend_analysis']['trend_description']),
                    
                    # 移动平均线数据
                    'current_price': result['trend_analysis']['ma_values']['current_price'],
                    'ma5': result['trend_analysis']['ma_values']['ma5'],
                    'ma20': result['trend_analysis']['ma_values']['ma20'],
                    'ma50': result['trend_analysis']['ma_values']['ma50'],
                    'ma200': result['trend_analysis']['ma_values']['ma200'],
                    
                    # 时间戳
                    'analysis_time': result['analysis_time'].strftime('%Y-%m-%d %H:%M:%S'),
                    'last_update': result['last_update'].strftime('%Y-%m-%d %H:%M:%S') if hasattr(result['last_update'], 'strftime') else str(result['last_update'])
                }
                csv_data.append(row)
            
            # 创建DataFrame并保存
            df = pd.DataFrame(csv_data)
            df.to_csv(self.results_file, index=False, encoding='utf-8')
            
            # 保存元数据
            metadata = {
                'total_stocks': len(results),
                'data_limit': data_limit,
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'file_version': '1.0'
            }
            
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"成功保存 {len(results)} 条分析结果到 {self.results_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存分析结果失败: {str(e)}")
            return False
    
    def load_analysis_results(self) -> Optional[List[Dict]]:
        """从CSV文件加载分析结果"""
        try:
            if not os.path.exists(self.results_file):
                self.logger.info("分析结果文件不存在，将进行首次分析")
                return None
            
            # 读取CSV文件
            df = pd.read_csv(self.results_file, encoding='utf-8')
            
            if df.empty:
                self.logger.warning("分析结果文件为空")
                return None
            
            # 转换为字典列表
            results = []
            for _, row in df.iterrows():
                result = {
                    'symbol': row['symbol'],
                    'name': row['name'],
                    'category': row['category'],
                    'trend_score': row['trend_score'],
                    'trend_grade': row['trend_grade'],
                    'trend_description': row['trend_description'],
                    'data_count': row['data_count'],
                    'analysis_time': pd.to_datetime(row['analysis_time']),
                    
                    # RSI信息
                    'rsi_score': row.get('rsi_score', 0),
                    'rsi_value': row.get('rsi_value', None),
                    'rsi_signal': row.get('rsi_signal', 'unknown'),
                    'rsi_description': row.get('rsi_description', ''),
                    
                    # MACD信息
                    'macd_score': row.get('macd_score', 0),
                    'macd_signal': row.get('macd_signal', 'unknown'),
                    'macd_description': row.get('macd_description', ''),
                    
                    # 综合评分信息
                    'composite_score': row.get('composite_score', row['trend_score']),
                    'composite_grade': row.get('composite_grade', row['trend_grade']),
                    'composite_description': row.get('composite_description', row['trend_description'])
                }
                results.append(result)
            
            self.logger.info(f"成功加载 {len(results)} 条分析结果")
            return results
            
        except Exception as e:
            self.logger.error(f"加载分析结果失败: {str(e)}")
            return None
    
    def get_metadata(self) -> Optional[Dict]:
        """获取分析元数据"""
        try:
            if not os.path.exists(self.metadata_file):
                return None
            
            with open(self.metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            return metadata
            
        except Exception as e:
            self.logger.error(f"读取元数据失败: {str(e)}")
            return None
    
    def is_analysis_fresh(self, max_age_hours: int = 24) -> bool:
        """检查分析结果是否新鲜（在指定小时内）"""
        try:
            metadata = self.get_metadata()
            if not metadata:
                return False
            
            analysis_time = datetime.strptime(metadata['analysis_time'], '%Y-%m-%d %H:%M:%S')
            age_hours = (datetime.now() - analysis_time).total_seconds() / 3600
            
            return age_hours <= max_age_hours
            
        except Exception as e:
            self.logger.error(f"检查分析新鲜度失败: {str(e)}")
            return False
    
    def clear_storage(self) -> bool:
        """清空存储的分析结果"""
        try:
            if os.path.exists(self.results_file):
                os.remove(self.results_file)
            if os.path.exists(self.metadata_file):
                os.remove(self.metadata_file)
            
            self.logger.info("已清空存储的分析结果")
            return True
            
        except Exception as e:
            self.logger.error(f"清空存储失败: {str(e)}")
            return False
