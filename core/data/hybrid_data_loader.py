#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
混合数据加载器
优先使用数据库数据源，失败时回退到CSV数据源
"""

import logging
import pandas as pd
from typing import Dict, List, Optional
from .data_loader import DataLoader
from .csv_data_loader import CSVDataLoader


class HybridDataLoader:
    """
    混合数据加载器
    
    优先使用数据库数据源，失败时回退到CSV数据源
    提供统一的数据访问接口
    """
    
    def __init__(self, csv_dir="stock_data", prefer_database=True):
        """
        初始化混合数据加载器
        
        Args:
            csv_dir: CSV文件目录
            prefer_database: 是否优先使用数据库
        """
        self.logger = logging.getLogger(__name__)
        self.prefer_database = prefer_database
        
        # 初始化数据库加载器
        try:
            self.db_loader = DataLoader()
            self.db_available = self.db_loader.connect_db()
            if self.db_available:
                self.logger.info("数据库连接成功，将优先使用数据库数据源")
            else:
                self.logger.warning("数据库连接失败，将使用CSV数据源")
        except Exception as e:
            self.logger.error(f"初始化数据库加载器失败: {str(e)}")
            self.db_available = False
            self.db_loader = None
        
        # 初始化CSV加载器
        try:
            self.csv_loader = CSVDataLoader(csv_dir)
            self.csv_available = True
            self.logger.info("CSV数据加载器初始化成功")
        except Exception as e:
            self.logger.error(f"初始化CSV加载器失败: {str(e)}")
            self.csv_available = False
            self.csv_loader = None
        
        # 检查至少有一个数据源可用
        if not self.db_available and not self.csv_available:
            raise RuntimeError("没有可用的数据源！数据库和CSV都无法访问")
    
    def get_stock_data(self, stock_code: str, limit: int = 250) -> Optional[pd.DataFrame]:
        """
        获取股票数据
        
        Args:
            stock_code: 股票代码
            limit: 数据量限制
            
        Returns:
            股票数据DataFrame，失败返回None
        """
        # 优先使用数据库
        if self.prefer_database and self.db_available:
            try:
                data = self.db_loader.get_stock_data(stock_code, limit=limit)
                if data is not None and not data.empty:
                    self.logger.debug(f"从数据库获取 {stock_code} 数据成功，{len(data)} 条记录")
                    return data
                else:
                    self.logger.debug(f"数据库中未找到 {stock_code} 数据，尝试CSV")
            except Exception as e:
                self.logger.warning(f"从数据库获取 {stock_code} 数据失败: {str(e)}，尝试CSV")
        
        # 回退到CSV
        if self.csv_available:
            try:
                data = self.csv_loader.get_stock_data(stock_code, limit=limit)
                if data is not None and not data.empty:
                    self.logger.debug(f"从CSV获取 {stock_code} 数据成功，{len(data)} 条记录")
                    return data
                else:
                    self.logger.warning(f"CSV中也未找到 {stock_code} 数据")
            except Exception as e:
                self.logger.error(f"从CSV获取 {stock_code} 数据失败: {str(e)}")
        
        self.logger.error(f"所有数据源都无法获取 {stock_code} 数据")
        return None
    
    def get_stock_list(self) -> List[Dict]:
        """
        获取所有可用的股票列表
        
        Returns:
            股票列表，包含数据库和CSV的股票
        """
        all_stocks = []
        
        # 从数据库获取股票列表
        if self.db_available:
            try:
                db_stocks = self.db_loader.get_stock_list()
                for stock in db_stocks:
                    stock['source'] = 'database'
                all_stocks.extend(db_stocks)
                self.logger.debug(f"从数据库获取到 {len(db_stocks)} 只股票")
            except Exception as e:
                self.logger.warning(f"从数据库获取股票列表失败: {str(e)}")
        
        # 从CSV获取股票列表
        if self.csv_available:
            try:
                csv_stocks = self.csv_loader.get_stock_list()
                # 避免重复，只添加数据库中没有的股票
                db_symbols = {stock['symbol'] for stock in all_stocks}
                for stock in csv_stocks:
                    if stock['symbol'] not in db_symbols:
                        stock['source'] = 'csv'
                        all_stocks.append(stock)
                self.logger.debug(f"从CSV获取到 {len(csv_stocks)} 只股票，去重后添加 {len([s for s in csv_stocks if s['symbol'] not in db_symbols])} 只")
            except Exception as e:
                self.logger.warning(f"从CSV获取股票列表失败: {str(e)}")
        
        self.logger.info(f"混合数据源共获取到 {len(all_stocks)} 只股票")
        return all_stocks
    
    def check_stock_exists(self, stock_code: str) -> bool:
        """
        检查股票是否存在
        
        Args:
            stock_code: 股票代码
            
        Returns:
            是否存在
        """
        # 检查数据库
        if self.db_available:
            try:
                data = self.db_loader.get_stock_data(stock_code, limit=1)
                if data is not None and not data.empty:
                    return True
            except Exception:
                pass
        
        # 检查CSV
        if self.csv_available:
            try:
                data = self.csv_loader.get_stock_data(stock_code, limit=1)
                if data is not None and not data.empty:
                    return True
            except Exception:
                pass
        
        return False
    
    def get_data_source_status(self) -> Dict:
        """
        获取数据源状态
        
        Returns:
            数据源状态信息
        """
        return {
            'database_available': self.db_available,
            'csv_available': self.csv_available,
            'prefer_database': self.prefer_database,
            'active_sources': [
                source for source, available in [
                    ('database', self.db_available),
                    ('csv', self.csv_available)
                ] if available
            ]
        }
    
    def set_preference(self, prefer_database: bool):
        """
        设置数据源偏好
        
        Args:
            prefer_database: 是否优先使用数据库
        """
        self.prefer_database = prefer_database
        self.logger.info(f"数据源偏好已设置为: {'数据库优先' if prefer_database else 'CSV优先'}")
    
    def close_connections(self):
        """关闭所有连接"""
        if self.db_loader:
            try:
                self.db_loader.close_connection()
            except Exception as e:
                self.logger.error(f"关闭数据库连接失败: {str(e)}")
