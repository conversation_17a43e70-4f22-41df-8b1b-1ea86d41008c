#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
趋势分组
专门用于趋势相关的计分单元分组
"""

import pandas as pd
from typing import Dict, Any
from .base_group import BaseGroup, GroupResult


class TrendGroup(BaseGroup):
    """
    趋势分组
    
    专门管理趋势相关的计分单元，如：
    - 趋势计分单元 (移动平均线)
    - 长期动量指标
    - 趋势强度指标
    """
    
    def __init__(self,
                 group_id: str = 'trend_group',
                 name: str = '趋势分组',
                 description: str = '趋势相关指标的分组',
                 weight: float = 1.0):
        """
        初始化趋势分组
        
        Args:
            group_id: 分组唯一标识符
            name: 分组名称
            description: 分组描述
            weight: 分组权重
        """
        super().__init__(
            group_id=group_id,
            name=name,
            description=description,
            weight=weight
        )
        
        # 趋势分组特有配置
        self.config = {
            'trend_threshold': 0.5,  # 趋势判断阈值
            'trend_confirmation_period': 3,  # 趋势确认周期
            'weight_adjustment_enabled': True  # 是否启用权重自适应调整
        }
    
    def calculate_group_score(self, data: pd.DataFrame) -> GroupResult:
        """
        计算趋势分组分数
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            GroupResult: 趋势分组计分结果
        """
        try:
            unit_results = {}
            total_score = 0.0
            valid_unit_count = 0
            
            # 计算每个计分单元的分数
            for unit_id, unit in self.scoring_units.items():
                if unit.enabled:
                    try:
                        result = unit.score_with_validation(data)
                        unit_results[unit_id] = result.to_dict()
                        
                        if result.score != 0.0:  # 只计算有效分数
                            total_score += result.score
                            valid_unit_count += 1
                            
                    except Exception as e:
                        self.logger.warning(f"计分单元 {unit_id} 计算失败: {str(e)}")
                        unit_results[unit_id] = {
                            'score': 0.0,
                            'signal': 'neutral',
                            'description': f'计算失败: {str(e)}'
                        }
            
            # 计算加权分数（不进行额外调整，保持原始分数）
            weighted_score = self.calculate_weighted_score(unit_results)

            # 简化分数处理，不进行复杂的调整
            adjusted_score = weighted_score
            confidence = 0.8  # 固定置信度
            
            # 生成描述
            description = self._generate_trend_description(
                adjusted_score, valid_unit_count, unit_results
            )
            
            return GroupResult(
                group_id=self.group_id,
                group_name=self.name,
                total_score=total_score,
                weighted_score=adjusted_score,
                unit_results=unit_results,
                confidence=confidence,
                description=description,
                metadata={
                    'valid_unit_count': valid_unit_count,
                    'original_weighted_score': weighted_score,
                    'trend_adjustment': adjusted_score - weighted_score,
                    'config': self.config
                }
            )
            
        except Exception as e:
            self.logger.error(f"趋势分组计算失败: {str(e)}")
            return GroupResult(
                group_id=self.group_id,
                group_name=self.name,
                total_score=0.0,
                weighted_score=0.0,
                unit_results={},
                description=f'趋势分组计算错误: {str(e)}'
            )
    
    def _adjust_trend_score(self, weighted_score: float, 
                           unit_results: Dict[str, Any], 
                           data: pd.DataFrame) -> tuple:
        """
        趋势分组特有的分数调整
        
        Args:
            weighted_score: 原始加权分数
            unit_results: 单元结果字典
            data: 股票数据
            
        Returns:
            tuple: (调整后分数, 置信度)
        """
        adjusted_score = weighted_score
        confidence = 0.5
        
        try:
            # 1. 趋势一致性检查
            bullish_signals = 0
            bearish_signals = 0
            neutral_signals = 0
            
            for unit_id, result in unit_results.items():
                signal = result.get('signal', 'neutral')
                if 'bullish' in signal:
                    bullish_signals += 1
                elif 'bearish' in signal:
                    bearish_signals += 1
                else:
                    neutral_signals += 1
            
            total_signals = bullish_signals + bearish_signals + neutral_signals
            
            if total_signals > 0:
                # 计算信号一致性
                max_signal_ratio = max(bullish_signals, bearish_signals) / total_signals
                
                # 一致性越高，置信度越高
                confidence = 0.3 + 0.7 * max_signal_ratio
                
                # 如果信号高度一致，增强分数
                if max_signal_ratio >= 0.8:
                    if bullish_signals > bearish_signals:
                        adjusted_score *= 1.2  # 增强看涨信号
                    else:
                        adjusted_score *= 1.2  # 增强看跌信号
                elif max_signal_ratio <= 0.4:
                    # 信号分歧，减弱分数
                    adjusted_score *= 0.8
            
            # 2. 趋势强度调整
            if self.config.get('weight_adjustment_enabled', True):
                trend_strength = self._calculate_trend_strength(data)
                adjusted_score *= (0.8 + 0.4 * trend_strength)  # 0.8-1.2倍调整
            
            # 3. 限制分数范围
            max_score = 10.0
            min_score = -10.0
            adjusted_score = max(min_score, min(max_score, adjusted_score))
            
        except Exception as e:
            self.logger.warning(f"趋势分数调整失败: {str(e)}")
        
        return adjusted_score, confidence
    
    def _calculate_trend_strength(self, data: pd.DataFrame) -> float:
        """
        计算趋势强度
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            float: 趋势强度 (0.0-1.0)
        """
        try:
            if len(data) < 20:
                return 0.5
            
            # 使用价格的线性回归斜率来判断趋势强度
            recent_data = data.tail(20)
            prices = recent_data['close'].values
            
            # 计算线性回归
            x = range(len(prices))
            slope = (len(x) * sum(x[i] * prices[i] for i in range(len(x))) - 
                    sum(x) * sum(prices)) / (len(x) * sum(x[i]**2 for i in range(len(x))) - sum(x)**2)
            
            # 将斜率转换为强度值
            avg_price = sum(prices) / len(prices)
            relative_slope = abs(slope) / avg_price if avg_price > 0 else 0
            
            # 标准化到0-1范围
            strength = min(1.0, relative_slope * 1000)  # 调整系数
            
            return strength
            
        except Exception:
            return 0.5
    
    def _generate_trend_description(self, score: float, valid_count: int, 
                                   unit_results: Dict[str, Any]) -> str:
        """
        生成趋势描述
        
        Args:
            score: 调整后分数
            valid_count: 有效单元数量
            unit_results: 单元结果字典
            
        Returns:
            str: 趋势描述
        """
        if score >= 3.0:
            trend_desc = "强烈看涨趋势"
        elif score >= 1.0:
            trend_desc = "看涨趋势"
        elif score >= 0.5:
            trend_desc = "轻微看涨趋势"
        elif score <= -3.0:
            trend_desc = "强烈看跌趋势"
        elif score <= -1.0:
            trend_desc = "看跌趋势"
        elif score <= -0.5:
            trend_desc = "轻微看跌趋势"
        else:
            trend_desc = "趋势不明确"
        
        return f"趋势分析({score:.2f}): {trend_desc} (基于{valid_count}个指标)"
    
    def add_default_units(self):
        """添加默认的趋势计分单元"""
        try:
            from core.scoring_units import TrendScoringUnit

            # 添加趋势计分单元
            trend_unit = TrendScoringUnit()
            self.add_scoring_unit(trend_unit, weight=1.0)

            self.logger.info("已添加默认趋势计分单元")

        except ImportError as e:
            self.logger.warning(f"无法导入默认趋势计分单元: {str(e)}")
    
    def update_config(self, config: Dict[str, Any]) -> None:
        """
        更新趋势分组配置
        
        Args:
            config: 新的配置参数
        """
        super().update_config(config)
        
        # 趋势分组特有配置更新
        if 'trend_threshold' in config:
            self.config['trend_threshold'] = config['trend_threshold']
        
        if 'trend_confirmation_period' in config:
            self.config['trend_confirmation_period'] = config['trend_confirmation_period']
        
        if 'weight_adjustment_enabled' in config:
            self.config['weight_adjustment_enabled'] = config['weight_adjustment_enabled']
