#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础分组类
所有计分单元分组的基类
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime
import pandas as pd


class GroupResult:
    """分组计分结果类"""
    
    def __init__(self,
                 group_id: str,
                 group_name: str,
                 total_score: float,
                 weighted_score: float,
                 unit_results: Dict[str, Any],
                 confidence: float = 1.0,
                 description: str = '',
                 metadata: Optional[Dict[str, Any]] = None):
        self.group_id = group_id
        self.group_name = group_name
        self.total_score = total_score
        self.weighted_score = weighted_score
        self.unit_results = unit_results
        self.confidence = confidence
        self.description = description
        self.metadata = metadata or {}
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'group_id': self.group_id,
            'group_name': self.group_name,
            'total_score': self.total_score,
            'weighted_score': self.weighted_score,
            'unit_results': self.unit_results,
            'confidence': self.confidence,
            'description': self.description,
            'metadata': self.metadata,
            'timestamp': self.timestamp.isoformat()
        }


class BaseGroup(ABC):
    """
    基础分组抽象类
    
    所有计分单元分组都必须继承此类并实现抽象方法
    """
    
    def __init__(self,
                 group_id: str,
                 name: str,
                 description: str = '',
                 weight: float = 1.0,
                 enabled: bool = True):
        """
        初始化分组
        
        Args:
            group_id: 分组唯一标识符
            name: 分组名称
            description: 分组描述
            weight: 分组权重
            enabled: 是否启用
        """
        self.group_id = group_id
        self.name = name
        self.description = description
        self.weight = weight
        self.enabled = enabled
        self.logger = logging.getLogger(f"{__name__}.{group_id}")
        
        # 计分单元列表和权重
        self.scoring_units = {}  # {unit_id: unit_instance}
        self.unit_weights = {}   # {unit_id: weight}
        
        # 分组历史记录
        self.group_history: List[GroupResult] = []
        
        # 配置参数
        self.config = {}
    
    @abstractmethod
    def calculate_group_score(self, data: pd.DataFrame) -> GroupResult:
        """
        计算分组分数
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            GroupResult: 分组计分结果
        """
        pass
    
    def add_scoring_unit(self, unit, weight: float = 1.0) -> None:
        """
        添加计分单元到分组
        
        Args:
            unit: 计分单元实例
            weight: 单元权重
        """
        self.scoring_units[unit.unit_id] = unit
        self.unit_weights[unit.unit_id] = weight
        self.logger.info(f"添加计分单元 {unit.unit_id} 到分组 {self.group_id}，权重: {weight}")
    
    def remove_scoring_unit(self, unit_id: str) -> None:
        """
        从分组中移除计分单元
        
        Args:
            unit_id: 计分单元ID
        """
        if unit_id in self.scoring_units:
            del self.scoring_units[unit_id]
            del self.unit_weights[unit_id]
            self.logger.info(f"从分组 {self.group_id} 移除计分单元 {unit_id}")
    
    def update_unit_weight(self, unit_id: str, weight: float, notify_manager: bool = True) -> None:
        """
        更新计分单元权重

        Args:
            unit_id: 计分单元ID
            weight: 新权重
            notify_manager: 是否通知管理器保存配置
        """
        if unit_id in self.unit_weights:
            self.unit_weights[unit_id] = weight
            self.logger.info(f"更新分组 {self.group_id} 中单元 {unit_id} 权重为 {weight}")

            # 通知管理器保存配置（如果有的话）
            if notify_manager and hasattr(self, '_manager_callback'):
                self._manager_callback()
    
    def get_unit_weights(self) -> Dict[str, float]:
        """获取所有单元权重"""
        return self.unit_weights.copy()
    
    def set_unit_weights(self, weights: Dict[str, float], notify_manager: bool = True) -> None:
        """
        批量设置单元权重

        Args:
            weights: 权重字典 {unit_id: weight}
            notify_manager: 是否通知管理器保存配置
        """
        for unit_id, weight in weights.items():
            if unit_id in self.scoring_units:
                self.unit_weights[unit_id] = weight
        self.logger.info(f"批量更新分组 {self.group_id} 单元权重")

        # 通知管理器保存配置（如果有的话）
        if notify_manager and hasattr(self, '_manager_callback'):
            self._manager_callback()
    
    def calculate_weighted_score(self, unit_results: Dict[str, Any]) -> float:
        """
        计算加权分数
        
        Args:
            unit_results: 单元计分结果字典
            
        Returns:
            float: 加权分数
        """
        total_weighted_score = 0.0
        total_weight = 0.0
        
        for unit_id, result in unit_results.items():
            if unit_id in self.unit_weights:
                weight = self.unit_weights[unit_id]
                score = result.get('score', 0.0) if isinstance(result, dict) else result.score
                total_weighted_score += score * weight
                total_weight += weight
        
        if total_weight > 0:
            return total_weighted_score / total_weight
        else:
            return 0.0
    
    def validate_units(self, data: pd.DataFrame) -> List[str]:
        """
        验证所有计分单元是否可用
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            List[str]: 可用的单元ID列表
        """
        valid_units = []
        for unit_id, unit in self.scoring_units.items():
            if unit.enabled and unit.validate_data(data):
                valid_units.append(unit_id)
        return valid_units
    
    def get_info(self) -> Dict[str, Any]:
        """
        获取分组信息
        
        Returns:
            Dict[str, Any]: 分组信息
        """
        return {
            'group_id': self.group_id,
            'name': self.name,
            'description': self.description,
            'weight': self.weight,
            'enabled': self.enabled,
            'scoring_units': list(self.scoring_units.keys()),
            'unit_weights': self.unit_weights,
            'config': self.config,
            'stocks': getattr(self, 'stocks', [])  # 添加股票列表
        }
    
    def enable(self) -> None:
        """启用分组"""
        self.enabled = True
        self.logger.info(f"分组 {self.group_id} 已启用")
    
    def disable(self) -> None:
        """禁用分组"""
        self.enabled = False
        self.logger.info(f"分组 {self.group_id} 已禁用")
    
    def update_config(self, config: Dict[str, Any]) -> None:
        """
        更新配置参数
        
        Args:
            config: 新的配置参数
        """
        self.config.update(config)
        self.logger.info(f"分组 {self.group_id} 配置已更新")
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取当前配置
        
        Returns:
            Dict[str, Any]: 当前配置参数
        """
        return self.config.copy()
    
    def add_to_history(self, result: GroupResult) -> None:
        """
        添加分组结果到历史记录
        
        Args:
            result: 分组结果
        """
        self.group_history.append(result)
        
        # 保持历史记录在合理范围内
        if len(self.group_history) > 500:
            self.group_history = self.group_history[-250:]
    
    def get_latest_result(self) -> Optional[GroupResult]:
        """
        获取最新的分组结果
        
        Returns:
            Optional[GroupResult]: 最新的分组结果，如果没有则返回None
        """
        return self.group_history[-1] if self.group_history else None
    
    def score_with_validation(self, data: pd.DataFrame) -> GroupResult:
        """
        带验证的分组计分方法
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            GroupResult: 分组计分结果
        """
        try:
            # 检查是否启用
            if not self.enabled:
                return GroupResult(
                    group_id=self.group_id,
                    group_name=self.name,
                    total_score=0.0,
                    weighted_score=0.0,
                    unit_results={},
                    description=f"分组 {self.group_id} 已禁用"
                )
            
            # 验证计分单元
            valid_units = self.validate_units(data)
            if not valid_units:
                return GroupResult(
                    group_id=self.group_id,
                    group_name=self.name,
                    total_score=0.0,
                    weighted_score=0.0,
                    unit_results={},
                    description=f"分组 {self.group_id} 没有可用的计分单元"
                )
            
            # 计算分组分数
            result = self.calculate_group_score(data)
            
            # 添加到历史记录
            self.add_to_history(result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"分组 {self.group_id} 计算失败: {str(e)}")
            return GroupResult(
                group_id=self.group_id,
                group_name=self.name,
                total_score=0.0,
                weighted_score=0.0,
                unit_results={},
                description=f"计算错误: {str(e)}"
            )
