#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义分组
用户可以自定义的计分单元分组
"""

import pandas as pd
from typing import Dict, Any, List
from .base_group import BaseGroup, GroupResult


class CustomGroup(BaseGroup):
    """
    自定义分组
    
    允许用户创建自定义的计分单元分组，可以：
    - 自由组合任意计分单元
    - 自定义权重分配
    - 自定义评分规则
    - 自定义分组名称和描述
    """
    
    def __init__(self,
                 group_id: str,
                 name: str,
                 description: str = '',
                 weight: float = 1.0,
                 custom_config: Dict[str, Any] = None):
        """
        初始化自定义分组
        
        Args:
            group_id: 分组唯一标识符
            name: 分组名称
            description: 分组描述
            weight: 分组权重
            custom_config: 自定义配置
        """
        super().__init__(
            group_id=group_id,
            name=name,
            description=description,
            weight=weight
        )
        
        # 自定义分组配置
        self.config = custom_config or {
            'scoring_method': 'weighted_average',  # 评分方法: weighted_average, max, min, custom
            'score_multiplier': 1.0,  # 分数倍数
            'signal_threshold': 0.5,  # 信号阈值
            'confidence_boost': 0.0,  # 置信度提升
            'custom_rules': {}  # 自定义规则
        }
        
        # 用户定义的评分函数
        self.custom_scoring_function = None
    
    def calculate_group_score(self, data: pd.DataFrame) -> GroupResult:
        """
        计算自定义分组分数
        
        Args:
            data: 股票数据DataFrame
            
        Returns:
            GroupResult: 自定义分组计分结果
        """
        try:
            unit_results = {}
            total_score = 0.0
            valid_unit_count = 0
            
            # 计算每个计分单元的分数
            for unit_id, unit in self.scoring_units.items():
                if unit.enabled:
                    try:
                        result = unit.score_with_validation(data)
                        unit_results[unit_id] = result.to_dict()
                        
                        if result.score != 0.0:  # 只计算有效分数
                            total_score += result.score
                            valid_unit_count += 1
                            
                    except Exception as e:
                        self.logger.warning(f"计分单元 {unit_id} 计算失败: {str(e)}")
                        unit_results[unit_id] = {
                            'score': 0.0,
                            'signal': 'neutral',
                            'description': f'计算失败: {str(e)}'
                        }
            
            # 根据配置的评分方法计算分数
            final_score, confidence = self._calculate_custom_score(unit_results, data)
            
            # 生成描述
            description = self._generate_custom_description(
                final_score, valid_unit_count, unit_results
            )
            
            return GroupResult(
                group_id=self.group_id,
                group_name=self.name,
                total_score=total_score,
                weighted_score=final_score,
                unit_results=unit_results,
                confidence=confidence,
                description=description,
                metadata={
                    'valid_unit_count': valid_unit_count,
                    'scoring_method': self.config.get('scoring_method'),
                    'config': self.config
                }
            )
            
        except Exception as e:
            self.logger.error(f"自定义分组计算失败: {str(e)}")
            return GroupResult(
                group_id=self.group_id,
                group_name=self.name,
                total_score=0.0,
                weighted_score=0.0,
                unit_results={},
                description=f'自定义分组计算错误: {str(e)}'
            )
    
    def _calculate_custom_score(self, unit_results: Dict[str, Any], 
                               data: pd.DataFrame) -> tuple:
        """
        根据自定义规则计算分数
        
        Args:
            unit_results: 单元结果字典
            data: 股票数据
            
        Returns:
            tuple: (最终分数, 置信度)
        """
        scoring_method = self.config.get('scoring_method', 'weighted_average')
        score_multiplier = self.config.get('score_multiplier', 1.0)
        confidence_boost = self.config.get('confidence_boost', 0.0)
        
        try:
            if self.custom_scoring_function:
                # 使用用户自定义的评分函数
                score, confidence = self.custom_scoring_function(unit_results, data, self.config)
            else:
                # 使用内置评分方法
                score, confidence = self._builtin_scoring_methods(
                    scoring_method, unit_results, data
                )
            
            # 应用分数倍数
            score *= score_multiplier
            
            # 应用置信度提升
            confidence = min(1.0, confidence + confidence_boost)
            
            return score, confidence
            
        except Exception as e:
            self.logger.warning(f"自定义评分计算失败: {str(e)}")
            return 0.0, 0.5
    
    def _builtin_scoring_methods(self, method: str, unit_results: Dict[str, Any], 
                                data: pd.DataFrame) -> tuple:
        """
        内置评分方法
        
        Args:
            method: 评分方法名称
            unit_results: 单元结果字典
            data: 股票数据
            
        Returns:
            tuple: (分数, 置信度)
        """
        scores = [result.get('score', 0.0) for result in unit_results.values()]
        confidences = [result.get('confidence', 0.5) for result in unit_results.values()]
        
        if not scores:
            return 0.0, 0.5
        
        if method == 'weighted_average':
            # 加权平均
            score = self.calculate_weighted_score(unit_results)
            confidence = sum(confidences) / len(confidences)
            
        elif method == 'max':
            # 取最大值
            score = max(scores)
            max_index = scores.index(score)
            confidence = confidences[max_index]
            
        elif method == 'min':
            # 取最小值
            score = min(scores)
            min_index = scores.index(score)
            confidence = confidences[min_index]
            
        elif method == 'median':
            # 取中位数
            import numpy as np
            score = np.median(scores)
            confidence = np.median(confidences)
            
        elif method == 'consensus':
            # 共识方法：只有当大多数指标一致时才给出强信号
            positive_scores = [s for s in scores if s > 0]
            negative_scores = [s for s in scores if s < 0]
            
            if len(positive_scores) > len(negative_scores) * 2:
                score = sum(positive_scores) / len(positive_scores)
                confidence = 0.8
            elif len(negative_scores) > len(positive_scores) * 2:
                score = sum(negative_scores) / len(negative_scores)
                confidence = 0.8
            else:
                score = sum(scores) / len(scores)
                confidence = 0.3
                
        else:
            # 默认使用加权平均
            score = self.calculate_weighted_score(unit_results)
            confidence = sum(confidences) / len(confidences)
        
        return score, confidence
    
    def _generate_custom_description(self, score: float, valid_count: int, 
                                    unit_results: Dict[str, Any]) -> str:
        """
        生成自定义描述
        
        Args:
            score: 最终分数
            valid_count: 有效单元数量
            unit_results: 单元结果字典
            
        Returns:
            str: 自定义描述
        """
        scoring_method = self.config.get('scoring_method', 'weighted_average')
        
        if score >= 2.0:
            signal_desc = "强烈看涨"
        elif score >= 1.0:
            signal_desc = "看涨"
        elif score >= 0.5:
            signal_desc = "轻微看涨"
        elif score <= -2.0:
            signal_desc = "强烈看跌"
        elif score <= -1.0:
            signal_desc = "看跌"
        elif score <= -0.5:
            signal_desc = "轻微看跌"
        else:
            signal_desc = "中性"
        
        return f"{self.name}({score:.2f}): {signal_desc} (方法:{scoring_method}, 指标:{valid_count}个)"
    
    def set_custom_scoring_function(self, func):
        """
        设置自定义评分函数
        
        Args:
            func: 自定义评分函数，签名为 func(unit_results, data, config) -> (score, confidence)
        """
        self.custom_scoring_function = func
        self.logger.info(f"自定义分组 {self.group_id} 设置了自定义评分函数")
    
    def add_custom_rule(self, rule_name: str, rule_config: Dict[str, Any]) -> None:
        """
        添加自定义规则
        
        Args:
            rule_name: 规则名称
            rule_config: 规则配置
        """
        if 'custom_rules' not in self.config:
            self.config['custom_rules'] = {}
        
        self.config['custom_rules'][rule_name] = rule_config
        self.logger.info(f"自定义分组 {self.group_id} 添加规则: {rule_name}")
    
    def remove_custom_rule(self, rule_name: str) -> None:
        """
        移除自定义规则
        
        Args:
            rule_name: 规则名称
        """
        if 'custom_rules' in self.config and rule_name in self.config['custom_rules']:
            del self.config['custom_rules'][rule_name]
            self.logger.info(f"自定义分组 {self.group_id} 移除规则: {rule_name}")
    
    def get_custom_rules(self) -> Dict[str, Any]:
        """
        获取所有自定义规则
        
        Returns:
            Dict[str, Any]: 自定义规则字典
        """
        return self.config.get('custom_rules', {})
    
    def update_config(self, config: Dict[str, Any]) -> None:
        """
        更新自定义分组配置
        
        Args:
            config: 新的配置参数
        """
        super().update_config(config)
        
        # 自定义分组特有配置更新
        for key in ['scoring_method', 'score_multiplier', 'signal_threshold', 
                   'confidence_boost', 'custom_rules']:
            if key in config:
                self.config[key] = config[key]
