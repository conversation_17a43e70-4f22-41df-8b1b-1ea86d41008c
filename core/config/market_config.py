"""
市场配置文件
定义不同市场类型的交易时间和聚合规则
"""

from typing import Dict, List, Tuple
from datetime import time


class MarketConfig:
    """市场配置管理器"""
    
    # 市场类型常量
    STOCK = "stock"
    FUTURES = "futures"
    CRYPTO = "crypto"
    
    def __init__(self):
        self.configs = self._load_market_configs()
    
    def _load_market_configs(self) -> Dict:
        """加载市场配置"""
        return {
            self.STOCK: {
                "name": "股票市场",
                "description": "T+1交易，周一到周五",
                "trading_days": [0, 1, 2, 3, 4],  # 周一到周五
                "resample_rule": "W-FRI",
                "session_times": [
                    {"start": "09:30", "end": "11:30", "name": "上午盘"},
                    {"start": "13:00", "end": "15:00", "name": "下午盘"}
                ],
                "timezone": "Asia/Shanghai",
                "has_night_session": False,
                "holidays_enabled": True
            },
            
            self.FUTURES: {
                "name": "期货市场",
                "description": "包含夜盘的期货交易",
                "trading_days": [0, 1, 2, 3, 4],  # 周一到周五
                "resample_rule": "W-FRI",
                "session_times": [
                    {"start": "21:00", "end": "23:00", "name": "夜盘1"},
                    {"start": "23:30", "end": "01:00", "name": "夜盘2"},
                    {"start": "09:00", "end": "10:15", "name": "上午盘1"},
                    {"start": "10:30", "end": "11:30", "name": "上午盘2"},
                    {"start": "13:30", "end": "15:00", "name": "下午盘"}
                ],
                "timezone": "Asia/Shanghai",
                "has_night_session": True,
                "holidays_enabled": True,
                "night_session_adjustment": True  # 夜盘数据需要调整到下一交易日
            },
            
            self.CRYPTO: {
                "name": "加密货币市场",
                "description": "7×24小时全天候交易",
                "trading_days": [0, 1, 2, 3, 4, 5, 6],  # 全周
                "resample_rule": "W-SUN",  # 以周日结束
                "session_times": [
                    {"start": "00:00", "end": "23:59", "name": "全天交易"}
                ],
                "timezone": "UTC",
                "has_night_session": False,
                "holidays_enabled": False
            }
        }
    
    def get_config(self, market_type: str) -> Dict:
        """获取指定市场类型的配置"""
        if market_type not in self.configs:
            raise ValueError(f"不支持的市场类型: {market_type}")
        return self.configs[market_type]
    
    def get_trading_days(self, market_type: str) -> List[int]:
        """获取交易日（0=周一, 6=周日）"""
        return self.get_config(market_type)["trading_days"]
    
    def get_resample_rule(self, market_type: str) -> str:
        """获取周线聚合规则"""
        return self.get_config(market_type)["resample_rule"]
    
    def has_night_session(self, market_type: str) -> bool:
        """是否有夜盘交易"""
        return self.get_config(market_type).get("has_night_session", False)
    
    def get_session_times(self, market_type: str) -> List[Dict]:
        """获取交易时段"""
        return self.get_config(market_type)["session_times"]
    
    def get_timezone(self, market_type: str) -> str:
        """获取时区"""
        return self.get_config(market_type)["timezone"]
    
    def is_trading_day(self, market_type: str, weekday: int) -> bool:
        """判断是否为交易日"""
        trading_days = self.get_trading_days(market_type)
        return weekday in trading_days
    
    def get_market_info(self, market_type: str) -> str:
        """获取市场信息描述"""
        config = self.get_config(market_type)
        return f"{config['name']}: {config['description']}"


class MarketDetector:
    """市场类型检测器（基于数据断点分析）"""

    def __init__(self):
        self.config = MarketConfig()

    def detect_market_type_by_data(self, dates: List, symbol: str = None) -> str:
        """
        基于数据断点检测市场类型（主要方法）

        Args:
            dates: 日期列表
            symbol: 股票代码（用于辅助判断）

        Returns:
            市场类型字符串
        """
        try:
            # 转换为pandas datetime
            import pandas as pd
            date_series = pd.to_datetime(dates)

            # 分析数据断点模式
            gap_pattern = self._analyze_date_gaps(date_series)

            # 根据断点模式判断市场类型
            market_type = self._classify_by_gap_pattern(gap_pattern, symbol)

            return market_type

        except Exception as e:
            # 如果数据分析失败，回退到代码模式检测
            if symbol:
                return self.detect_market_type_by_symbol(symbol)
            return MarketConfig.STOCK

    def _analyze_date_gaps(self, date_series) -> Dict:
        """
        分析日期序列的断点模式

        Args:
            date_series: pandas datetime序列

        Returns:
            断点分析结果字典
        """
        import pandas as pd
        import numpy as np

        # 计算日期差异
        date_diffs = date_series.diff().dropna()

        # 统计不同天数的间隔
        try:
            # 尝试多种方法处理时间差数据
            if hasattr(date_diffs, 'dt') and hasattr(date_diffs.dt, 'days'):
                gap_days = date_diffs.dt.days
            else:
                # 处理TimedeltaIndex或其他时间差格式
                gap_days = pd.Series([x.days if hasattr(x, 'days') else int(x.total_seconds()/86400) for x in date_diffs])
        except (AttributeError, TypeError) as e:
            # 最终回退：手动转换为天数
            gap_days = pd.Series([int(abs(x).total_seconds()/86400) if hasattr(x, 'total_seconds') else 1 for x in date_diffs])
        
        gap_counts = gap_days.value_counts().sort_index()

        # 分析周模式
        weekday_pattern = self._analyze_weekday_pattern(date_series)

        # 计算连续性指标
        total_days = (date_series.max() - date_series.min()).days + 1
        actual_days = len(date_series)
        continuity_ratio = actual_days / total_days if total_days > 0 else 0

        return {
            'gap_counts': gap_counts.to_dict(),
            'weekday_pattern': weekday_pattern,
            'continuity_ratio': continuity_ratio,
            'total_days': total_days,
            'actual_days': actual_days,
            'avg_gap': gap_days.mean() if len(gap_days) > 0 else 1,
            'max_gap': gap_days.max() if len(gap_days) > 0 else 1
        }

    def _analyze_weekday_pattern(self, date_series) -> Dict:
        """分析周模式"""
        import pandas as pd

        # 统计各个工作日的出现频率
        try:
            weekdays = date_series.weekday  # 对于DatetimeIndex
        except AttributeError:
            weekdays = date_series.dt.weekday  # 对于Series
        weekday_counts = weekdays.value_counts().sort_index()

        # 计算工作日vs周末的比例
        weekday_data = weekday_counts.reindex(range(7), fill_value=0)
        workday_count = weekday_data[0:5].sum()  # 周一到周五
        weekend_count = weekday_data[5:7].sum()  # 周六到周日

        return {
            'weekday_counts': weekday_counts.to_dict(),
            'workday_count': workday_count,
            'weekend_count': weekend_count,
            'weekend_ratio': weekend_count / (workday_count + weekend_count) if (workday_count + weekend_count) > 0 else 0
        }

    def _classify_by_gap_pattern(self, gap_pattern: Dict, symbol: str = None) -> str:
        """
        根据断点模式分类市场类型

        Args:
            gap_pattern: 断点分析结果
            symbol: 股票代码（辅助判断）

        Returns:
            市场类型
        """
        continuity_ratio = gap_pattern['continuity_ratio']
        weekend_ratio = gap_pattern['weekday_pattern']['weekend_ratio']
        avg_gap = gap_pattern['avg_gap']
        max_gap = gap_pattern['max_gap']

        # 判断逻辑（基于您的聪明想法：断点模式识别）
        if continuity_ratio > 0.95 and weekend_ratio > 0.25:
            # 高连续性 + 有周末数据 = 加密货币 (7×24交易)
            return MarketConfig.CRYPTO

        elif continuity_ratio < 0.75 and weekend_ratio < 0.05:
            # 低连续性 + 无周末数据 = 传统交易市场
            if avg_gap <= 1.5 and max_gap <= 3:
                # 平均间隔约1天，最大间隔不超过3天 = 股票
                return MarketConfig.STOCK
            elif max_gap > 3 or avg_gap > 1.5:
                # 间隔较大，可能有更多节假日 = 期货
                return MarketConfig.FUTURES
            else:
                return MarketConfig.STOCK

        elif continuity_ratio >= 0.75 and continuity_ratio <= 0.95 and weekend_ratio < 0.1:
            # 中等连续性 + 少量周末数据 = 可能是期货（有夜盘）
            return MarketConfig.FUTURES

        else:
            # 无法明确判断，使用代码模式检测作为备选
            if symbol:
                symbol_based = self.detect_market_type_by_symbol(symbol)
                # 如果代码检测是期货，优先使用代码检测结果
                if symbol_based == MarketConfig.FUTURES:
                    return MarketConfig.FUTURES
                return symbol_based
            return MarketConfig.STOCK

    def detect_market_type_by_symbol(self, symbol: str) -> str:
        """
        根据股票代码检测市场类型（备选方法）

        Args:
            symbol: 股票/期货/加密货币代码

        Returns:
            市场类型字符串
        """
        symbol = symbol.upper()

        # 加密货币检测
        crypto_patterns = ['BTC', 'ETH', 'USDT', 'BNB', 'ADA', 'DOT', 'DOGE']
        if any(pattern in symbol for pattern in crypto_patterns):
            return MarketConfig.CRYPTO

        # 期货检测
        futures_patterns = ['FUT', 'F_', '_F', 'FUTURE']
        if any(pattern in symbol for pattern in futures_patterns):
            return MarketConfig.FUTURES

        # 中国期货代码检测
        if len(symbol) >= 4 and symbol[:2].isalpha() and symbol[2:].isdigit():
            return MarketConfig.FUTURES

        # 默认为股票
        return MarketConfig.STOCK

    def detect_market_type(self, symbol: str) -> str:
        """向后兼容的接口"""
        return self.detect_market_type_by_symbol(symbol)
    
    def get_recommended_config(self, symbol: str) -> Dict:
        """获取推荐的市场配置"""
        market_type = self.detect_market_type(symbol)
        return self.config.get_config(market_type)


# 全局配置实例
market_config = MarketConfig()
market_detector = MarketDetector()


def get_market_aggregator(symbol: str = None, market_type: str = None, dates: List = None):
    """
    注意：WeeklyDataAggregator已删除，数据库中已有真实周线数据
    直接查询period='weekly'即可
    """
    raise NotImplementedError("WeeklyDataAggregator已删除，请使用数据库中的真实周线数据")

def analyze_market_data(dates: List, symbol: str = None) -> Dict:
    """
    分析市场数据特征

    Args:
        dates: 日期列表
        symbol: 股票代码

    Returns:
        市场分析结果
    """
    detector = MarketDetector()

    # 基于数据的检测
    data_based_type = detector.detect_market_type_by_data(dates, symbol)

    # 基于代码的检测（如果有symbol）
    symbol_based_type = detector.detect_market_type_by_symbol(symbol) if symbol else None

    # 获取详细的断点分析
    import pandas as pd
    date_series = pd.to_datetime(dates)
    gap_pattern = detector._analyze_date_gaps(date_series)

    return {
        'symbol': symbol,
        'data_based_detection': data_based_type,
        'symbol_based_detection': symbol_based_type,
        'recommended_type': data_based_type,  # 优先使用数据驱动的结果
        'gap_analysis': gap_pattern,
        'market_config': market_config.get_config(data_based_type)
    }


# 使用示例
if __name__ == "__main__":
    # 测试市场检测
    test_symbols = ["08340", "BTCUSDT", "CU2401", "ETHBTC", "000001"]
    
    for symbol in test_symbols:
        market_type = market_detector.detect_market_type(symbol)
        config = market_config.get_config(market_type)
        print(f"{symbol} -> {market_type}: {config['name']}")
