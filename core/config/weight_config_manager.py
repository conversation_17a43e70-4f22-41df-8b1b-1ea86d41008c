#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
权重配置管理器
专门管理评分单元权重配置的保存和加载
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path


class WeightConfigManager:
    """
    权重配置管理器
    
    负责管理所有权重配置的保存、加载和历史记录：
    - 分组权重配置
    - 计分单元权重配置
    - 配置历史记录
    - 自动保存机制
    """
    
    def __init__(self, config_dir: str = 'config'):
        """
        初始化权重配置管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.logger = logging.getLogger(__name__)
        self.config_dir = Path(config_dir)
        
        # 确保配置目录存在
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置文件路径
        self.weight_config_file = self.config_dir / 'weight_config.json'
        self.history_file = self.config_dir / 'weight_history.json'
        
        # 当前权重配置
        self.current_config = {
            'group_weights': {},
            'unit_weights': {},
            'last_updated': None,
            'version': '1.0'
        }
        
        # 配置历史记录
        self.config_history = []
        
        # 加载现有配置
        self.load_config()
        self.load_history()
    
    def save_weight_config(self, group_weights: Dict[str, float], 
                          unit_weights: Dict[str, Dict[str, float]], 
                          auto_backup: bool = True) -> bool:
        """
        保存权重配置
        
        Args:
            group_weights: 分组权重 {group_id: weight}
            unit_weights: 计分单元权重 {group_id: {unit_id: weight}}
            auto_backup: 是否自动备份到历史记录
            
        Returns:
            bool: 是否保存成功
        """
        try:
            # 备份当前配置到历史记录
            if auto_backup and self.current_config.get('last_updated'):
                self._backup_current_config()
            
            # 更新当前配置
            self.current_config.update({
                'group_weights': group_weights.copy(),
                'unit_weights': unit_weights.copy(),
                'last_updated': datetime.now().isoformat(),
                'version': '1.0'
            })
            
            # 保存到文件
            with open(self.weight_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.current_config, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"权重配置已保存到 {self.weight_config_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存权重配置失败: {str(e)}")
            return False
    
    def load_config(self) -> bool:
        """
        加载权重配置
        
        Returns:
            bool: 是否加载成功
        """
        try:
            if self.weight_config_file.exists():
                with open(self.weight_config_file, 'r', encoding='utf-8') as f:
                    self.current_config = json.load(f)
                
                self.logger.info(f"权重配置已从 {self.weight_config_file} 加载")
                return True
            else:
                self.logger.info("权重配置文件不存在，使用默认配置")
                return False
                
        except Exception as e:
            self.logger.error(f"加载权重配置失败: {str(e)}")
            return False
    
    def get_group_weights(self) -> Dict[str, float]:
        """获取分组权重配置"""
        return self.current_config.get('group_weights', {}).copy()
    
    def get_unit_weights(self) -> Dict[str, Dict[str, float]]:
        """获取计分单元权重配置"""
        return self.current_config.get('unit_weights', {}).copy()
    
    def update_group_weight(self, group_id: str, weight: float, auto_save: bool = True) -> bool:
        """
        更新单个分组权重
        
        Args:
            group_id: 分组ID
            weight: 新权重
            auto_save: 是否自动保存
            
        Returns:
            bool: 是否更新成功
        """
        try:
            if 'group_weights' not in self.current_config:
                self.current_config['group_weights'] = {}
            
            self.current_config['group_weights'][group_id] = weight
            self.current_config['last_updated'] = datetime.now().isoformat()
            
            if auto_save:
                return self._save_current_config()
            
            return True
            
        except Exception as e:
            self.logger.error(f"更新分组权重失败: {str(e)}")
            return False
    
    def update_unit_weight(self, group_id: str, unit_id: str, weight: float, auto_save: bool = True) -> bool:
        """
        更新单个计分单元权重
        
        Args:
            group_id: 分组ID
            unit_id: 计分单元ID
            weight: 新权重
            auto_save: 是否自动保存
            
        Returns:
            bool: 是否更新成功
        """
        try:
            if 'unit_weights' not in self.current_config:
                self.current_config['unit_weights'] = {}
            
            if group_id not in self.current_config['unit_weights']:
                self.current_config['unit_weights'][group_id] = {}
            
            self.current_config['unit_weights'][group_id][unit_id] = weight
            self.current_config['last_updated'] = datetime.now().isoformat()
            
            if auto_save:
                return self._save_current_config()
            
            return True
            
        except Exception as e:
            self.logger.error(f"更新计分单元权重失败: {str(e)}")
            return False
    
    def _save_current_config(self) -> bool:
        """保存当前配置到文件"""
        try:
            with open(self.weight_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.current_config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {str(e)}")
            return False
    
    def _backup_current_config(self) -> None:
        """备份当前配置到历史记录"""
        try:
            backup_entry = {
                'timestamp': self.current_config.get('last_updated'),
                'config': self.current_config.copy()
            }
            
            self.config_history.append(backup_entry)
            
            # 限制历史记录数量（保留最近50个）
            if len(self.config_history) > 50:
                self.config_history = self.config_history[-50:]
            
            # 保存历史记录
            self.save_history()
            
        except Exception as e:
            self.logger.error(f"备份配置失败: {str(e)}")
    
    def save_history(self) -> bool:
        """保存配置历史记录"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_history, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            self.logger.error(f"保存历史记录失败: {str(e)}")
            return False
    
    def load_history(self) -> bool:
        """加载配置历史记录"""
        try:
            if self.history_file.exists():
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    self.config_history = json.load(f)
                return True
            return False
        except Exception as e:
            self.logger.error(f"加载历史记录失败: {str(e)}")
            return False
    
    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息"""
        return {
            'last_updated': self.current_config.get('last_updated'),
            'version': self.current_config.get('version'),
            'group_count': len(self.current_config.get('group_weights', {})),
            'unit_count': sum(len(units) for units in self.current_config.get('unit_weights', {}).values()),
            'history_count': len(self.config_history),
            'config_file': str(self.weight_config_file),
            'history_file': str(self.history_file)
        }
    
    def restore_from_history(self, index: int = -1) -> bool:
        """
        从历史记录恢复配置
        
        Args:
            index: 历史记录索引，-1表示最新的
            
        Returns:
            bool: 是否恢复成功
        """
        try:
            if not self.config_history:
                self.logger.warning("没有历史记录可恢复")
                return False
            
            if abs(index) > len(self.config_history):
                self.logger.error(f"历史记录索引超出范围: {index}")
                return False
            
            # 备份当前配置
            self._backup_current_config()
            
            # 恢复历史配置
            history_entry = self.config_history[index]
            self.current_config = history_entry['config'].copy()
            self.current_config['last_updated'] = datetime.now().isoformat()
            
            # 保存恢复的配置
            return self._save_current_config()
            
        except Exception as e:
            self.logger.error(f"恢复历史配置失败: {str(e)}")
            return False
