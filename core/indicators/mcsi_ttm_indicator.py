#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCSI TTM指标模块别名
为了保持导入路径的一致性
"""

# 从TV-code/py-code导入实际的实现
import sys
import os

# 添加TV-code/py-code到路径
tv_code_path = os.path.join(os.path.dirname(__file__), '../../TV-code/py-code')
if tv_code_path not in sys.path:
    sys.path.insert(0, tv_code_path)

# 直接导入模块
import mcsi_ttm
MCSITTMIndicator = mcsi_ttm.MCSITTMIndicator

__all__ = ['MCSITTMIndicator']
