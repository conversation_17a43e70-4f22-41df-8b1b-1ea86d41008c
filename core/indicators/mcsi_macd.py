#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCSI MACD指标
从Pine Script转换为Python实现
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import logging

class MCSIMACDIndicator:
    """MCSI MACD技术指标"""
    
    def __init__(self, fast_length=19, slow_length=39, signal_length=9, lookback_period=20):
        """
        初始化MCSI MACD指标

        Args:
            fast_length: 快线长度，默认19
            slow_length: 慢线长度，默认39
            signal_length: 信号长度，默认9
            lookback_period: 动态阈值计算周期，默认20
        """
        self.fast_length = fast_length
        self.slow_length = slow_length
        self.signal_length = signal_length
        self.lookback_period = lookback_period
        self.logger = logging.getLogger(__name__)

        # 添加状态变量 - 对应Pine Script的var变量
        self.reset_state()

    def reset_state(self):
        """
        重置状态变量 - 对应Pine Script的var变量
        在每次新的计算序列开始时调用
        """
        # 对应Pine Script: var float macdScore = 0.0
        self.macd_score = 0.0

    def calculate_ema(self, data: np.ndarray, period: int) -> np.ndarray:
        """计算指数移动平均线"""
        if len(data) < period:
            return np.full(len(data), np.nan)
            
        alpha = 2.0 / (period + 1)
        ema = np.zeros(len(data))
        ema[0] = data[0]
        
        for i in range(1, len(data)):
            ema[i] = alpha * data[i] + (1 - alpha) * ema[i-1]
            
        return ema
    
    def calculate_sma(self, data: np.ndarray, period: int) -> np.ndarray:
        """计算简单移动平均线"""
        if len(data) < period:
            return np.full(len(data), np.nan)
            
        sma = np.full(len(data), np.nan)
        for i in range(period-1, len(data)):
            sma[i] = np.mean(data[i-period+1:i+1])
            
        return sma
    
    def calculate_stdev(self, data: np.ndarray, period: int) -> np.ndarray:
        """计算标准差"""
        if len(data) < period:
            return np.full(len(data), np.nan)
            
        stdev = np.full(len(data), np.nan)
        for i in range(period-1, len(data)):
            stdev[i] = np.std(data[i-period+1:i+1], ddof=0)
            
        return stdev
    
    def calculate(self, close_prices: np.ndarray) -> Dict:
        """
        计算MCSI MACD指标
        
        Args:
            close_prices: 收盘价数组
            
        Returns:
            包含MACD相关数据的字典
        """
        try:
            if len(close_prices) < max(self.fast_length, self.slow_length, self.lookback_period):
                self.logger.warning("数据长度不足以计算MCSI MACD")
                return self._empty_result(len(close_prices))
            
            # 计算快慢EMA
            fast_ma = self.calculate_ema(close_prices, self.fast_length)
            slow_ma = self.calculate_ema(close_prices, self.slow_length)
            
            # 计算MACD线
            macd = fast_ma - slow_ma
            
            # 计算信号线（使用SMA）
            signal = self.calculate_sma(macd, self.signal_length)
            
            # 计算柱状图
            histogram = macd - signal
            
            # 计算动态阈值
            hist_stdev = self.calculate_stdev(histogram, self.lookback_period)
            dynamic_threshold = hist_stdev * 1.5
            
            # 状态判断
            hist_above_threshold = histogram > dynamic_threshold
            hist_below_threshold = histogram < -dynamic_threshold
            hist_near_zero = np.abs(histogram) <= dynamic_threshold
            
            # 柱状图颜色状态
            hist_a_is_up = (histogram > np.roll(histogram, 1)) & (histogram > 0)
            hist_a_is_down = (histogram < np.roll(histogram, 1)) & (histogram > 0)
            hist_b_is_down = (histogram < np.roll(histogram, 1)) & (histogram <= 0)
            hist_b_is_up = (histogram > np.roll(histogram, 1)) & (histogram <= 0)
            
            # 检测颜色变化
            hist_a_is_up_prev = np.roll(hist_a_is_up, 1)
            hist_a_is_down_prev = np.roll(hist_a_is_down, 1)
            hist_b_is_down_prev = np.roll(hist_b_is_down, 1)
            hist_b_is_up_prev = np.roll(hist_b_is_up, 1)
            
            hist_color_changed = (
                (hist_a_is_up_prev & ~hist_a_is_up) |
                (hist_a_is_down_prev & ~hist_a_is_down) |
                (hist_b_is_down_prev & ~hist_b_is_down) |
                (hist_b_is_up_prev & ~hist_b_is_up)
            )
            
            # 重置状态变量（对应Pine Script每次新计算）
            self.reset_state()

            # 计算MACD评分 - 使用修复后的逻辑
            macd_score = self._calculate_macd_score_fixed(
                histogram, dynamic_threshold, hist_near_zero,
                hist_above_threshold, hist_below_threshold,
                hist_a_is_up_prev, hist_a_is_down,
                hist_b_is_down_prev, hist_b_is_up,
                hist_color_changed
            )
            
            # 计算柱状图颜色
            hist_colors = self._calculate_histogram_colors(
                hist_a_is_up, hist_a_is_down, hist_b_is_down, hist_b_is_up
            )
            
            return {
                'macd': macd,
                'signal': signal,
                'histogram': histogram,
                'dynamic_threshold': dynamic_threshold,
                'macd_score': macd_score,
                'hist_colors': hist_colors,
                'fast_ma': fast_ma,
                'slow_ma': slow_ma
            }
            
        except Exception as e:
            self.logger.error(f"MCSI MACD计算失败: {str(e)}")
            return self._empty_result(len(close_prices))

    def _calculate_macd_score_fixed(self, histogram, dynamic_threshold, hist_near_zero,
                                   hist_above_threshold, hist_below_threshold,
                                   hist_a_is_up_prev, hist_a_is_down,
                                   hist_b_is_down_prev, hist_b_is_up,
                                   hist_color_changed):
        """
        修复后的MACD评分计算 - 完全对齐Pine Script逻辑

        Pine Script关键逻辑:
        - 使用var变量在K线间保持状态
        - 精确的if-elif-else条件判断顺序
        - 正确的分数维持机制
        """
        macd_score = np.zeros(len(histogram))

        # 从第二个K线开始计算（因为需要比较前一个K线）
        for i in range(1, len(histogram)):
            # === MACD评分计算 ===
            # 对应Pine Script: if histNearZero
            if hist_near_zero[i]:
                # macdScore := 0  // 忽略阈值附近的信号
                macd_score[i] = 0
                self.macd_score = 0

            # 对应Pine Script: else if histAboveThreshold and histA_IsUp[1] and histA_IsDown
            elif (hist_above_threshold[i] and hist_a_is_up_prev[i] and hist_a_is_down[i]):
                # 高位由上升转下降（卖出）
                if dynamic_threshold[i] != 0:
                    relative_height = abs(histogram[i] / dynamic_threshold[i])
                    extra_score = min(50, (relative_height - 1) * 25)
                    score = -(50 + extra_score)
                else:
                    score = -50
                macd_score[i] = score
                self.macd_score = score

            # 对应Pine Script: else if histBelowThreshold and histB_IsDown[1] and histB_IsUp
            elif (hist_below_threshold[i] and hist_b_is_down_prev[i] and hist_b_is_up[i]):
                # 低位由下降转上升（买入）
                if dynamic_threshold[i] != 0:
                    relative_height = abs(histogram[i] / dynamic_threshold[i])
                    extra_score = min(50, (relative_height - 1) * 25)
                    score = 50 + extra_score
                else:
                    score = 50
                macd_score[i] = score
                self.macd_score = score

            # 对应Pine Script: else if histColorChanged
            elif hist_color_changed[i]:
                # macdScore := 0
                macd_score[i] = 0
                self.macd_score = 0

            # 对应Pine Script: else
            else:
                # macdScore := macdScore[1]  // 保持前一个分数
                macd_score[i] = self.macd_score

        return macd_score

    def _calculate_macd_score(self, histogram, dynamic_threshold, hist_near_zero,
                             hist_above_threshold, hist_below_threshold,
                             hist_a_is_up_prev, hist_a_is_down,
                             hist_b_is_down_prev, hist_b_is_up,
                             hist_color_changed):
        """计算MACD评分"""
        macd_score = np.zeros(len(histogram))
        
        for i in range(1, len(histogram)):
            # 严格按照Pine Script逻辑
            if hist_near_zero[i]:
                # if math.abs(histLine) <= dynamicThreshold
                macd_score[i] = 0
            elif (hist_above_threshold[i] and hist_a_is_up_prev[i] and hist_a_is_down[i]):
                # else if histLine > dynamicThreshold and histA_IsUp[1] and histA_IsDown
                relative_height = abs(histogram[i] / dynamic_threshold[i]) if dynamic_threshold[i] != 0 else 1
                extra_score = min(50, (relative_height - 1) * 25)
                macd_score[i] = -(50 + extra_score)
            elif (hist_below_threshold[i] and hist_b_is_down_prev[i] and hist_b_is_up[i]):
                # else if histLine < -dynamicThreshold and histB_IsDown[1] and histB_IsUp
                relative_height = abs(histogram[i] / dynamic_threshold[i]) if dynamic_threshold[i] != 0 else 1
                extra_score = min(50, (relative_height - 1) * 25)
                macd_score[i] = 50 + extra_score
            elif hist_color_changed[i]:
                # else if histColorChanged
                macd_score[i] = 0
            else:
                # else: macdScore := macdScore[1]
                macd_score[i] = macd_score[i-1] if i > 0 else 0
                
        return macd_score
    
    def _calculate_histogram_colors(self, hist_a_is_up, hist_a_is_down, hist_b_is_down, hist_b_is_up):
        """计算柱状图颜色"""
        colors = []
        for i in range(len(hist_a_is_up)):
            if hist_a_is_up[i]:
                colors.append('aqua')  # 青色
            elif hist_a_is_down[i]:
                colors.append('blue')  # 蓝色
            elif hist_b_is_down[i]:
                colors.append('red')   # 红色
            elif hist_b_is_up[i]:
                colors.append('maroon') # 栗色
            else:
                colors.append('gray')   # 灰色
        return colors
    
    def _empty_result(self, length: int) -> Dict:
        """返回空结果"""
        return {
            'macd': np.full(length, np.nan),
            'signal': np.full(length, np.nan),
            'histogram': np.full(length, np.nan),
            'dynamic_threshold': np.full(length, np.nan),
            'macd_score': np.full(length, 0.0),
            'hist_colors': ['gray'] * length,
            'fast_ma': np.full(length, np.nan),
            'slow_ma': np.full(length, np.nan)
        }
