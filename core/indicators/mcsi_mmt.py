#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCSI MMT指标
从Pine Script转换为Python实现
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import logging

class MCSIMMTIndicator:
    """MCSI MMT技术指标"""
    
    def __init__(self, leveling=10, cyclic_memory=34, channel_weight=0.5, divergence_weight=0.5):
        """
        初始化MCSI MMT指标

        Args:
            leveling: 水平化参数，默认10
            cyclic_memory: 循环记忆周期，默认34
            channel_weight: 轨道分数权重，默认0.5
            divergence_weight: 背离分数权重，默认0.5
        """
        self.leveling = leveling
        self.cyclic_memory = cyclic_memory
        self.channel_weight = channel_weight
        self.divergence_weight = divergence_weight
        self.logger = logging.getLogger(__name__)

        # 添加状态变量 - 对应Pine Script的var变量
        self.reset_state()

    def reset_state(self):
        """
        重置状态变量 - 对应Pine Script的var变量
        在每次新的计算序列开始时调用
        """
        # 对应Pine Script: var float lastChannelScore = 0.0
        self.last_channel_score = 0.0
        # 对应Pine Script: var float lastDivergenceScore = 0.0
        self.last_divergence_score = 0.0
        # 对应Pine Script: var int channelSignalDuration = 0
        self.channel_signal_duration = 0
        # 对应Pine Script: var int divergenceSignalDuration = 0
        self.divergence_signal_duration = 0

    def _calculate_momentum(self, csi_buffer: np.ndarray) -> np.ndarray:
        """
        计算动量 - 完全对齐Pine Script逻辑
        Pine Script: momentum = CSIBuffer - CSIBuffer[1]
        """
        momentum = np.zeros(len(csi_buffer))
        for i in range(1, len(csi_buffer)):
            momentum[i] = csi_buffer[i] - csi_buffer[i-1]
        return momentum

    def cycle1(self, i: int, wave_throttle: float, cycs: int) -> float:
        """Cycle1函数"""
        ret = 6.0 * wave_throttle + 1.0
        if i == 0:
            ret = 1.0 + wave_throttle
        elif i == 1:
            ret = 1.0 + wave_throttle * 5.0
        elif i == (cycs - 1):
            ret = 1.0 + wave_throttle
        elif i == (cycs - 2):
            ret = 1.0 + wave_throttle * 5.0
        return ret
    
    def cycle2(self, i: int, wave_throttle: float, cycs: int) -> float:
        """Cycle2函数"""
        ret = -4.0 * wave_throttle
        if i == 0:
            ret = -2.0 * wave_throttle
        elif i == (cycs - 1):
            ret = 0.0
        elif i == (cycs - 2):
            ret = -2.0 * wave_throttle
        return ret
    
    def cycle3(self, i: int, wave_throttle: float, cycs: int) -> float:
        """Cycle3函数"""
        ret = wave_throttle
        if i == (cycs - 1):
            ret = 0.0
        elif i == (cycs - 2):
            ret = 0.0
        return ret
    
    def iwtt_csi_processor(self, close_prices: np.ndarray, cycle_count: int) -> np.ndarray:
        """iWTT CSI处理器"""
        result = np.zeros(len(close_prices))
        cycs = 50
        wave_throttle = float(160 * cycle_count)
        
        for idx in range(cycs, len(close_prices)):
            wtt1, wtt2, wtt3, wtt4, wtt5 = 0.0, 0.0, 0.0, 0.0, 0.0
            _wtt1, _wtt2, _wtt3, _wtt4, _wtt5 = 0.0, 0.0, 0.0, 0.0, 0.0
            
            for i in range(cycs):
                swing = self.cycle1(i, wave_throttle, cycs) - wtt4 * wtt1 - _wtt5 * _wtt2
                if swing == 0:
                    break
                    
                momentum = self.cycle2(i, wave_throttle, cycs)
                _wtt1 = wtt1
                wtt1 = (momentum - wtt4 * wtt2) / swing
                
                acceleration = self.cycle3(i, wave_throttle, cycs)
                _wtt2 = wtt2
                wtt2 = acceleration / swing
                
                current_val = (close_prices[idx - (49 - i)] - _wtt3 * _wtt5 - wtt3 * wtt4) / swing
                _wtt3 = wtt3
                wtt3 = current_val
                wtt4 = momentum - wtt5 * _wtt1
                _wtt5 = wtt5
                wtt5 = acceleration
                
            result[idx] = current_val
            
        return result
    
    def banding(self, crsi: np.ndarray, period: int, leveling: float) -> Tuple[np.ndarray, np.ndarray]:
        """计算动态轨道"""
        percent = leveling / 100.0
        high_band = np.full(len(crsi), np.nan)
        low_band = np.full(len(crsi), np.nan)
        
        for idx in range(period, len(crsi)):
            # 获取当前周期的数据
            period_data = crsi[idx-period+1:idx+1]
            maxima = np.max(period_data)
            minima = np.min(period_data)
            
            if maxima == minima:
                high_band[idx] = maxima
                low_band[idx] = minima
                continue
                
            step_factor = (maxima - minima) / 100.0
            
            # 计算低轨
            for steps in range(101):
                test_value = minima + step_factor * steps
                below_count = np.sum(period_data < test_value)
                if below_count / period >= percent:
                    low_band[idx] = test_value
                    break
            
            # 计算高轨
            for steps in range(101):
                test_value = maxima - step_factor * steps
                above_count = np.sum(period_data >= test_value)
                if above_count / period >= percent:
                    high_band[idx] = test_value
                    break
                    
        return high_band, low_band
    
    def find_pivots(self, data: np.ndarray, left_bars: int, right_bars: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        寻找枢轴点 - 完全对齐Pine Script的ta.pivotlow和ta.pivothigh逻辑

        Pine Script逻辑:
        - ta.pivotlow(CSIBuffer, lbL, lbR) 在当前位置确认枢轴点
        - 但枢轴点实际发生在right_bars个周期前
        - 需要左侧left_bars个周期和右侧right_bars个周期确认
        """
        pivot_lows = np.zeros(len(data), dtype=bool)
        pivot_highs = np.zeros(len(data), dtype=bool)

        # 从left_bars开始，到len(data)结束（不需要减去right_bars，因为我们检查的是过去的点）
        for i in range(left_bars + right_bars, len(data)):
            pivot_idx = i - right_bars  # 实际枢轴点位置（right_bars个周期前）

            # 检查是否为低点（在pivot_idx位置）
            is_low = True
            for j in range(pivot_idx - left_bars, pivot_idx + right_bars + 1):
                if j != pivot_idx and j >= 0 and j < len(data):
                    if data[j] <= data[pivot_idx]:
                        is_low = False
                        break
            if is_low:
                pivot_lows[i] = True  # 在当前位置i标记，但实际枢轴在pivot_idx

            # 检查是否为高点（在pivot_idx位置）
            is_high = True
            for j in range(pivot_idx - left_bars, pivot_idx + right_bars + 1):
                if j != pivot_idx and j >= 0 and j < len(data):
                    if data[j] >= data[pivot_idx]:
                        is_high = False
                        break
            if is_high:
                pivot_highs[i] = True  # 在当前位置i标记，但实际枢轴在pivot_idx

        return pivot_lows, pivot_highs
    
    def _bars_since(self, condition: np.ndarray, current_idx: int) -> int:
        """
        实现Pine Script的ta.barssince()函数
        计算从当前位置向前查找，距离上次条件为真的K线数
        """
        for i in range(current_idx - 1, -1, -1):
            if condition[i]:
                return current_idx - i
        return current_idx + 1  # 如果没找到，返回一个大值

    def _in_range(self, condition: np.ndarray, current_idx: int, range_lower: int, range_upper: int) -> bool:
        """
        实现Pine Script的_inRange()函数
        检查前一个条件为真的位置是否在有效范围内
        """
        bars = self._bars_since(condition, current_idx)
        return range_lower <= bars <= range_upper

    def detect_divergences(self, csi_buffer: np.ndarray, high_prices: np.ndarray,
                          low_prices: np.ndarray, lb_l: int = 5, lb_r: int = 2,
                          range_upper: int = 60, range_lower: int = 5) -> Dict:
        """
        检测背离 - 完全对齐Pine Script逻辑

        Pine Script关键逻辑:
        1. 使用CSIBuffer[lbR]和low[lbR]，即lb_r个周期前的数据
        2. ta.valuewhen(plFound, CSIBuffer[lbR], 1) 获取前一个枢轴点的值
        3. _inRange(plFound[1]) 检查前一个枢轴点是否在有效范围内
        4. 背离信号在当前位置标记，但使用lb_r偏移的数据进行比较
        """
        pivot_lows, pivot_highs = self.find_pivots(csi_buffer, lb_l, lb_r)

        bull_div = np.zeros(len(csi_buffer), dtype=bool)
        bear_div = np.zeros(len(csi_buffer), dtype=bool)
        hidden_bull_div = np.zeros(len(csi_buffer), dtype=bool)
        hidden_bear_div = np.zeros(len(csi_buffer), dtype=bool)

        # 从足够的位置开始，确保有足够的历史数据
        for i in range(range_upper + lb_r, len(csi_buffer)):
            # Pine Script: plFound = na(ta.pivotlow(CSIBuffer, lbL, lbR)) ? false : true
            if pivot_lows[i]:
                # Pine Script: _inRange(plFound[1]) - 检查前一个枢轴点是否在范围内
                if self._in_range(pivot_lows, i, range_lower, range_upper):
                    # 寻找前一个低点 - ta.valuewhen(plFound, CSIBuffer[lbR], 1)
                    prev_pivot_idx = None
                    for j in range(i - 1, -1, -1):
                        if pivot_lows[j]:
                            prev_pivot_idx = j
                            break

                    if prev_pivot_idx is not None:
                        # Pine Script使用CSIBuffer[lbR]和low[lbR]
                        current_osc = csi_buffer[i - lb_r]
                        current_price = low_prices[i - lb_r]
                        prev_osc = csi_buffer[prev_pivot_idx - lb_r]
                        prev_price = low_prices[prev_pivot_idx - lb_r]

                        # 常规看涨背离: priceLL and oscHL and plFound
                        osc_hl = current_osc > prev_osc
                        price_ll = current_price < prev_price
                        if price_ll and osc_hl:
                            bull_div[i] = True

                        # 隐藏看涨背离: priceHL and oscLL and plFound
                        osc_ll = current_osc < prev_osc
                        price_hl = current_price > prev_price
                        if price_hl and osc_ll:
                            hidden_bull_div[i] = True

            # Pine Script: phFound = na(ta.pivothigh(CSIBuffer, lbL, lbR)) ? false : true
            if pivot_highs[i]:
                # Pine Script: _inRange(phFound[1]) - 检查前一个枢轴点是否在范围内
                if self._in_range(pivot_highs, i, range_lower, range_upper):
                    # 寻找前一个高点 - ta.valuewhen(phFound, CSIBuffer[lbR], 1)
                    prev_pivot_idx = None
                    for j in range(i - 1, -1, -1):
                        if pivot_highs[j]:
                            prev_pivot_idx = j
                            break

                    if prev_pivot_idx is not None:
                        # Pine Script使用CSIBuffer[lbR]和high[lbR]
                        current_osc = csi_buffer[i - lb_r]
                        current_price = high_prices[i - lb_r]
                        prev_osc = csi_buffer[prev_pivot_idx - lb_r]
                        prev_price = high_prices[prev_pivot_idx - lb_r]

                        # 常规看跌背离: priceHH and oscLH and phFound
                        osc_lh = current_osc < prev_osc
                        price_hh = current_price > prev_price
                        if price_hh and osc_lh:
                            bear_div[i] = True

                        # 隐藏看跌背离: priceLH and oscHH and phFound
                        osc_hh = current_osc > prev_osc
                        price_lh = current_price < prev_price
                        if price_lh and osc_hh:
                            hidden_bear_div[i] = True

        return {
            'bull_div': bull_div,
            'bear_div': bear_div,
            'hidden_bull_div': hidden_bull_div,
            'hidden_bear_div': hidden_bear_div
        }
    
    def calculate(self, close_prices: np.ndarray, high_prices: np.ndarray, 
                 low_prices: np.ndarray) -> Dict:
        """
        计算MCSI MMT指标
        
        Args:
            close_prices: 收盘价数组
            high_prices: 最高价数组
            low_prices: 最低价数组
            
        Returns:
            包含MMT相关数据的字典
        """
        try:
            if len(close_prices) < max(50, self.cyclic_memory):
                self.logger.warning("数据长度不足以计算MCSI MMT")
                return self._empty_result(len(close_prices))
            
            # 计算CSI缓冲区
            thrust1 = self.iwtt_csi_processor(close_prices, 1)
            thrust2 = self.iwtt_csi_processor(close_prices, 10)
            csi_buffer = thrust1 - thrust2
            
            # 计算动态轨道
            high_band, low_band = self.banding(csi_buffer, self.cyclic_memory, self.leveling)
            
            # 计算动量 - 对应Pine Script: momentum = CSIBuffer - CSIBuffer[1]
            momentum = self._calculate_momentum(csi_buffer)
            
            # 检测背离
            divergences = self.detect_divergences(csi_buffer, high_prices, low_prices)
            
            # 重置状态变量（对应Pine Script每次新计算）
            self.reset_state()

            # 计算评分 - 使用修复后的逻辑
            scores_result = self._calculate_scores_fixed(
                csi_buffer, high_band, low_band, momentum, divergences
            )
            
            return scores_result
            
        except Exception as e:
            self.logger.error(f"MCSI MMT计算失败: {str(e)}")
            return self._empty_result(len(close_prices))

    def _calculate_scores_fixed(self, csi_buffer, high_band, low_band, momentum, divergences):
        """
        修复后的评分计算 - 完全对齐Pine Script逻辑

        Pine Script关键逻辑:
        - 使用var变量在K线间保持状态
        - 精确的if-elif-else条件判断顺序
        - 正确的信号持续时间管理
        """
        channel_score = np.zeros(len(csi_buffer))
        divergence_score = np.zeros(len(csi_buffer))

        # 从第二个K线开始计算（因为需要比较前一个K线）
        for i in range(1, len(csi_buffer)):
            # === 轨道位置分数计算 ===
            # 对应Pine Script: if CSIBuffer[1] > highBand[1] and CSIBuffer < highBand
            if (not np.isnan(high_band[i-1]) and not np.isnan(high_band[i]) and
                csi_buffer[i-1] > high_band[i-1] and csi_buffer[i] < high_band[i]):
                # channelScore := -100, lastChannelScore := -100, channelSignalDuration := 2
                channel_score[i] = -100
                self.last_channel_score = -100
                self.channel_signal_duration = 2

            # 对应Pine Script: else if CSIBuffer[1] < lowBand[1] and CSIBuffer > lowBand
            elif (not np.isnan(low_band[i-1]) and not np.isnan(low_band[i]) and
                  csi_buffer[i-1] < low_band[i-1] and csi_buffer[i] > low_band[i]):
                # channelScore := 100, lastChannelScore := 100, channelSignalDuration := 2
                channel_score[i] = 100
                self.last_channel_score = 100
                self.channel_signal_duration = 2

            # 对应Pine Script: else if CSIBuffer > highBand
            elif not np.isnan(high_band[i]) and csi_buffer[i] > high_band[i]:
                # channelScore := momentum < 0 ? -80 : -20
                score = -80 if momentum[i] < 0 else -20
                channel_score[i] = score
                self.last_channel_score = score
                self.channel_signal_duration = 2

            # 对应Pine Script: else if CSIBuffer < lowBand
            elif not np.isnan(low_band[i]) and csi_buffer[i] < low_band[i]:
                # channelScore := momentum > 0 ? 80 : 20
                score = 80 if momentum[i] > 0 else 20
                channel_score[i] = score
                self.last_channel_score = score
                self.channel_signal_duration = 2

            # 对应Pine Script: else
            else:
                # if channelSignalDuration > 0
                if self.channel_signal_duration > 0:
                    # channelSignalDuration := channelSignalDuration - 1
                    self.channel_signal_duration -= 1
                    # channelScore := lastChannelScore
                    channel_score[i] = self.last_channel_score
                else:
                    # channelScore := 0
                    channel_score[i] = 0

            # === 背离分数计算 ===
            # 对应Pine Script: if bearDiv
            if divergences['bear_div'][i]:
                # divergenceScore := -100, lastDivergenceScore := -100, divergenceSignalDuration := 1
                divergence_score[i] = -100
                self.last_divergence_score = -100
                self.divergence_signal_duration = 1

            # 对应Pine Script: else if bullDiv
            elif divergences['bull_div'][i]:
                # divergenceScore := 100, lastDivergenceScore := 100, divergenceSignalDuration := 1
                divergence_score[i] = 100
                self.last_divergence_score = 100
                self.divergence_signal_duration = 1

            # 对应Pine Script: else if hiddenBearDiv
            elif divergences['hidden_bear_div'][i]:
                # divergenceScore := -70, lastDivergenceScore := -70, divergenceSignalDuration := 1
                divergence_score[i] = -70
                self.last_divergence_score = -70
                self.divergence_signal_duration = 1

            # 对应Pine Script: else if hiddenBullDiv
            elif divergences['hidden_bull_div'][i]:
                # divergenceScore := 70, lastDivergenceScore := 70, divergenceSignalDuration := 1
                divergence_score[i] = 70
                self.last_divergence_score = 70
                self.divergence_signal_duration = 1

            # 对应Pine Script: else
            else:
                # if divergenceSignalDuration > 0
                if self.divergence_signal_duration > 0:
                    # divergenceSignalDuration := divergenceSignalDuration - 1
                    self.divergence_signal_duration -= 1
                    # divergenceScore := lastDivergenceScore
                    divergence_score[i] = self.last_divergence_score
                else:
                    # divergenceScore := 0
                    divergence_score[i] = 0

        # 最终分数计算 - 对应Pine Script: mmtScore := channelScore * channelWeight + divergenceScore * divergenceWeight
        mmt_score = channel_score * self.channel_weight + divergence_score * self.divergence_weight

        return {
            'csi_buffer': csi_buffer,
            'high_band': high_band,
            'low_band': low_band,
            'momentum': momentum,
            'channel_score': channel_score,
            'divergence_score': divergence_score,
            'mmt_score': mmt_score,
            'bull_div': divergences['bull_div'],
            'bear_div': divergences['bear_div'],
            'hidden_bull_div': divergences['hidden_bull_div'],
            'hidden_bear_div': divergences['hidden_bear_div']
        }

    def _calculate_scores(self, csi_buffer, high_band, low_band, momentum, divergences):
        """计算评分"""
        channel_score = np.zeros(len(csi_buffer))
        divergence_score = np.zeros(len(csi_buffer))
        
        last_channel_score = 0.0
        last_divergence_score = 0.0
        channel_signal_duration = 0
        divergence_signal_duration = 0
        
        for i in range(1, len(csi_buffer)):
            # 轨道位置分数计算
            if (not np.isnan(high_band[i-1]) and not np.isnan(high_band[i]) and
                csi_buffer[i-1] > high_band[i-1] and csi_buffer[i] < high_band[i]):
                channel_score[i] = -100
                last_channel_score = -100
                channel_signal_duration = 2
            elif (not np.isnan(low_band[i-1]) and not np.isnan(low_band[i]) and
                  csi_buffer[i-1] < low_band[i-1] and csi_buffer[i] > low_band[i]):
                channel_score[i] = 100
                last_channel_score = 100
                channel_signal_duration = 2
            elif not np.isnan(high_band[i]) and csi_buffer[i] > high_band[i]:
                channel_score[i] = -80 if momentum[i] < 0 else -20
                last_channel_score = channel_score[i]
                channel_signal_duration = 2
            elif not np.isnan(low_band[i]) and csi_buffer[i] < low_band[i]:
                channel_score[i] = 80 if momentum[i] > 0 else 20
                last_channel_score = channel_score[i]
                channel_signal_duration = 2
            else:
                if channel_signal_duration > 0:
                    channel_signal_duration -= 1
                    channel_score[i] = last_channel_score
                else:
                    channel_score[i] = 0
            
            # 背离分数计算
            if divergences['bear_div'][i]:
                divergence_score[i] = -100
                last_divergence_score = -100
                divergence_signal_duration = 1
            elif divergences['bull_div'][i]:
                divergence_score[i] = 100
                last_divergence_score = 100
                divergence_signal_duration = 1
            elif divergences['hidden_bear_div'][i]:
                divergence_score[i] = -70
                last_divergence_score = -70
                divergence_signal_duration = 1
            elif divergences['hidden_bull_div'][i]:
                divergence_score[i] = 70
                last_divergence_score = 70
                divergence_signal_duration = 1
            else:
                if divergence_signal_duration > 0:
                    divergence_signal_duration -= 1
                    divergence_score[i] = last_divergence_score
                else:
                    divergence_score[i] = 0
        
        # 最终分数计算
        mmt_score = channel_score * self.channel_weight + divergence_score * self.divergence_weight

        return {
            'csi_buffer': csi_buffer,
            'high_band': high_band,
            'low_band': low_band,
            'momentum': momentum,
            'channel_score': channel_score,
            'divergence_score': divergence_score,
            'mmt_score': mmt_score,
            'bull_div': divergences['bull_div'],
            'bear_div': divergences['bear_div'],
            'hidden_bull_div': divergences['hidden_bull_div'],
            'hidden_bear_div': divergences['hidden_bear_div']
        }
    
    def _empty_result(self, length: int) -> Dict:
        """返回空结果"""
        return {
            'csi_buffer': np.full(length, np.nan),
            'high_band': np.full(length, np.nan),
            'low_band': np.full(length, np.nan),
            'momentum': np.full(length, np.nan),
            'channel_score': np.full(length, 0.0),
            'divergence_score': np.full(length, 0.0),
            'mmt_score': np.full(length, 0.0),
            'bull_div': np.full(length, False),
            'bear_div': np.full(length, False),
            'hidden_bull_div': np.full(length, False),
            'hidden_bear_div': np.full(length, False)
        }
