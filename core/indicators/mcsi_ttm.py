#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCSI TTM指标
从Pine Script转换为Python实现
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import logging

class MCSITTMIndicator:
    """MCSI TTM技术指标"""
    
    def __init__(self, comparison_period=4):
        """
        初始化MCSI TTM指标
        
        Args:
            comparison_period: 比较周期，默认4
        """
        self.comparison_period = comparison_period
        self.logger = logging.getLogger(__name__)
        
    def get_ttm_score(self, count: int) -> float:
        """
        获取TTM评分
        
        Args:
            count: 计数值
            
        Returns:
            对应的评分
        """
        if count == 0 or count <= 6:
            return 0.0
        elif count == 7:
            return 20.0
        elif count == 8:
            return 50.0
        elif count == 9 or (13 <= count <= 16):
            return 100.0
        elif 10 <= count <= 12:
            return 80.0
        else:
            return 0.0
    
    def calculate_td_sequences(self, close_prices: np.ndarray) -> <PERSON><PERSON>[np.ndarray, np.ndarray]:
        """
        计算TD序列 (严格按照Pine Script逻辑)

        Args:
            close_prices: 收盘价数组

        Returns:
            (TD序列, TS序列)
        """
        length = len(close_prices)
        td = np.zeros(length, dtype=int)  # TD变量
        ts = np.zeros(length, dtype=int)  # TS变量

        # 按照Pine Script逻辑: TD := close > close[4] ? nz(TD[1])+1 : 0
        for i in range(self.comparison_period, length):
            # TD := close > close[4] ? nz(TD[1])+1 : 0
            if close_prices[i] > close_prices[i - self.comparison_period]:
                td[i] = td[i-1] + 1
            else:
                td[i] = 0

            # TS := close < close[4] ? nz(TS[1])+1 : 0
            if close_prices[i] < close_prices[i - self.comparison_period]:
                ts[i] = ts[i-1] + 1
            else:
                ts[i] = 0

        return td, ts
    
    def calculate_td_counts(self, td: np.ndarray, ts: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算TD计数 (严格按照Pine Script逻辑)

        Args:
            td: TD序列
            ts: TS序列

        Returns:
            (TDUp计数, TDDn计数)
        """
        length = len(td)
        td_up = np.zeros(length, dtype=int)
        td_dn = np.zeros(length, dtype=int)

        # 按照Pine Script逻辑计算
        # TDUp = TD - ta.valuewhen(TD < TD[1], TD, 1)
        # TDDn = TS - ta.valuewhen(TS < TS[1], TS, 1)

        for i in range(1, length):
            # 计算TDUp
            if td[i] > 0:
                # 找到最近一次TD下降时的值 (ta.valuewhen(TD < TD[1], TD, 1))
                last_td_value = 0
                for j in range(i-1, -1, -1):
                    if j > 0 and td[j] < td[j-1]:
                        last_td_value = td[j]
                        break
                td_up[i] = max(0, td[i] - last_td_value)
            else:
                td_up[i] = 0

            # 计算TDDn
            if ts[i] > 0:
                # 找到最近一次TS下降时的值 (ta.valuewhen(TS < TS[1], TS, 1))
                last_ts_value = 0
                for j in range(i-1, -1, -1):
                    if j > 0 and ts[j] < ts[j-1]:
                        last_ts_value = ts[j]
                        break
                td_dn[i] = max(0, ts[i] - last_ts_value)
            else:
                td_dn[i] = 0

        return td_up, td_dn
    
    def calculate_background_colors(self, ttm_score: np.ndarray) -> List[str]:
        """
        计算背景颜色
        
        Args:
            ttm_score: TTM评分数组
            
        Returns:
            颜色列表
        """
        colors = []
        for score in ttm_score:
            if score != 0:
                if score > 0:
                    # 绿色，透明度基于分数
                    transparency = 100 - abs(score)
                    colors.append(f'rgba(0, 255, 0, {(100-transparency)/100:.2f})')
                else:
                    # 红色，透明度基于分数
                    transparency = 100 - abs(score)
                    colors.append(f'rgba(255, 0, 0, {(100-transparency)/100:.2f})')
            else:
                colors.append('transparent')
        return colors
    
    def calculate(self, close_prices: np.ndarray) -> Dict:
        """
        计算MCSI TTM指标
        
        Args:
            close_prices: 收盘价数组
            
        Returns:
            包含TTM相关数据的字典
        """
        try:
            if len(close_prices) < self.comparison_period + 1:
                self.logger.warning("数据长度不足以计算MCSI TTM")
                return self._empty_result(len(close_prices))
            
            # 计算TD序列
            td, ts = self.calculate_td_sequences(close_prices)

            # 计算TD计数
            td_up_count, td_down_count = self.calculate_td_counts(td, ts)
            
            # 计算TTM评分
            ttm_score = np.zeros(len(close_prices))
            
            for i in range(len(close_prices)):
                if td_down_count[i] > 0:
                    # 下降计数为正分（买入信号）
                    ttm_score[i] = self.get_ttm_score(td_down_count[i])
                elif td_up_count[i] > 0:
                    # 上升计数为负分（卖出信号）
                    ttm_score[i] = -self.get_ttm_score(td_up_count[i])
                else:
                    ttm_score[i] = 0.0
            
            # 计算背景颜色
            background_colors = self.calculate_background_colors(ttm_score)
            
            # 计算形状标记
            up_shapes = td_up_count > 0
            down_shapes = td_down_count > 0
            
            return {
                'td_up': td,
                'td_down': ts,
                'td_up_count': td_up_count,
                'td_down_count': td_down_count,
                'ttm_score': ttm_score,
                'background_colors': background_colors,
                'up_shapes': up_shapes,
                'down_shapes': down_shapes
            }
            
        except Exception as e:
            self.logger.error(f"MCSI TTM计算失败: {str(e)}")
            return self._empty_result(len(close_prices))
    
    def _empty_result(self, length: int) -> Dict:
        """返回空结果"""
        return {
            'td_up': np.full(length, 0, dtype=int),
            'td_down': np.full(length, 0, dtype=int),
            'td_up_count': np.full(length, 0, dtype=int),
            'td_down_count': np.full(length, 0, dtype=int),
            'ttm_score': np.full(length, 0.0),
            'background_colors': ['transparent'] * length,
            'up_shapes': np.full(length, False),
            'down_shapes': np.full(length, False)
        }
