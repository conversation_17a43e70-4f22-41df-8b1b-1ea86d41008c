#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web应用中的RSI滑动窗口计算
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_web_rsi_calculation():
    print("🌐 测试Web应用中的RSI滑动窗口计算")
    print("=" * 60)
    
    try:
        # 导入RSI单元
        from core.scoring_units.mcsi_rsi_scoring import MCSIRSIScoringUnit
        from core.scoring_units.mcsi_ttm_scoring import MCSITTMScoringUnit

        print("✅ 指标单元导入成功")
        
        # 创建RSI和TTM单元
        rsi_unit = MCSIRSIScoringUnit()
        ttm_unit = MCSITTMScoringUnit()
        
        print(f"✅ 指标单元创建: RSI={rsi_unit.source_available}, TTM={ttm_unit.source_available}")
        
        # 创建测试数据
        np.random.seed(42)
        data_length = 200  # 足够长的数据
        dates = pd.date_range('2024-01-01', periods=data_length, freq='D')
        close_prices = 100 + np.cumsum(np.random.normal(0, 1, data_length))
        
        test_data = pd.DataFrame({
            'timestamp': dates,
            'date': dates,
            'open': close_prices * (1 + np.random.uniform(-0.01, 0.01, data_length)),
            'high': close_prices * (1 + np.random.uniform(0.005, 0.02, data_length)),
            'low': close_prices * (1 + np.random.uniform(-0.02, -0.005, data_length)),
            'close': close_prices,
            'volume': np.random.randint(1000, 10000, data_length)
        })
        
        print(f"✅ 测试数据创建: {len(test_data)}行")
        
        # 模拟滑动窗口计算
        print("\n🔄 开始模拟滑动窗口RSI计算...")

        # 模拟滑动窗口逻辑
        window_size = 100
        data_length = len(test_data)
        rsi_scores = []

        for i in range(data_length):
            try:
                # 获取窗口数据
                window_data = test_data.iloc[max(0, i-window_size+1):i+1]

                if len(window_data) >= 20:  # 最小数据要求
                    # 调用RSI单元计算
                    rsi_result = rsi_unit.calculate_score(window_data)
                    rsi_scores.append(rsi_result.score)
                else:
                    rsi_scores.append(0.0)

            except Exception as e:
                rsi_scores.append(0.0)

        result = {'rsi_scores': rsi_scores}
        print("✅ 模拟滑动窗口计算完成")
        
        # 检查结果
        if result:
            print(f"   返回键: {list(result.keys())}")
            
            if 'rsi_scores' in result:
                rsi_scores = result['rsi_scores']
                print(f"   RSI评分数量: {len(rsi_scores)}")
                
                # 检查有效评分
                valid_rsi = [s for s in rsi_scores if s is not None and not np.isnan(s)]
                if len(valid_rsi) > 0:
                    print(f"   有效RSI评分: {len(valid_rsi)}")
                    print(f"   RSI评分范围: {min(valid_rsi):.2f} ~ {max(valid_rsi):.2f}")
                    print(f"   非零RSI评分: {sum(1 for s in valid_rsi if s != 0)}")
                    
                    if any(s != 0 for s in valid_rsi):
                        print("🎉 Web应用RSI滑动窗口计算成功！")
                        return True
                    else:
                        print("⚠️  所有RSI评分都为0")
                        return False
                else:
                    print("❌ 没有有效的RSI评分")
                    return False
            else:
                print("❌ 未返回rsi_scores")
                return False
        else:
            print("❌ 滑动窗口计算返回空结果")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rsi_vs_other_indicators():
    """对比RSI与其他指标在Web应用中的表现"""
    print("\n🔍 对比RSI与其他指标在Web应用中的表现")
    print("=" * 60)
    
    try:
        # 导入各个指标单元
        from core.scoring_units.mcsi_rsi_scoring import MCSIRSIScoringUnit
        from core.scoring_units.mcsi_macd_scoring import MCSIMACDScoringUnit
        from core.scoring_units.mcsi_mmt_scoring import MCSIMMTScoringUnit
        
        # 创建测试数据
        np.random.seed(42)
        dates = pd.date_range('2024-01-01', periods=100, freq='D')
        close_prices = 100 + np.cumsum(np.random.normal(0, 1, 100))
        
        test_data = pd.DataFrame({
            'timestamp': dates,
            'date': dates,
            'open': close_prices * 0.99,
            'high': close_prices * 1.01,
            'low': close_prices * 0.98,
            'close': close_prices,
            'volume': [1000] * 100
        })
        
        indicators = [
            ('MCSI RSI', MCSIRSIScoringUnit),
            ('MCSI MACD', MCSIMACDScoringUnit),
            ('MCSI MMT', MCSIMMTScoringUnit),
        ]
        
        for name, indicator_class in indicators:
            try:
                indicator = indicator_class()
                result = indicator.calculate_score(data=test_data)
                
                print(f"{name:12}: 评分={result.score:8.2f}, 信号={result.signal:15}, 可用={indicator.source_available}")
                
            except Exception as e:
                print(f"{name:12}: 测试失败 - {e}")
                
    except Exception as e:
        print(f"❌ 对比测试失败: {e}")

if __name__ == "__main__":
    print("🚀 开始Web应用RSI测试")
    
    # 主要测试
    success = test_web_rsi_calculation()
    
    # 对比测试
    test_rsi_vs_other_indicators()
    
    # 最终结果
    print("\n" + "=" * 60)
    if success:
        print("🎉 Web应用RSI测试成功！RSI指标已完全修复！")
    else:
        print("❌ Web应用RSI测试失败，需要进一步调试")
    print("=" * 60)
