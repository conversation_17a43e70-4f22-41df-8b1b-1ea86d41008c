#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试RSI指标的真实周线数据获取
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_rsi_weekly_data():
    """测试RSI指标的周线数据获取"""
    print("🔍 测试RSI指标的真实周线数据获取")
    print("=" * 60)
    
    try:
        from core.scoring_units.mcsi_rsi_scoring import MCSIRSIScoringUnit
        
        # 创建RSI单元
        rsi_unit = MCSIRSIScoringUnit()
        print(f"✅ RSI单元创建成功: source_available={rsi_unit.source_available}")
        
        # 创建测试数据
        np.random.seed(42)
        dates = pd.date_range('2024-01-01', periods=100, freq='D')
        close_prices = 100 + np.cumsum(np.random.normal(0, 1, 100))
        
        test_data = pd.DataFrame({
            'date': dates,
            'timestamp': dates,
            'open': close_prices * 0.99,
            'high': close_prices * 1.01,
            'low': close_prices * 0.98,
            'close': close_prices,
            'volume': [1000] * 100
        })
        
        print(f"✅ 测试数据创建成功: {len(test_data)}行")
        
        # 测试1: 没有数据库连接的情况
        print("\n=== 测试1: 没有数据库连接 ===")
        result1 = rsi_unit.calculate_score(data=test_data)
        print(f"评分: {result1.score:.2f}")
        print(f"信号: {result1.signal}")
        print(f"描述: {result1.description}")
        
        # 测试2: 有数据库连接但无真实数据的情况
        print("\n=== 测试2: 模拟数据库连接 ===")
        result2 = rsi_unit.calculate_score(
            data=test_data,
            db_conn="mock_connection",
            symbol="test_symbol",
            start_date="2024-01-01",
            end_date="2024-12-31"
        )
        print(f"评分: {result2.score:.2f}")
        print(f"信号: {result2.signal}")
        print(f"描述: {result2.description}")
        
        # 测试3: 直接测试周线数据获取方法
        print("\n=== 测试3: 直接测试周线数据获取 ===")
        try:
            weekly_data = rsi_unit._get_data_using_provider(
                db_conn="mock_connection",
                symbol="test_symbol",
                start_date="2024-01-01",
                end_date="2024-12-31",
                period='weekly'
            )
            
            if weekly_data is not None and len(weekly_data) > 0:
                print(f"✅ 周线数据获取成功: {len(weekly_data)}行")
                print(f"   列: {list(weekly_data.columns)}")
            else:
                print("⚠️  周线数据为空或None")
                
        except Exception as e:
            print(f"❌ 周线数据获取失败: {e}")
        
        # 测试4: 验证RSI源代码的周线数据处理
        print("\n=== 测试4: RSI源代码周线数据处理 ===")
        try:
            # 准备包含周线数据的数据字典
            data_with_weekly = {
                'date': test_data['date'].dt.strftime('%Y-%m-%d').tolist(),
                'open': test_data['open'].values,
                'high': test_data['high'].values,
                'low': test_data['low'].values,
                'close': test_data['close'].values,
                'volume': test_data['volume'].values,
                'weekly_data': {
                    'date': ['2024-01-01', '2024-01-08', '2024-01-15'],
                    'open': [100.0, 101.0, 102.0],
                    'high': [102.0, 103.0, 104.0],
                    'low': [99.0, 100.0, 101.0],
                    'close': [101.0, 102.0, 103.0]
                }
            }
            
            # 直接调用RSI源代码
            if rsi_unit.source_available:
                result = rsi_unit.source_indicator.calculate(data_with_weekly)
                
                if result and 'rsi_score' in result:
                    scores = result['rsi_score']
                    print(f"✅ RSI源代码计算成功")
                    print(f"   评分数量: {len(scores)}")
                    print(f"   非零评分: {sum(1 for s in scores if s != 0)}")
                    
                    # 检查是否使用了周线数据
                    if 'crsi_weekly' in result:
                        weekly_crsi = result['crsi_weekly']
                        valid_weekly = weekly_crsi[~np.isnan(weekly_crsi)]
                        print(f"   周线CRSI有效数据: {len(valid_weekly)}")
                else:
                    print("❌ RSI源代码未返回有效结果")
            else:
                print("❌ RSI源代码不可用")
                
        except Exception as e:
            print(f"❌ RSI源代码测试失败: {e}")
        
        # 总结
        print("\n" + "=" * 60)
        print("📋 测试总结")
        print("=" * 60)
        
        if result1.score != 0 and result2.score != 0:
            print("✅ RSI指标基本功能正常")
            print("✅ RSI指标能在有无数据库连接的情况下都正常工作")
            
            if result1.score == result2.score:
                print("⚠️  两种情况下评分相同，可能未使用真实周线数据")
            else:
                print("✅ 两种情况下评分不同，可能正在尝试使用真实周线数据")
                
            return True
        else:
            print("❌ RSI指标仍有问题")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_rsi_weekly_data()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 RSI指标周线数据测试完成！")
        print("📝 注意: 如果要使用真实周线数据，需要确保:")
        print("   1. 数据库连接配置正确")
        print("   2. 数据库中存在对应的周线数据表")
        print("   3. 表结构包含period='weekly'的数据")
    else:
        print("❌ RSI指标周线数据测试失败")
    print("=" * 60)
