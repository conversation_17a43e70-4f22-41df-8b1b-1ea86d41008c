#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化RSI测试
"""

import sys
import os
import numpy as np
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / 'TV-code' / 'py-code'))

def test_minimal_rsi():
    print("🔧 最小化RSI测试")
    
    try:
        # 直接导入RSI指标
        from mcsi_rsi import MCSIRSIIndicator
        print("✅ RSI指标导入成功")
        
        # 创建RSI实例
        rsi = MCSIRSIIndicator(dom_cycle=14, vibration=10, leveling=10.0)
        print(f"✅ RSI实例创建成功: dom_cycle={rsi.dom_cycle}, cyclic_memory={rsi.cyclic_memory}")
        
        # 创建足够长的测试数据
        np.random.seed(42)
        data_length = max(rsi.dom_cycle, rsi.cyclic_memory) + 50  # 确保数据足够长
        close_prices = 100 + np.cumsum(np.random.normal(0, 1, data_length))
        
        print(f"✅ 测试数据创建成功: {data_length}行")
        print(f"   价格范围: {close_prices.min():.2f} ~ {close_prices.max():.2f}")
        
        # 直接调用calculate方法
        print("🔄 开始RSI计算...")
        result = rsi.calculate(close_prices)  # 传入数组格式
        print("✅ RSI计算完成")
        
        if result:
            print(f"   返回键: {list(result.keys())}")
            
            # 检查关键数据
            if 'rsi_score' in result:
                scores = result['rsi_score']
                print(f"   RSI评分数量: {len(scores)}")
                
                # 检查是否有有效数据
                valid_scores = scores[~np.isnan(scores)]
                if len(valid_scores) > 0:
                    print(f"   有效评分数量: {len(valid_scores)}")
                    print(f"   评分范围: {valid_scores.min():.2f} ~ {valid_scores.max():.2f}")
                    print(f"   非零评分: {sum(1 for s in valid_scores if s != 0)}")
                    
                    if any(s != 0 for s in valid_scores):
                        print("🎉 RSI源代码工作正常！")
                        return True
                    else:
                        print("⚠️  所有有效评分都为0")
                        
                        # 检查CRSI数据
                        if 'crsi_daily' in result:
                            crsi = result['crsi_daily']
                            valid_crsi = crsi[~np.isnan(crsi)]
                            print(f"   有效CRSI数量: {len(valid_crsi)}")
                            if len(valid_crsi) > 0:
                                print(f"   CRSI范围: {valid_crsi.min():.2f} ~ {valid_crsi.max():.2f}")
                        
                        return False
                else:
                    print("❌ 所有RSI评分都是NaN")
                    return False
            else:
                print("❌ 未返回rsi_score")
                return False
        else:
            print("❌ RSI计算返回空结果")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_minimal_rsi()
    if success:
        print("\n🎉 RSI源代码测试成功！")
    else:
        print("\n❌ RSI源代码测试失败")
