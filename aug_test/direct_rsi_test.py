#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试RSI源代码
"""

import sys
import os
import numpy as np
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'TV-code' / 'py-code'))

def test_direct_rsi():
    print("🔧 直接测试RSI源代码")
    
    try:
        # 直接导入RSI指标
        from mcsi_rsi import MCSIRSIIndicator
        print("✅ RSI指标导入成功")
        
        # 创建RSI实例
        rsi = MCSIRSIIndicator()
        print("✅ RSI实例创建成功")
        
        # 创建测试数据
        np.random.seed(42)
        close_prices = 100 + np.cumsum(np.random.normal(0, 1, 100))
        
        data_dict = {
            'date': [f'2024-01-{i+1:02d}' for i in range(100)],
            'open': (close_prices * 0.99).tolist(),
            'high': (close_prices * 1.01).tolist(),
            'low': (close_prices * 0.98).tolist(),
            'close': close_prices.tolist(),
            'volume': [1000] * 100
        }
        
        print("✅ 测试数据创建成功")
        
        # 调用RSI计算
        result = rsi.calculate(data_dict)
        print("✅ RSI计算完成")
        
        if result:
            print(f"   返回键: {list(result.keys())}")
            if 'rsi_score' in result:
                scores = result['rsi_score']
                print(f"   RSI评分数量: {len(scores)}")
                print(f"   RSI评分范围: {min(scores):.2f} ~ {max(scores):.2f}")
                print(f"   非零评分: {sum(1 for s in scores if s != 0)}")
                
                if any(s != 0 for s in scores):
                    print("🎉 RSI源代码工作正常！")
                    return True
                else:
                    print("⚠️  RSI评分全为0")
                    return False
            else:
                print("❌ 未返回rsi_score")
                return False
        else:
            print("❌ RSI计算返回空结果")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_direct_rsi()
