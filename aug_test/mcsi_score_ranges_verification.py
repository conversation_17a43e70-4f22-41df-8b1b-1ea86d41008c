#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCSI技术指标评分范围验证脚本
确认四个指标在三个版本中的最终分数范围
"""

import sys
import os
import numpy as np
import pandas as pd
from pathlib import Path
import logging

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'TV-code' / 'py-code'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_pine_script_score_ranges():
    """分析Pine Script源代码中的评分范围"""
    logger.info("=== 分析Pine Script源代码评分范围 ===")
    
    score_ranges = {}
    
    # MACD评分范围分析
    logger.info("1. MCSI MACD (MCSI-macd-test.pine):")
    logger.info("   - 基础卖出信号: -(50 + extraScore)")
    logger.info("   - 基础买入信号: 50 + extraScore")
    logger.info("   - extraScore = min(50, (relativeHeight - 1) * 25)")
    logger.info("   - 理论范围: [-100, +100]")
    score_ranges['MACD_Pine'] = (-100, 100)
    
    # MMT评分范围分析
    logger.info("2. MCSI MMT (MCSI-mmt-test.pine):")
    logger.info("   - 轨道分数: [-100, +100]")
    logger.info("   - 背离分数: [-100, +100] (常规), [-70, +70] (隐藏)")
    logger.info("   - 最终分数: channelScore * 0.5 + divergenceScore * 0.5")
    logger.info("   - 理论范围: [-100, +100]")
    score_ranges['MMT_Pine'] = (-100, 100)
    
    # RSI评分范围分析
    logger.info("3. MCSI RSI (MCSI-rsi-test.pine):")
    logger.info("   - 日线分数: [-67, +67]")
    logger.info("   - 周线分数: [-67, +67] (突破), [-33, +33] (持续)")
    logger.info("   - 最终分数: max(-100, min(100, dailyScore + weeklyScore))")
    logger.info("   - 理论范围: [-100, +100]")
    score_ranges['RSI_Pine'] = (-100, 100)
    
    # TTM评分范围分析
    logger.info("4. MCSI TTM (MCSI-td9-test.pine):")
    logger.info("   - 评分规则: count==7→20, count==8→50, count==9→100, count==10-12→80, count==13-16→100")
    logger.info("   - 下降计数为正分(买入), 上升计数为负分(卖出)")
    logger.info("   - 理论范围: [-100, +100]")
    score_ranges['TTM_Pine'] = (-100, 100)
    
    return score_ranges

def analyze_python_authority_score_ranges():
    """分析Python权威实现的评分范围"""
    logger.info("\n=== 分析Python权威实现评分范围 ===")
    
    score_ranges = {}
    
    try:
        # 生成多种测试数据
        test_scenarios = [
            ("趋势上涨", generate_trending_data(200, trend=0.001)),
            ("趋势下跌", generate_trending_data(200, trend=-0.001)),
            ("震荡行情", generate_oscillating_data(200)),
            ("极端波动", generate_extreme_data(200))
        ]
        
        for scenario_name, test_data in test_scenarios:
            logger.info(f"\n--- {scenario_name}场景测试 ---")
            
            # 测试MACD
            try:
                from mcsi_macd import MCSIMACDIndicator
                macd_indicator = MCSIMACDIndicator()
                macd_result = macd_indicator.calculate(test_data['close'].values)
                macd_scores = macd_result['macd_score']
                macd_range = (np.nanmin(macd_scores), np.nanmax(macd_scores))
                logger.info(f"   MACD范围: [{macd_range[0]:.2f}, {macd_range[1]:.2f}]")
                
                if 'MACD_Python' not in score_ranges:
                    score_ranges['MACD_Python'] = macd_range
                else:
                    current = score_ranges['MACD_Python']
                    score_ranges['MACD_Python'] = (min(current[0], macd_range[0]), max(current[1], macd_range[1]))
                    
            except Exception as e:
                logger.error(f"   MACD测试失败: {e}")
            
            # 测试MMT
            try:
                from mcsi_mmt import MCSIMMTIndicator
                mmt_indicator = MCSIMMTIndicator()
                mmt_result = mmt_indicator.calculate(
                    test_data['close'].values,
                    test_data['high'].values,
                    test_data['low'].values
                )
                mmt_scores = mmt_result['mmt_score']
                mmt_range = (np.nanmin(mmt_scores), np.nanmax(mmt_scores))
                logger.info(f"   MMT范围: [{mmt_range[0]:.2f}, {mmt_range[1]:.2f}]")
                
                if 'MMT_Python' not in score_ranges:
                    score_ranges['MMT_Python'] = mmt_range
                else:
                    current = score_ranges['MMT_Python']
                    score_ranges['MMT_Python'] = (min(current[0], mmt_range[0]), max(current[1], mmt_range[1]))
                    
            except Exception as e:
                logger.error(f"   MMT测试失败: {e}")
            
            # 测试RSI
            try:
                from mcsi_rsi import MCSIRSIIndicator
                rsi_indicator = MCSIRSIIndicator()
                rsi_result = rsi_indicator.calculate(test_data['close'].values)
                rsi_scores = rsi_result['rsi_score']
                rsi_range = (np.nanmin(rsi_scores), np.nanmax(rsi_scores))
                logger.info(f"   RSI范围: [{rsi_range[0]:.2f}, {rsi_range[1]:.2f}]")
                
                if 'RSI_Python' not in score_ranges:
                    score_ranges['RSI_Python'] = rsi_range
                else:
                    current = score_ranges['RSI_Python']
                    score_ranges['RSI_Python'] = (min(current[0], rsi_range[0]), max(current[1], rsi_range[1]))
                    
            except Exception as e:
                logger.error(f"   RSI测试失败: {e}")
            
            # 测试TTM
            try:
                from mcsi_ttm import MCSITTMIndicator
                ttm_indicator = MCSITTMIndicator()
                ttm_result = ttm_indicator.calculate(test_data['close'].values)
                ttm_scores = ttm_result['ttm_score']
                ttm_range = (np.nanmin(ttm_scores), np.nanmax(ttm_scores))
                logger.info(f"   TTM范围: [{ttm_range[0]:.2f}, {ttm_range[1]:.2f}]")
                
                if 'TTM_Python' not in score_ranges:
                    score_ranges['TTM_Python'] = ttm_range
                else:
                    current = score_ranges['TTM_Python']
                    score_ranges['TTM_Python'] = (min(current[0], ttm_range[0]), max(current[1], ttm_range[1]))
                    
            except Exception as e:
                logger.error(f"   TTM测试失败: {e}")
    
    except Exception as e:
        logger.error(f"Python权威实现测试失败: {e}")
    
    return score_ranges

def analyze_unified_interface_score_ranges():
    """分析统一接口的评分范围"""
    logger.info("\n=== 分析统一接口评分范围 ===")
    
    score_ranges = {}
    
    try:
        # 导入统一接口
        sys.path.insert(0, str(project_root / 'core'))
        
        from scoring_units.mcsi_macd_scoring import MCSIMACDScoringUnit
        from scoring_units.mcsi_mmt_scoring import MCSIMMTScoringUnit
        from scoring_units.mcsi_rsi_scoring import MCSIRSIScoringUnit
        from scoring_units.mcsi_ttm_scoring import MCSITTMScoringUnit
        
        # 生成多种测试数据
        test_scenarios = [
            ("趋势上涨", generate_trending_data(200, trend=0.001)),
            ("趋势下跌", generate_trending_data(200, trend=-0.001)),
            ("震荡行情", generate_oscillating_data(200)),
            ("极端波动", generate_extreme_data(200))
        ]
        
        scoring_units = [
            ('MACD', MCSIMACDScoringUnit()),
            ('MMT', MCSIMMTScoringUnit()),
            ('RSI', MCSIRSIScoringUnit()),
            ('TTM', MCSITTMScoringUnit())
        ]
        
        for scenario_name, test_data in test_scenarios:
            logger.info(f"\n--- {scenario_name}场景测试 ---")
            
            for name, unit in scoring_units:
                try:
                    result = unit.calculate_score(data=test_data)
                    score = result.score
                    logger.info(f"   {name}评分: {score:.2f}")
                    
                    key = f"{name}_Unified"
                    if key not in score_ranges:
                        score_ranges[key] = (score, score)
                    else:
                        current = score_ranges[key]
                        score_ranges[key] = (min(current[0], score), max(current[1], score))
                        
                except Exception as e:
                    logger.error(f"   {name}测试失败: {e}")
    
    except Exception as e:
        logger.error(f"统一接口测试失败: {e}")
    
    return score_ranges

def generate_trending_data(length=200, trend=0.001, seed=42):
    """生成趋势性数据"""
    np.random.seed(seed)
    base_price = 100.0
    
    # 生成带趋势的价格序列
    returns = np.random.normal(trend, 0.02, length)
    close_prices = base_price * np.exp(np.cumsum(returns))
    
    return pd.DataFrame({
        'open': close_prices * (1 + np.random.uniform(-0.01, 0.01, length)),
        'high': close_prices * (1 + np.random.uniform(0.005, 0.02, length)),
        'low': close_prices * (1 + np.random.uniform(-0.02, -0.005, length)),
        'close': close_prices,
        'volume': np.random.randint(1000, 10000, length),
        'date': pd.date_range(start='2024-01-01', periods=length, freq='D')
    })

def generate_oscillating_data(length=200, seed=43):
    """生成震荡性数据"""
    np.random.seed(seed)
    base_price = 100.0
    
    # 生成震荡价格序列
    t = np.arange(length)
    trend = np.sin(t * 2 * np.pi / 50) * 0.1  # 50天周期的震荡
    noise = np.random.normal(0, 0.02, length)
    returns = trend + noise
    close_prices = base_price * np.exp(np.cumsum(returns))
    
    return pd.DataFrame({
        'open': close_prices * (1 + np.random.uniform(-0.01, 0.01, length)),
        'high': close_prices * (1 + np.random.uniform(0.005, 0.02, length)),
        'low': close_prices * (1 + np.random.uniform(-0.02, -0.005, length)),
        'close': close_prices,
        'volume': np.random.randint(1000, 10000, length),
        'date': pd.date_range(start='2024-01-01', periods=length, freq='D')
    })

def generate_extreme_data(length=200, seed=44):
    """生成极端波动数据"""
    np.random.seed(seed)
    base_price = 100.0
    
    # 生成极端波动价格序列
    returns = np.random.normal(0, 0.05, length)  # 更大的波动率
    # 添加一些极端事件
    extreme_events = np.random.choice(length, size=5, replace=False)
    returns[extreme_events] = np.random.choice([-0.2, 0.2], size=5)
    
    close_prices = base_price * np.exp(np.cumsum(returns))
    
    return pd.DataFrame({
        'open': close_prices * (1 + np.random.uniform(-0.01, 0.01, length)),
        'high': close_prices * (1 + np.random.uniform(0.005, 0.02, length)),
        'low': close_prices * (1 + np.random.uniform(-0.02, -0.005, length)),
        'close': close_prices,
        'volume': np.random.randint(1000, 10000, length),
        'date': pd.date_range(start='2024-01-01', periods=length, freq='D')
    })

def main():
    """主函数"""
    logger.info("开始MCSI技术指标评分范围验证")
    
    # 分析三个版本的评分范围
    pine_ranges = analyze_pine_script_score_ranges()
    python_ranges = analyze_python_authority_score_ranges()
    unified_ranges = analyze_unified_interface_score_ranges()
    
    # 汇总结果
    logger.info("\n" + "="*80)
    logger.info("MCSI技术指标评分范围汇总")
    logger.info("="*80)
    
    indicators = ['MACD', 'MMT', 'RSI', 'TTM']
    
    for indicator in indicators:
        logger.info(f"\n{indicator}指标评分范围:")
        
        # Pine Script理论范围
        pine_key = f"{indicator}_Pine"
        if pine_key in pine_ranges:
            pine_range = pine_ranges[pine_key]
            logger.info(f"  Pine Script (理论): [{pine_range[0]}, {pine_range[1]}]")
        
        # Python权威实现实际范围
        python_key = f"{indicator}_Python"
        if python_key in python_ranges:
            python_range = python_ranges[python_key]
            logger.info(f"  Python权威实现 (实际): [{python_range[0]:.2f}, {python_range[1]:.2f}]")
        
        # 统一接口实际范围
        unified_key = f"{indicator}_Unified"
        if unified_key in unified_ranges:
            unified_range = unified_ranges[unified_key]
            logger.info(f"  统一接口 (实际): [{unified_range[0]:.2f}, {unified_range[1]:.2f}]")
    
    logger.info("\n" + "="*80)
    logger.info("验证完成")

if __name__ == "__main__":
    main()
