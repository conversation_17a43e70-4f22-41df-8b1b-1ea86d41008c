#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试RSI指标修复效果
验证RSI指标是否能正常计算评分并返回非零值
"""

import sys
import os
import pandas as pd
import numpy as np
import logging

# 设置日志级别
logging.basicConfig(level=logging.WARNING)  # 减少日志输出

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_rsi_unified_interface():
    """测试RSI指标的统一接口"""
    print("🔧 测试RSI指标统一接口修复效果")
    print("=" * 60)
    
    try:
        # 1. 导入RSI评分单元
        from core.scoring_units.mcsi_rsi_scoring import MCSIRSIScoringUnit
        
        rsi_unit = MCSIRSIScoringUnit()
        print(f"✅ RSI单元初始化成功: {rsi_unit.source_available}")
        
        # 2. 创建测试数据
        dates = pd.date_range('2024-01-01', periods=100, freq='D')
        np.random.seed(42)
        base_price = 100.0
        price_changes = np.random.normal(0, 0.02, 100)
        close_prices = base_price + np.cumsum(price_changes)
        
        test_data = pd.DataFrame({
            'date': dates,
            'timestamp': dates,
            'open': close_prices * (1 + np.random.uniform(-0.01, 0.01, 100)),
            'high': close_prices * (1 + np.random.uniform(0.005, 0.02, 100)),
            'low': close_prices * (1 + np.random.uniform(-0.02, -0.005, 100)),
            'close': close_prices,
            'volume': np.random.randint(1000, 10000, 100)
        })
        
        print(f"✅ 测试数据创建成功: {len(test_data)}行")
        
        # 3. 测试数据验证
        validation_result = rsi_unit.validate_data(test_data)
        print(f"✅ 数据验证结果: {validation_result}")
        
        # 4. 测试RSI计算（不使用数据库）
        print("\n=== 测试RSI计算（仅日线数据）===")
        result = rsi_unit.calculate_score(data=test_data)
        
        print(f"✅ RSI计算完成")
        print(f"   - 评分: {result.score}")
        print(f"   - 信号: {result.signal}")
        print(f"   - 置信度: {result.confidence}")
        print(f"   - 描述: {result.description}")
        
        if hasattr(result, 'metadata') and result.metadata:
            print(f"   - 元数据键: {list(result.metadata.keys())}")
            if 'score_series' in result.metadata:
                scores = result.metadata['score_series']
                print(f"   - 评分序列长度: {len(scores)}")
                print(f"   - 评分范围: {min(scores):.2f} ~ {max(scores):.2f}")
                print(f"   - 非零评分数量: {sum(1 for s in scores if s != 0)}")
        
        # 5. 测试源代码指标直接调用
        print("\n=== 测试源代码指标直接调用 ===")
        if rsi_unit.source_available:
            # 准备数据字典格式
            data_dict = {
                'date': test_data['date'].dt.strftime('%Y-%m-%d').tolist(),
                'open': test_data['open'].values,
                'high': test_data['high'].values,
                'low': test_data['low'].values,
                'close': test_data['close'].values,
                'volume': test_data['volume'].values
            }
            
            # 直接调用源代码指标
            source_result = rsi_unit.source_indicator.calculate(data_dict)
            
            print(f"✅ 源代码指标调用成功")
            print(f"   - 返回键: {list(source_result.keys()) if source_result else 'None'}")
            
            if source_result and 'rsi_score' in source_result:
                rsi_scores = source_result['rsi_score']
                print(f"   - RSI评分数组长度: {len(rsi_scores)}")
                print(f"   - RSI评分范围: {min(rsi_scores):.2f} ~ {max(rsi_scores):.2f}")
                print(f"   - 非零评分数量: {sum(1 for s in rsi_scores if s != 0)}")
                
                # 检查是否有有效的CRSI数据
                if 'crsi_daily' in source_result:
                    crsi_daily = source_result['crsi_daily']
                    valid_crsi = crsi_daily[~np.isnan(crsi_daily)]
                    print(f"   - 有效CRSI数据点: {len(valid_crsi)}")
                    if len(valid_crsi) > 0:
                        print(f"   - CRSI范围: {min(valid_crsi):.2f} ~ {max(valid_crsi):.2f}")
        
        # 6. 测试模拟数据库调用
        print("\n=== 测试模拟数据库调用 ===")
        try:
            # 模拟数据库连接字符串
            db_conn = "************************************************/fintech_db"
            symbol = "test_symbol"
            
            # 使用DataProvider接口
            result_with_db = rsi_unit.calculate_score(
                db_conn=db_conn,
                symbol=symbol,
                ohlc=test_data  # 提供备用数据
            )
            
            print(f"✅ 数据库接口测试完成")
            print(f"   - 评分: {result_with_db.score}")
            print(f"   - 信号: {result_with_db.signal}")
            
        except Exception as e:
            print(f"⚠️  数据库接口测试失败（预期）: {e}")
        
        # 7. 总结测试结果
        print("\n" + "=" * 60)
        print("📋 测试结果总结")
        print("=" * 60)
        
        if result.score != 0.0:
            print("✅ RSI指标修复成功！")
            print(f"   - RSI评分正常: {result.score}")
            print(f"   - 信号类型: {result.signal}")
            return True
        else:
            print("⚠️  RSI指标仍返回0评分，需要进一步调试")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rsi_vs_other_indicators():
    """对比RSI与其他指标的表现"""
    print("\n🔍 对比RSI与其他指标")
    print("=" * 60)
    
    # 创建相同的测试数据
    dates = pd.date_range('2024-01-01', periods=100, freq='D')
    np.random.seed(42)
    base_price = 100.0
    price_changes = np.random.normal(0, 0.02, 100)
    close_prices = base_price + np.cumsum(price_changes)
    
    test_data = pd.DataFrame({
        'date': dates,
        'open': close_prices * (1 + np.random.uniform(-0.01, 0.01, 100)),
        'high': close_prices * (1 + np.random.uniform(0.005, 0.02, 100)),
        'low': close_prices * (1 + np.random.uniform(-0.02, -0.005, 100)),
        'close': close_prices,
        'volume': np.random.randint(1000, 10000, 100)
    })
    
    indicators = [
        ('MCSI RSI', 'core.scoring_units.mcsi_rsi_scoring', 'MCSIRSIScoringUnit'),
        ('MCSI MACD', 'core.scoring_units.mcsi_macd_scoring', 'MCSIMACDScoringUnit'),
        ('MCSI MMT', 'core.scoring_units.mcsi_mmt_scoring', 'MCSIMMTScoringUnit'),
    ]
    
    for name, module_path, class_name in indicators:
        try:
            # 动态导入
            module = __import__(module_path, fromlist=[class_name])
            indicator_class = getattr(module, class_name)
            
            # 创建实例并测试
            indicator = indicator_class()
            result = indicator.calculate_score(data=test_data)
            
            print(f"{name:12}: 评分={result.score:8.2f}, 信号={result.signal}")
            
        except Exception as e:
            print(f"{name:12}: 测试失败 - {e}")

if __name__ == "__main__":
    # 设置日志级别
    logging.basicConfig(level=logging.INFO)
    
    print("🚀 开始RSI指标修复验证")
    
    # 主要测试
    success = test_rsi_unified_interface()
    
    # 对比测试
    test_rsi_vs_other_indicators()
    
    # 最终结果
    print("\n" + "=" * 60)
    if success:
        print("🎉 RSI指标修复验证成功！")
    else:
        print("❌ RSI指标仍需进一步修复")
    print("=" * 60)
