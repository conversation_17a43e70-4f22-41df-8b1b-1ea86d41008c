#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Step 1 工作成果全面检验报告
验证另一个AI完成的Step 1任务是否符合要求
"""

import sys
import os
import json
import numpy as np
import pandas as pd
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (文件不存在)")
        return False

def analyze_baseline_scores():
    """分析基准分数文件"""
    print("\n=== 1. 基准分数文件分析 ===")
    
    baseline_file = "claude_test/baseline_scores.json"
    if not check_file_exists(baseline_file, "基准分数文件"):
        return False
    
    try:
        with open(baseline_file, 'r', encoding='utf-8') as f:
            baseline_data = json.load(f)
        
        metadata = baseline_data.get('metadata', {})
        scores = baseline_data.get('scores', {})
        
        print(f"  📊 数据源: {metadata.get('data_source', 'N/A')}")
        print(f"  📅 生成时间: {metadata.get('generated_at', 'N/A')}")
        print(f"  📈 数据点数: {metadata.get('data_points', 'N/A')}")
        print(f"  🎯 权威版本: {metadata.get('authority_version', 'N/A')}")
        
        # 检查4个指标
        expected_indicators = ['mcsi_macd', 'mcsi_mmt', 'mcsi_rsi', 'mcsi_ttm']
        found_indicators = []
        
        for indicator in expected_indicators:
            if indicator in scores:
                values = scores[indicator].get('values', [])
                if len(values) >= 100:  # 要求至少100个历史点
                    print(f"  ✅ {indicator.upper()}: {len(values)} 个分数点")
                    found_indicators.append(indicator)
                    
                    # 分析分数范围
                    non_zero_values = [v for v in values if v != 0]
                    if non_zero_values:
                        min_val = min(non_zero_values)
                        max_val = max(non_zero_values)
                        mean_val = np.mean(non_zero_values)
                        print(f"    📊 范围: [{min_val:.2f}, {max_val:.2f}], 均值: {mean_val:.2f}")
                else:
                    print(f"  ❌ {indicator.upper()}: 数据点不足 ({len(values)} < 100)")
            else:
                print(f"  ❌ {indicator.upper()}: 缺失")
        
        success = len(found_indicators) == 4
        print(f"  🎯 指标完整性: {len(found_indicators)}/4 {'✅' if success else '❌'}")
        return success
        
    except Exception as e:
        print(f"  ❌ 基准分数文件解析失败: {e}")
        return False

def check_authority_version():
    """检查权威版本文件"""
    print("\n=== 2. 权威版本文件检查 ===")
    
    authority_path = "TV-code/py-code"
    if not os.path.exists(authority_path):
        print(f"❌ 权威版本目录不存在: {authority_path}")
        return False
    
    expected_files = [
        'mcsi_macd.py',
        'mcsi_mmt.py', 
        'mcsi_rsi.py',
        'mcsi_ttm.py'
    ]
    
    found_files = []
    for file_name in expected_files:
        file_path = os.path.join(authority_path, file_name)
        if check_file_exists(file_path, f"权威版本 {file_name}"):
            found_files.append(file_name)
    
    success = len(found_files) == 4
    print(f"  🎯 权威版本完整性: {len(found_files)}/4 {'✅' if success else '❌'}")
    return success

def check_unified_interface():
    """检查统一接口实现"""
    print("\n=== 3. 统一接口实现检查 ===")
    
    interface_path = "core/scoring_units"
    expected_interfaces = [
        'mcsi_macd_scoring.py',
        'mcsi_mmt_scoring.py',
        'mcsi_rsi_scoring.py', 
        'mcsi_ttm_scoring.py'
    ]
    
    found_interfaces = []
    for interface_name in expected_interfaces:
        interface_file = os.path.join(interface_path, interface_name)
        if check_file_exists(interface_file, f"统一接口 {interface_name}"):
            found_interfaces.append(interface_name)
    
    # 检查BaseScoringUnit基类
    base_class_file = os.path.join(interface_path, 'base_scoring_unit.py')
    base_class_exists = check_file_exists(base_class_file, "BaseScoringUnit基类")
    
    success = len(found_interfaces) == 4 and base_class_exists
    print(f"  🎯 统一接口完整性: {len(found_interfaces)}/4 + 基类 {'✅' if success else '❌'}")
    return success

def check_verification_results():
    """检查验证结果"""
    print("\n=== 4. 一致性验证结果检查 ===")
    
    verification_file = "claude_test/verification_results.json"
    if not check_file_exists(verification_file, "验证结果文件"):
        return False
    
    try:
        with open(verification_file, 'r', encoding='utf-8') as f:
            verification_data = json.load(f)
        
        results = verification_data.get('results', {})
        
        print(f"  📅 验证时间: {verification_data.get('timestamp', 'N/A')}")
        print(f"  📊 测试数据点: {verification_data.get('test_data_points', 'N/A')}")
        
        consistent_count = 0
        total_count = 0
        
        for indicator, result in results.items():
            total_count += 1
            if result.get('consistent', False):
                consistent_count += 1
                match_rate = result.get('match_rate', 0)
                max_diff = result.get('max_difference', 0)
                print(f"  ✅ {indicator.upper()}: 100%一致 (最大差异: {max_diff:.2e})")
            else:
                match_rate = result.get('match_rate', 0)
                max_diff = result.get('max_difference', 0)
                print(f"  ❌ {indicator.upper()}: 不一致 (匹配率: {match_rate:.1f}%, 最大差异: {max_diff:.2f})")
        
        success = consistent_count == total_count and consistent_count == 4
        print(f"  🎯 总体一致性: {consistent_count}/{total_count} {'✅' if success else '❌'}")
        return success
        
    except Exception as e:
        print(f"  ❌ 验证结果文件解析失败: {e}")
        return False

def check_data_source():
    """检查数据源"""
    print("\n=== 5. 数据源检查 ===")
    
    # 检查上证指数数据
    data_files = [
        "stock_data/cnindex_000001_上证指数.csv",
        "cnindex_000001_上证指数.csv"  # 可能的备用位置
    ]
    
    data_found = False
    for data_file in data_files:
        if os.path.exists(data_file):
            print(f"✅ 数据源文件: {data_file}")
            
            # 检查数据量
            try:
                df = pd.read_csv(data_file)
                print(f"  📊 数据行数: {len(df)}")
                if len(df) >= 200:  # 要求至少200个数据点
                    print(f"  ✅ 数据量充足 (>= 200)")
                    data_found = True
                else:
                    print(f"  ❌ 数据量不足 (< 200)")
            except Exception as e:
                print(f"  ❌ 数据文件读取失败: {e}")
            break
    
    if not data_found:
        print("❌ 未找到有效的数据源文件")
    
    return data_found

def generate_summary_report():
    """生成总结报告"""
    print("\n" + "="*60)
    print("📋 Step 1 工作成果检验总结报告")
    print("="*60)
    
    # 执行所有检查
    checks = [
        ("基准分数生成", analyze_baseline_scores),
        ("权威版本确认", check_authority_version), 
        ("统一接口实现", check_unified_interface),
        ("一致性验证", check_verification_results),
        ("数据源准备", check_data_source)
    ]
    
    passed_checks = 0
    total_checks = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n🔍 检查项目: {check_name}")
        try:
            if check_func():
                passed_checks += 1
                print(f"✅ {check_name}: 通过")
            else:
                print(f"❌ {check_name}: 未通过")
        except Exception as e:
            print(f"❌ {check_name}: 检查失败 - {e}")
    
    # 最终评估
    print(f"\n{'='*60}")
    print(f"📊 检验结果: {passed_checks}/{total_checks} 项通过")
    
    if passed_checks == total_checks:
        print("🎉 Step 1 任务完成度: 100% - 完全符合要求！")
        print("✅ 可以继续进行后续步骤")
    elif passed_checks >= total_checks * 0.8:
        print("⚠️  Step 1 任务完成度: 80%+ - 基本符合要求，有小问题需要修复")
    else:
        print("❌ Step 1 任务完成度: <80% - 存在重大问题，需要重新完成")
    
    # 保存报告
    report_data = {
        'timestamp': datetime.now().isoformat(),
        'total_checks': total_checks,
        'passed_checks': passed_checks,
        'completion_rate': passed_checks / total_checks * 100,
        'status': 'PASS' if passed_checks == total_checks else 'PARTIAL' if passed_checks >= total_checks * 0.8 else 'FAIL'
    }
    
    report_file = "aug_test/step1_verification_report.json"
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细报告已保存到: {report_file}")

if __name__ == "__main__":
    generate_summary_report()
