#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的RSI指标测试
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def main():
    print("🔧 简单RSI测试")
    
    try:
        # 导入RSI单元
        from core.scoring_units.mcsi_rsi_scoring import MCSIRSIScoringUnit
        print("✅ RSI单元导入成功")
        
        # 创建RSI实例
        rsi_unit = MCSIRSIScoringUnit()
        print(f"✅ RSI单元初始化: source_available={rsi_unit.source_available}")
        
        # 创建测试数据
        np.random.seed(42)
        close_prices = 100 + np.cumsum(np.random.normal(0, 1, 100))
        
        test_data = pd.DataFrame({
            'close': close_prices,
            'open': close_prices * 0.99,
            'high': close_prices * 1.01,
            'low': close_prices * 0.98,
            'volume': [1000] * 100
        })
        
        print(f"✅ 测试数据创建: {len(test_data)}行")
        
        # 验证数据
        is_valid = rsi_unit.validate_data(test_data)
        print(f"✅ 数据验证: {is_valid}")
        
        # 计算RSI
        result = rsi_unit.calculate_score(data=test_data)
        print(f"✅ RSI计算完成")
        print(f"   评分: {result.score}")
        print(f"   信号: {result.signal}")
        print(f"   描述: {result.description}")
        
        if result.score != 0.0:
            print("🎉 RSI指标修复成功！")
            return True
        else:
            print("⚠️  RSI评分仍为0")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
