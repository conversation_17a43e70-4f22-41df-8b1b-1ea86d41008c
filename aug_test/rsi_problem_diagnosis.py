#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RSI指标问题诊断脚本
专门分析RSI指标为什么返回0值和无法绘图的问题
"""

import sys
import os
import pandas as pd
import numpy as np
import logging

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_rsi_data_flow():
    """测试RSI指标的数据流转过程"""
    print("🔍 RSI指标数据流转诊断")
    print("=" * 60)
    
    # 1. 测试MCSI RSI单元的导入和初始化
    print("\n=== 1. 测试MCSI RSI单元导入 ===")
    try:
        from core.scoring_units.mcsi_rsi_scoring import MCSIRSIScoringUnit
        rsi_unit = MCSIRSIScoringUnit()
        print(f"✅ RSI单元初始化成功: {rsi_unit.source_available}")
        print(f"   - 单元ID: {rsi_unit.unit_id}")
        print(f"   - 参数: dom_cycle={rsi_unit.dom_cycle}, vibration={rsi_unit.vibration}")
    except Exception as e:
        print(f"❌ RSI单元导入失败: {e}")
        return False
    
    # 2. 创建测试数据
    print("\n=== 2. 创建测试数据 ===")
    try:
        # 模拟股票数据
        dates = pd.date_range('2024-01-01', periods=100, freq='D')
        np.random.seed(42)
        base_price = 100.0
        price_changes = np.random.normal(0, 0.02, 100)
        close_prices = base_price + np.cumsum(price_changes)
        
        test_data = pd.DataFrame({
            'timestamp': dates,
            'open': close_prices * (1 + np.random.uniform(-0.01, 0.01, 100)),
            'high': close_prices * (1 + np.random.uniform(0.005, 0.02, 100)),
            'low': close_prices * (1 + np.random.uniform(-0.02, -0.005, 100)),
            'close': close_prices,
            'volume': np.random.randint(1000, 10000, 100)
        })
        
        print(f"✅ 测试数据创建成功: {len(test_data)}行")
        print(f"   - 价格范围: {test_data['close'].min():.2f} ~ {test_data['close'].max():.2f}")
        print(f"   - 数据列: {list(test_data.columns)}")
        
    except Exception as e:
        print(f"❌ 测试数据创建失败: {e}")
        return False
    
    # 3. 测试RSI单元的数据验证
    print("\n=== 3. 测试数据验证 ===")
    try:
        validation_result = rsi_unit.validate_data(test_data)
        print(f"✅ 数据验证结果: {validation_result}")
        
        if not validation_result:
            print("❌ 数据验证失败，检查具体原因...")
            # 检查具体验证失败的原因
            print(f"   - 数据类型: {type(test_data)}")
            print(f"   - 数据长度: {len(test_data)}")
            print(f"   - 必需列检查: {'close' in test_data.columns}")
            print(f"   - 最小长度要求: {rsi_unit.dom_cycle * 4}")
            print(f"   - 空值检查: {test_data['close'].isnull().any()}")
            print(f"   - 非正值检查: {(test_data['close'] <= 0).any()}")
            
    except Exception as e:
        print(f"❌ 数据验证异常: {e}")
        return False
    
    # 4. 测试RSI计算过程
    print("\n=== 4. 测试RSI计算过程 ===")
    try:
        # 直接调用calculate_score方法
        result = rsi_unit.calculate_score(data=test_data)
        
        print(f"✅ RSI计算完成")
        print(f"   - 评分: {result.score}")
        print(f"   - 信号: {result.signal}")
        print(f"   - 置信度: {result.confidence}")
        print(f"   - 描述: {result.description}")
        
        if hasattr(result, 'metadata') and result.metadata:
            print(f"   - 元数据: {list(result.metadata.keys())}")
            if 'score_series' in result.metadata:
                scores = result.metadata['score_series']
                print(f"   - 评分序列长度: {len(scores)}")
                print(f"   - 评分范围: {min(scores):.2f} ~ {max(scores):.2f}")
                print(f"   - 非零评分数量: {sum(1 for s in scores if s != 0)}")
        
        # 检查是否所有评分都是0
        if result.score == 0.0:
            print("⚠️  警告: RSI评分为0，需要深入分析")
            return "zero_score"
        
    except Exception as e:
        print(f"❌ RSI计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 5. 测试源代码指标直接调用
    print("\n=== 5. 测试源代码指标直接调用 ===")
    try:
        if rsi_unit.source_available:
            # 准备数据字典格式
            data_dict = {
                'date': test_data['timestamp'].dt.strftime('%Y-%m-%d').tolist(),
                'open': test_data['open'].values,
                'high': test_data['high'].values,
                'low': test_data['low'].values,
                'close': test_data['close'].values,
                'volume': test_data['volume'].values
            }
            
            # 直接调用源代码指标
            source_result = rsi_unit.source_indicator.calculate(data_dict)
            
            print(f"✅ 源代码指标调用成功")
            print(f"   - 返回键: {list(source_result.keys()) if source_result else 'None'}")
            
            if source_result and 'rsi_score' in source_result:
                rsi_scores = source_result['rsi_score']
                print(f"   - RSI评分数组长度: {len(rsi_scores)}")
                print(f"   - RSI评分范围: {min(rsi_scores):.2f} ~ {max(rsi_scores):.2f}")
                print(f"   - 非零评分数量: {sum(1 for s in rsi_scores if s != 0)}")
                
                # 检查是否所有评分都是0
                if all(s == 0 for s in rsi_scores):
                    print("⚠️  警告: 源代码指标返回的所有RSI评分都是0")
                    return "source_zero"
            else:
                print("❌ 源代码指标未返回rsi_score")
                return "no_rsi_score"
        else:
            print("❌ 源代码指标不可用")
            return False
            
    except Exception as e:
        print(f"❌ 源代码指标调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def compare_with_other_indicators():
    """对比其他正常指标的实现"""
    print("\n🔍 对比其他正常指标")
    print("=" * 60)
    
    # 测试MACD指标
    print("\n=== 测试MACD指标 ===")
    try:
        from core.scoring_units.mcsi_macd_scoring import MCSIMACDScoringUnit
        macd_unit = MCSIMACDScoringUnit()
        print(f"✅ MACD单元: {macd_unit.source_available}")
        
        # 创建相同的测试数据
        dates = pd.date_range('2024-01-01', periods=100, freq='D')
        np.random.seed(42)
        base_price = 100.0
        price_changes = np.random.normal(0, 0.02, 100)
        close_prices = base_price + np.cumsum(price_changes)
        
        test_data = pd.DataFrame({
            'open': close_prices * (1 + np.random.uniform(-0.01, 0.01, 100)),
            'high': close_prices * (1 + np.random.uniform(0.005, 0.02, 100)),
            'low': close_prices * (1 + np.random.uniform(-0.02, -0.005, 100)),
            'close': close_prices,
            'volume': np.random.randint(1000, 10000, 100)
        })
        
        # 测试MACD计算
        macd_result = macd_unit.calculate_score(data=test_data)
        print(f"   - MACD评分: {macd_result.score}")
        print(f"   - MACD信号: {macd_result.signal}")
        
    except Exception as e:
        print(f"❌ MACD测试失败: {e}")
    
    # 测试MMT指标
    print("\n=== 测试MMT指标 ===")
    try:
        from core.scoring_units.mcsi_mmt_scoring import MCSIMMTScoringUnit
        mmt_unit = MCSIMMTScoringUnit()
        print(f"✅ MMT单元: {mmt_unit.source_available}")
        
        # 测试MMT计算
        mmt_result = mmt_unit.calculate_score(data=test_data)
        print(f"   - MMT评分: {mmt_result.score}")
        print(f"   - MMT信号: {mmt_result.signal}")
        
    except Exception as e:
        print(f"❌ MMT测试失败: {e}")

if __name__ == "__main__":
    # 设置日志级别
    logging.basicConfig(level=logging.INFO)
    
    print("🚀 开始RSI指标问题诊断")
    
    # 主要诊断流程
    result = test_rsi_data_flow()
    
    # 对比测试
    compare_with_other_indicators()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 诊断总结")
    print("=" * 60)
    
    if result == True:
        print("✅ RSI指标基本功能正常")
    elif result == "zero_score":
        print("⚠️  RSI指标返回0评分 - 需要检查评分计算逻辑")
    elif result == "source_zero":
        print("⚠️  源代码指标返回0评分 - 问题在源代码实现")
    elif result == "no_rsi_score":
        print("❌ 源代码指标未返回rsi_score - 数据格式问题")
    else:
        print("❌ RSI指标存在严重问题")
