#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Step 2 工作成果全面检验报告
验证另一个AI完成的Step 2任务是否符合要求
"""

import sys
import os
import json
import numpy as np
import pandas as pd
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (文件不存在)")
        return False

def check_adapter_integration():
    """检查适配器集成情况 - Step 2.1"""
    print("\n=== 2.1 适配器集成检查 ===")
    
    # 检查mcsi_adapter.py文件
    adapter_file = "core/scoring_units/mcsi_adapter.py"
    if not check_file_exists(adapter_file, "MCSI适配器文件"):
        return False
    
    try:
        # 检查适配器是否正确集成统一接口
        from core.scoring_units.mcsi_adapter import MCSIAdapter
        
        # 检查4个MCSI指标的创建方法
        methods = [
            'create_mcsi_macd_unit',
            'create_mcsi_mmt_unit', 
            'create_mcsi_rsi_unit',
            'create_mcsi_ttm_unit'
        ]
        
        found_methods = []
        for method in methods:
            if hasattr(MCSIAdapter, method):
                found_methods.append(method)
                print(f"  ✅ {method}: 存在")
            else:
                print(f"  ❌ {method}: 缺失")
        
        # 测试适配器是否使用"standard"类型
        csv_path = "stock_data/cnindex_000001_上证指数.csv"
        if os.path.exists(csv_path):
            df = pd.read_csv(csv_path).tail(150)
            
            macd_unit = MCSIAdapter.create_mcsi_macd_unit()
            result = macd_unit.calculate_score(df)
            
            unit_type = result.metadata.get('unit_type', 'unknown')
            if unit_type == 'standard':
                print(f"  ✅ 适配器使用'standard'类型统一接口")
            else:
                print(f"  ❌ 适配器使用类型: {unit_type}")
                return False
        
        success = len(found_methods) == 4
        print(f"  🎯 适配器方法完整性: {len(found_methods)}/4 {'✅' if success else '❌'}")
        return success
        
    except Exception as e:
        print(f"  ❌ 适配器集成检查失败: {e}")
        return False

def check_data_source_support():
    """检查数据源支持情况 - Step 2.2"""
    print("\n=== 2.2 数据源支持检查 ===")
    
    try:
        from core.scoring_units.mcsi_adapter import MCSIAdapter
        
        # 检查DataFrame数据源支持
        csv_path = "stock_data/cnindex_000001_上证指数.csv"
        if os.path.exists(csv_path):
            df = pd.read_csv(csv_path).tail(150)
            print(f"  📊 CSV数据: {len(df)} 行")
            
            macd_unit = MCSIAdapter.create_mcsi_macd_unit()
            result = macd_unit.calculate_score(df)
            
            data_source = result.metadata.get('data_source', 'unknown')
            rows_processed = result.metadata.get('rows_processed', 0)
            
            print(f"  ✅ DataFrame数据源: {data_source}, 处理行数: {rows_processed}")
            
            if rows_processed >= 100:  # 检查是否处理了足够的数据
                print(f"  ✅ 数据处理量充足: {rows_processed} >= 100")
                dataframe_ok = True
            else:
                print(f"  ❌ 数据处理量不足: {rows_processed} < 100")
                dataframe_ok = False
        else:
            print(f"  ❌ 测试数据文件不存在: {csv_path}")
            dataframe_ok = False
        
        # 检查数据库数据源支持（模拟）
        db_config = {
            'db_conn': 'test_connection',
            'symbol': 'test_symbol',
            'period': 'daily'
        }
        
        try:
            result_db = macd_unit.calculate_score(db_config)
            db_data_source = result_db.metadata.get('data_source', 'unknown')
            print(f"  ✅ 数据库数据源支持: {db_data_source}")
            database_ok = True
        except Exception as e:
            print(f"  ⚠️  数据库数据源测试: {str(e)[:100]}...")
            database_ok = False  # 数据库不可用是正常的
        
        return dataframe_ok  # 主要检查DataFrame支持
        
    except Exception as e:
        print(f"  ❌ 数据源支持检查失败: {e}")
        return False

def check_consistency_verification():
    """检查一致性验证情况 - Step 2.3"""
    print("\n=== 2.3 一致性验证检查 ===")
    
    # 检查基准比较报告
    comparison_file = "claude_test/baseline_comparison_report.json"
    if not check_file_exists(comparison_file, "基准比较报告"):
        return False
    
    try:
        with open(comparison_file, 'r', encoding='utf-8') as f:
            comparison_data = json.load(f)
        
        summary = comparison_data.get('comparison_summary', {})
        detailed = comparison_data.get('detailed_comparison', {})
        
        print(f"  📅 比较时间: {comparison_data.get('timestamp', 'N/A')}")
        print(f"  📊 总指标数: {summary.get('total_indicators', 0)}")
        print(f"  ✅ 一致指标数: {summary.get('consistent_indicators', 0)}")
        print(f"  📈 总体一致性: {summary.get('overall_consistency_rate', 0)}%")
        
        # 检查每个指标的一致性
        consistent_count = 0
        total_count = 0
        
        for indicator, result in detailed.items():
            total_count += 1
            if result.get('consistent', False):
                consistent_count += 1
                max_diff = result.get('max_difference', 0)
                print(f"  ✅ {indicator.upper()}: 100%一致 (最大差异: {max_diff:.2e})")
            else:
                match_rate = result.get('match_rate', 0)
                max_diff = result.get('max_difference', 0)
                print(f"  ❌ {indicator.upper()}: 不一致 (匹配率: {match_rate:.1f}%, 最大差异: {max_diff:.2f})")
        
        success = consistent_count == total_count and consistent_count == 4
        print(f"  🎯 修改前后一致性: {consistent_count}/{total_count} {'✅' if success else '❌'}")
        return success
        
    except Exception as e:
        print(f"  ❌ 一致性验证检查失败: {e}")
        return False

def check_api_verification():
    """检查API验证情况 - Step 2.4"""
    print("\n=== 2.4 API验证检查 ===")
    
    # 检查API测试文件
    api_test_files = [
        "claude_test/test_api_endpoints.py",
        "claude_test/test_simple_api.py"
    ]
    
    found_api_tests = []
    for test_file in api_test_files:
        if check_file_exists(test_file, f"API测试文件 {os.path.basename(test_file)}"):
            found_api_tests.append(test_file)
    
    if len(found_api_tests) == 0:
        print("  ❌ 未找到API测试文件")
        return False
    
    # 检查适配器API兼容性
    try:
        from core.scoring_units.mcsi_adapter import MCSIAdapter
        
        # 测试核心MCSI适配器API
        csv_path = "stock_data/cnindex_000001_上证指数.csv"
        if os.path.exists(csv_path):
            df = pd.read_csv(csv_path).tail(100)
            
            # 测试所有4个指标的API响应格式
            indicators = [
                ('MACD', MCSIAdapter.create_mcsi_macd_unit),
                ('MMT', MCSIAdapter.create_mcsi_mmt_unit),
                ('RSI', MCSIAdapter.create_mcsi_rsi_unit),
                ('TTM', MCSIAdapter.create_mcsi_ttm_unit)
            ]
            
            api_ok_count = 0
            for name, create_func in indicators:
                try:
                    unit = create_func()
                    result = unit.calculate_score(df)
                    
                    # 检查API响应格式
                    required_fields = ['score', 'signal', 'confidence', 'description', 'metadata']
                    has_all_fields = all(hasattr(result, field) for field in required_fields)
                    
                    if has_all_fields:
                        print(f"  ✅ {name} API响应格式正确")
                        api_ok_count += 1
                    else:
                        print(f"  ❌ {name} API响应格式不完整")
                        
                except Exception as e:
                    print(f"  ❌ {name} API测试失败: {e}")
            
            success = api_ok_count == 4
            print(f"  🎯 API兼容性: {api_ok_count}/4 {'✅' if success else '❌'}")
            return success
        else:
            print("  ❌ 测试数据不可用")
            return False
            
    except Exception as e:
        print(f"  ❌ API验证检查失败: {e}")
        return False

def check_exception_handling():
    """检查异常处理情况 - Step 2.5"""
    print("\n=== 2.5 异常处理检查 ===")
    
    # 检查异常处理测试文件
    exception_test_file = "claude_test/test_exception_handling.py"
    if not check_file_exists(exception_test_file, "异常处理测试文件"):
        return False
    
    # 检查日志文件
    log_file = "claude_test/test_exception_handling.log"
    if check_file_exists(log_file, "异常处理日志文件"):
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                log_content = f.read()
            
            # 检查日志内容
            log_lines = log_content.strip().split('\n')
            print(f"  📝 日志行数: {len(log_lines)}")
            
            # 检查是否包含各种日志级别
            has_info = any('INFO' in line for line in log_lines)
            has_warning = any('WARNING' in line for line in log_lines)
            has_error = any('ERROR' in line for line in log_lines)
            
            print(f"  📊 日志级别: INFO={has_info}, WARNING={has_warning}, ERROR={has_error}")
            
        except Exception as e:
            print(f"  ⚠️  日志文件读取失败: {e}")
    
    # 模拟异常处理测试
    try:
        from core.scoring_units.mcsi_adapter import MCSIAdapter
        
        # 测试空数据处理
        empty_df = pd.DataFrame()
        macd_unit = MCSIAdapter.create_mcsi_macd_unit()
        result = macd_unit.calculate_score(empty_df)
        
        if result.score == 0.0 and 'error' in result.metadata:
            print(f"  ✅ 空数据异常处理正常")
            empty_ok = True
        else:
            print(f"  ❌ 空数据异常处理异常")
            empty_ok = False
        
        # 测试无效数据处理
        invalid_df = pd.DataFrame({'invalid_column': [1, 2, 3]})
        result_invalid = macd_unit.calculate_score(invalid_df)
        
        if result_invalid.score == 0.0:
            print(f"  ✅ 无效数据异常处理正常")
            invalid_ok = True
        else:
            print(f"  ❌ 无效数据异常处理异常")
            invalid_ok = False
        
        success = empty_ok and invalid_ok
        print(f"  🎯 异常处理健壮性: {'✅' if success else '❌'}")
        return success
        
    except Exception as e:
        print(f"  ❌ 异常处理检查失败: {e}")
        return False

def generate_step2_report():
    """生成Step 2总结报告"""
    print("\n" + "="*60)
    print("📋 Step 2 工作成果检验总结报告")
    print("="*60)
    
    # 执行所有检查
    checks = [
        ("2.1 适配器集成", check_adapter_integration),
        ("2.2 数据源支持", check_data_source_support), 
        ("2.3 一致性验证", check_consistency_verification),
        ("2.4 API验证", check_api_verification),
        ("2.5 异常处理", check_exception_handling)
    ]
    
    passed_checks = 0
    total_checks = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n🔍 检查项目: {check_name}")
        try:
            if check_func():
                passed_checks += 1
                print(f"✅ {check_name}: 通过")
            else:
                print(f"❌ {check_name}: 未通过")
        except Exception as e:
            print(f"❌ {check_name}: 检查失败 - {e}")
    
    # 最终评估
    print(f"\n{'='*60}")
    print(f"📊 检验结果: {passed_checks}/{total_checks} 项通过")
    
    if passed_checks == total_checks:
        print("🎉 Step 2 任务完成度: 100% - 完全符合要求！")
        print("✅ 数据分离与适配器接入成功完成")
        status = "EXCELLENT"
    elif passed_checks >= total_checks * 0.8:
        print("⚠️  Step 2 任务完成度: 80%+ - 基本符合要求，有小问题需要修复")
        status = "GOOD"
    else:
        print("❌ Step 2 任务完成度: <80% - 存在重大问题，需要重新完成")
        status = "NEEDS_WORK"
    
    # 保存报告
    report_data = {
        'timestamp': datetime.now().isoformat(),
        'step': 'Step 2',
        'title': '数据分离与适配器接入',
        'total_checks': total_checks,
        'passed_checks': passed_checks,
        'completion_rate': passed_checks / total_checks * 100,
        'status': status
    }
    
    report_file = "aug_test/step2_verification_report.json"
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细报告已保存到: {report_file}")

if __name__ == "__main__":
    generate_step2_report()
