#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCSI技术指标验证脚本
验证Python实现与Pine Script源代码的一致性
"""

import sys
import os
import numpy as np
import pandas as pd
from pathlib import Path
import logging

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'TV-code' / 'py-code'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_test_data(length=100, seed=42):
    """生成测试数据"""
    np.random.seed(seed)
    base_price = 100.0
    
    # 生成价格序列
    returns = np.random.normal(0, 0.02, length)
    close_prices = base_price * np.exp(np.cumsum(returns))
    
    # 生成OHLC数据
    data = {
        'open': close_prices * (1 + np.random.uniform(-0.01, 0.01, length)),
        'high': close_prices * (1 + np.random.uniform(0.005, 0.02, length)),
        'low': close_prices * (1 + np.random.uniform(-0.02, -0.005, length)),
        'close': close_prices,
        'volume': np.random.randint(1000, 10000, length),
        'date': pd.date_range(start='2024-01-01', periods=length, freq='D')
    }
    
    return pd.DataFrame(data)

def test_macd_implementation():
    """测试MACD实现"""
    logger.info("=== 测试MACD实现 ===")
    
    try:
        from mcsi_macd import MCSIMACDIndicator
        
        # 生成测试数据
        test_data = generate_test_data(200)
        
        # 创建指标实例
        macd_indicator = MCSIMACDIndicator()
        
        # 计算指标
        result = macd_indicator.calculate(test_data['close'].values)
        
        # 验证结果
        assert 'macd_score' in result, "缺少MACD评分"
        assert 'histogram' in result, "缺少柱状图数据"
        assert 'dynamic_threshold' in result, "缺少动态阈值"
        
        macd_scores = result['macd_score']
        histogram = result['histogram']
        threshold = result['dynamic_threshold']
        
        # 验证数据质量
        assert len(macd_scores) == len(test_data), "评分长度不匹配"
        assert not np.all(np.isnan(macd_scores)), "所有评分都是NaN"
        assert np.all(np.abs(macd_scores) <= 100), "评分超出范围"
        
        # 验证动态阈值逻辑
        valid_threshold = threshold[~np.isnan(threshold)]
        assert len(valid_threshold) > 0, "没有有效的动态阈值"
        assert np.all(valid_threshold > 0), "动态阈值应该为正值"
        
        logger.info(f"✅ MACD测试通过")
        logger.info(f"   - 评分范围: [{np.nanmin(macd_scores):.2f}, {np.nanmax(macd_scores):.2f}]")
        logger.info(f"   - 非零评分数量: {np.sum(macd_scores != 0)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ MACD测试失败: {str(e)}")
        return False

def test_mmt_implementation():
    """测试MMT实现"""
    logger.info("=== 测试MMT实现 ===")
    
    try:
        from mcsi_mmt import MCSIMMTIndicator
        
        # 生成测试数据
        test_data = generate_test_data(200)
        
        # 创建指标实例
        mmt_indicator = MCSIMMTIndicator()
        
        # 计算指标
        result = mmt_indicator.calculate(
            test_data['close'].values,
            test_data['high'].values,
            test_data['low'].values
        )
        
        # 验证结果
        assert 'mmt_score' in result, "缺少MMT评分"
        assert 'channel_score' in result, "缺少轨道评分"
        assert 'divergence_score' in result, "缺少背离评分"
        assert 'bull_div' in result, "缺少看涨背离"
        assert 'bear_div' in result, "缺少看跌背离"
        
        mmt_scores = result['mmt_score']
        channel_scores = result['channel_score']
        divergence_scores = result['divergence_score']
        
        # 验证数据质量
        assert len(mmt_scores) == len(test_data), "评分长度不匹配"
        assert np.all(np.abs(mmt_scores) <= 100), "评分超出范围"
        
        # 验证背离检测
        bull_div = result['bull_div']
        bear_div = result['bear_div']
        assert isinstance(bull_div, np.ndarray), "看涨背离应该是数组"
        assert isinstance(bear_div, np.ndarray), "看跌背离应该是数组"
        
        logger.info(f"✅ MMT测试通过")
        logger.info(f"   - 评分范围: [{np.nanmin(mmt_scores):.2f}, {np.nanmax(mmt_scores):.2f}]")
        logger.info(f"   - 看涨背离数量: {np.sum(bull_div)}")
        logger.info(f"   - 看跌背离数量: {np.sum(bear_div)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ MMT测试失败: {str(e)}")
        return False

def test_rsi_implementation():
    """测试RSI实现"""
    logger.info("=== 测试RSI实现 ===")
    
    try:
        from mcsi_rsi import MCSIRSIIndicator
        
        # 生成测试数据
        test_data = generate_test_data(200)
        
        # 创建指标实例
        rsi_indicator = MCSIRSIIndicator()
        
        # 测试简单数据输入
        result1 = rsi_indicator.calculate(test_data['close'].values)
        
        # 测试完整数据输入（包含日期）
        data_dict = {
            'close': test_data['close'].values,
            'open': test_data['open'].values,
            'high': test_data['high'].values,
            'low': test_data['low'].values,
            'date': test_data['date'].values
        }
        result2 = rsi_indicator.calculate(data_dict)
        
        # 验证结果
        for result in [result1, result2]:
            assert 'rsi_score' in result, "缺少RSI评分"
            assert 'crsi_daily' in result, "缺少日线CRSI"
            assert 'crsi_weekly' in result, "缺少周线CRSI"
            
            rsi_scores = result['rsi_score']
            assert len(rsi_scores) == len(test_data), "评分长度不匹配"
            assert np.all(np.abs(rsi_scores) <= 100), "评分超出范围"
        
        logger.info(f"✅ RSI测试通过")
        logger.info(f"   - 简单输入评分范围: [{np.nanmin(result1['rsi_score']):.2f}, {np.nanmax(result1['rsi_score']):.2f}]")
        logger.info(f"   - 完整输入评分范围: [{np.nanmin(result2['rsi_score']):.2f}, {np.nanmax(result2['rsi_score']):.2f}]")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ RSI测试失败: {str(e)}")
        return False

def test_ttm_implementation():
    """测试TTM实现"""
    logger.info("=== 测试TTM实现 ===")
    
    try:
        from mcsi_ttm import MCSITTMIndicator
        
        # 生成测试数据
        test_data = generate_test_data(200)
        
        # 创建指标实例
        ttm_indicator = MCSITTMIndicator()
        
        # 计算指标
        result = ttm_indicator.calculate(test_data['close'].values)
        
        # 验证结果
        assert 'ttm_score' in result, "缺少TTM评分"
        assert 'td_up_count' in result, "缺少上升计数"
        assert 'td_down_count' in result, "缺少下降计数"
        
        ttm_scores = result['ttm_score']
        td_up = result['td_up_count']
        td_down = result['td_down_count']
        
        # 验证数据质量
        assert len(ttm_scores) == len(test_data), "评分长度不匹配"
        assert np.all(np.abs(ttm_scores) <= 100), "评分超出范围"
        
        # 验证TD序列
        assert np.all(td_up >= 0), "上升计数应该非负"
        assert np.all(td_down >= 0), "下降计数应该非负"
        
        logger.info(f"✅ TTM测试通过")
        logger.info(f"   - 评分范围: [{np.nanmin(ttm_scores):.2f}, {np.nanmax(ttm_scores):.2f}]")
        logger.info(f"   - 最大上升计数: {np.max(td_up)}")
        logger.info(f"   - 最大下降计数: {np.max(td_down)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ TTM测试失败: {str(e)}")
        return False

def test_unified_interface():
    """测试统一接口"""
    logger.info("=== 测试统一接口 ===")

    try:
        # 导入统一接口
        sys.path.insert(0, str(project_root / 'core'))

        from scoring_units.mcsi_macd_scoring import MCSIMACDScoringUnit
        from scoring_units.mcsi_mmt_scoring import MCSIMMTScoringUnit
        from scoring_units.mcsi_rsi_scoring import MCSIRSIScoringUnit
        from scoring_units.mcsi_ttm_scoring import MCSITTMScoringUnit
        
        # 生成测试数据
        test_data = generate_test_data(200)
        
        # 测试各个评分单元
        scoring_units = [
            ('MACD', MCSIMACDScoringUnit()),
            ('MMT', MCSIMMTScoringUnit()),
            ('RSI', MCSIRSIScoringUnit()),
            ('TTM', MCSITTMScoringUnit())
        ]
        
        for name, unit in scoring_units:
            try:
                result = unit.calculate_score(data=test_data)
                
                assert hasattr(result, 'score'), f"{name}缺少评分"
                assert hasattr(result, 'signal'), f"{name}缺少信号"
                assert hasattr(result, 'confidence'), f"{name}缺少置信度"
                
                assert -100 <= result.score <= 100, f"{name}评分超出范围"
                assert 0 <= result.confidence <= 1, f"{name}置信度超出范围"
                
                logger.info(f"   ✅ {name}: 评分={result.score:.2f}, 信号={result.signal}, 置信度={result.confidence:.2f}")
                
            except Exception as e:
                logger.error(f"   ❌ {name}测试失败: {str(e)}")
                return False
        
        logger.info(f"✅ 统一接口测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 统一接口测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始MCSI技术指标验证")
    
    test_results = []
    
    # 执行各项测试
    test_results.append(('MACD实现', test_macd_implementation()))
    test_results.append(('MMT实现', test_mmt_implementation()))
    test_results.append(('RSI实现', test_rsi_implementation()))
    test_results.append(('TTM实现', test_ttm_implementation()))
    test_results.append(('统一接口', test_unified_interface()))
    
    # 汇总结果
    logger.info("\n=== 验证结果汇总 ===")
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！MCSI技术指标实现质量优秀。")
        return True
    else:
        logger.warning("⚠️ 部分测试失败，请检查实现。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
