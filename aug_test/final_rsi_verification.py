#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RSI指标修复最终验证
确保RSI指标在所有场景下都能正常工作
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_rsi_comprehensive():
    """全面测试RSI指标"""
    print("🎯 RSI指标修复最终验证")
    print("=" * 60)
    
    results = {}
    
    # 1. 测试RSI源代码
    print("\n1️⃣ 测试RSI源代码...")
    try:
        sys.path.insert(0, str(project_root / 'TV-code' / 'py-code'))
        from mcsi_rsi import MCSIRSIIndicator
        
        rsi = MCSIRSIIndicator()
        np.random.seed(42)
        close_prices = 100 + np.cumsum(np.random.normal(0, 1, 100))
        result = rsi.calculate(close_prices)
        
        if result and 'rsi_score' in result:
            scores = result['rsi_score']
            non_zero = sum(1 for s in scores if s != 0)
            results['source_code'] = f"✅ 正常 (非零评分: {non_zero}/{len(scores)})"
        else:
            results['source_code'] = "❌ 失败"
            
    except Exception as e:
        results['source_code'] = f"❌ 异常: {e}"
    
    # 2. 测试RSI评分单元
    print("\n2️⃣ 测试RSI评分单元...")
    try:
        from core.scoring_units.mcsi_rsi_scoring import MCSIRSIScoringUnit
        
        rsi_unit = MCSIRSIScoringUnit()
        
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=100, freq='D')
        close_prices = 100 + np.cumsum(np.random.normal(0, 1, 100))
        
        test_data = pd.DataFrame({
            'date': dates,
            'open': close_prices * 0.99,
            'high': close_prices * 1.01,
            'low': close_prices * 0.98,
            'close': close_prices,
            'volume': [1000] * 100
        })
        
        result = rsi_unit.calculate_score(data=test_data)
        
        if result.score != 0.0:
            results['scoring_unit'] = f"✅ 正常 (评分: {result.score:.2f}, 信号: {result.signal})"
        else:
            results['scoring_unit'] = f"❌ 评分为0 (描述: {result.description})"
            
    except Exception as e:
        results['scoring_unit'] = f"❌ 异常: {e}"
    
    # 3. 测试滑动窗口计算
    print("\n3️⃣ 测试滑动窗口计算...")
    try:
        # 模拟滑动窗口
        window_size = 100
        data_length = 200
        
        dates = pd.date_range('2024-01-01', periods=data_length, freq='D')
        close_prices = 100 + np.cumsum(np.random.normal(0, 1, data_length))
        
        full_data = pd.DataFrame({
            'date': dates,
            'open': close_prices * 0.99,
            'high': close_prices * 1.01,
            'low': close_prices * 0.98,
            'close': close_prices,
            'volume': [1000] * data_length
        })
        
        rsi_unit = MCSIRSIScoringUnit()
        valid_scores = 0
        non_zero_scores = 0
        
        for i in range(data_length):
            window_data = full_data.iloc[max(0, i-window_size+1):i+1]
            
            if len(window_data) >= 50:  # 足够的数据
                result = rsi_unit.calculate_score(window_data)
                if result.score is not None and not np.isnan(result.score):
                    valid_scores += 1
                    if result.score != 0.0:
                        non_zero_scores += 1
        
        if valid_scores > 0:
            results['sliding_window'] = f"✅ 正常 (有效: {valid_scores}, 非零: {non_zero_scores})"
        else:
            results['sliding_window'] = "❌ 无有效评分"
            
    except Exception as e:
        results['sliding_window'] = f"❌ 异常: {e}"
    
    # 4. 对比其他指标
    print("\n4️⃣ 对比其他指标...")
    try:
        from core.scoring_units.mcsi_macd_scoring import MCSIMACDScoringUnit
        from core.scoring_units.mcsi_mmt_scoring import MCSIMMTScoringUnit
        
        # 使用相同的测试数据
        indicators = {
            'RSI': MCSIRSIScoringUnit(),
            'MACD': MCSIMACDScoringUnit(),
            'MMT': MCSIMMTScoringUnit()
        }
        
        comparison = {}
        for name, unit in indicators.items():
            try:
                result = unit.calculate_score(data=test_data)
                comparison[name] = f"评分: {result.score:.2f}, 可用: {unit.source_available}"
            except Exception as e:
                comparison[name] = f"失败: {e}"
        
        results['comparison'] = comparison
        
    except Exception as e:
        results['comparison'] = f"❌ 异常: {e}"
    
    # 5. 输出结果
    print("\n" + "=" * 60)
    print("📋 最终验证结果")
    print("=" * 60)
    
    print(f"RSI源代码:     {results['source_code']}")
    print(f"RSI评分单元:   {results['scoring_unit']}")
    print(f"滑动窗口计算:  {results['sliding_window']}")
    
    if isinstance(results['comparison'], dict):
        print("\n指标对比:")
        for name, status in results['comparison'].items():
            print(f"  {name:6}: {status}")
    else:
        print(f"指标对比:      {results['comparison']}")
    
    # 6. 总结
    print("\n" + "=" * 60)
    
    success_count = 0
    total_tests = 3
    
    if "✅" in results['source_code']:
        success_count += 1
    if "✅" in results['scoring_unit']:
        success_count += 1
    if "✅" in results['sliding_window']:
        success_count += 1
    
    if success_count == total_tests:
        print("🎉 RSI指标修复完全成功！")
        print("   ✅ RSI源代码正常工作")
        print("   ✅ RSI评分单元正常工作")
        print("   ✅ 滑动窗口计算正常工作")
        print("   ✅ RSI指标现在可以正常计算评分并在图表上显示")
        return True
    else:
        print(f"⚠️  RSI指标部分修复成功 ({success_count}/{total_tests})")
        return False

if __name__ == "__main__":
    success = test_rsi_comprehensive()
    
    print("\n" + "=" * 60)
    if success:
        print("🚀 RSI指标问题已完全解决！")
        print("   现在可以启动web应用验证RSI指标的图表显示效果。")
    else:
        print("❌ RSI指标仍有部分问题需要解决")
    print("=" * 60)
