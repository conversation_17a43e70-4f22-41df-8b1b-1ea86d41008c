#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试CSV数据加载，不使用DataProvider
"""

import sys
import os
import pandas as pd

def test_simple_csv():
    """简单测试CSV数据加载"""
    print("=== 简单测试CSV数据 ===")
    
    csv_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'stock_data', 'cnindex_000001_上证指数.csv')
    
    # 直接加载CSV
    df = pd.read_csv(csv_path).tail(100)
    print(f"原始CSV数据: {len(df)} 行")
    print(f"列名: {list(df.columns)}")
    
    # 重命名列以匹配期望格式
    df = df.rename(columns={'timestamp': 'date'})
    df['date'] = pd.to_datetime(df['date'])
    
    # 确保有必要的OHLC列
    required_cols = ['date', 'open', 'high', 'low', 'close', 'volume']
    missing_cols = [col for col in required_cols if col not in df.columns]
    
    if missing_cols:
        print(f"❌ 缺少列: {missing_cols}")
    else:
        print("✅ 所有必要列都存在")
        print(f"数据范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
        
        # 测试与MCSI指标的集成
        sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))
        
        try:
            from core.scoring_units.mcsi_macd_scoring import MCSIMACDScoringUnit
            
            macd_unit = MCSIMACDScoringUnit()
            result = macd_unit.calculate_score(df)
            
            print(f"MCSI MACD 分数: {result.score}")
            print("✅ CSV数据与MCSI指标集成成功")
            
        except Exception as e:
            print(f"❌ MCSI指标集成失败: {e}")

if __name__ == "__main__":
    test_simple_csv()