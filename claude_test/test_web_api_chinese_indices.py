#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web API中国指数修复
通过Web API验证中国指数是否能被正确分析
"""

import requests
import json
import time
from datetime import datetime

def test_analysis_api():
    """测试分析API"""
    base_url = "http://127.0.0.1:50505"
    
    print("🔍 测试Web API分析功能...")
    
    # 启动分析
    print("   启动分析...")
    analysis_payload = {
        "data_limit": 500,
        "force_refresh": True
    }
    
    try:
        response = requests.post(f"{base_url}/api/analyze", json=analysis_payload, timeout=10)
        if response.status_code == 200:
            print("   ✅ 分析请求发送成功")
        else:
            print(f"   ❌ 分析请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 分析请求异常: {e}")
        return False
    
    # 等待分析完成
    print("   等待分析完成...")
    max_wait = 60  # 最多等待60秒
    waited = 0
    
    while waited < max_wait:
        try:
            status_response = requests.get(f"{base_url}/api/status", timeout=5)
            if status_response.status_code == 200:
                status_data = status_response.json()
                if not status_data.get('is_running', True):
                    print("   ✅ 分析完成")
                    break
                else:
                    progress = status_data.get('progress', {})
                    current = progress.get('current', 0)
                    total = progress.get('total', 1)
                    print(f"   📊 分析进度: {current}/{total}")
            
            time.sleep(5)
            waited += 5
        except Exception as e:
            print(f"   ⚠️ 获取状态失败: {e}")
            time.sleep(5)
            waited += 5
    
    if waited >= max_wait:
        print("   ⚠️ 分析超时，继续检查结果...")
    
    return True

def test_results_api():
    """测试结果API"""
    base_url = "http://127.0.0.1:50505"
    
    print("🔍 测试结果API...")
    
    try:
        # 获取分析结果
        response = requests.get(f"{base_url}/api/results", timeout=10)
        
        if response.status_code != 200:
            print(f"   ❌ 获取结果失败: {response.status_code}")
            return False
        
        data = response.json()
        items = data.get('items', [])
        total = data.get('total', 0)
        
        print(f"   ✅ 获取到 {total} 只股票的分析结果")
        
        # 查找中国指数
        chinese_indices = [item for item in items if item.get('category') == '中国指数']
        
        print(f"   🎯 中国指数分析结果: {len(chinese_indices)} 只")
        
        if chinese_indices:
            print("   📊 中国指数详情:")
            for idx in chinese_indices:
                symbol = idx.get('symbol', 'N/A')
                name = idx.get('name', 'N/A')
                score = idx.get('composite_score', idx.get('trend_score', 'N/A'))
                grade = idx.get('composite_grade', idx.get('trend_grade', 'N/A'))
                print(f"      - {name} ({symbol}): 评分 {score}, 等级 {grade}")
            
            return True
        else:
            print("   ❌ 未找到中国指数分析结果")
            
            # 显示所有分类的统计
            categories = {}
            for item in items:
                cat = item.get('category', '未分类')
                categories[cat] = categories.get(cat, 0) + 1
            
            if categories:
                print("   📊 当前分类统计:")
                for category, count in sorted(categories.items()):
                    print(f"      - {category}: {count} 只")
            
            return False
        
    except Exception as e:
        print(f"   ❌ 测试结果API异常: {e}")
        return False

def test_categories_api():
    """测试分类API"""
    base_url = "http://127.0.0.1:50505"
    
    print("🔍 测试分类API...")
    
    try:
        response = requests.get(f"{base_url}/api/categories", timeout=10)
        
        if response.status_code != 200:
            print(f"   ❌ 获取分类失败: {response.status_code}")
            return False
        
        categories = response.json()
        print(f"   ✅ 获取到 {len(categories)} 个分类: {', '.join(categories)}")
        
        # 检查是否包含中国指数
        has_chinese_indices = '中国指数' in categories
        print(f"   {'✅' if has_chinese_indices else '❌'} 中国指数分类: {'包含' if has_chinese_indices else '缺失'}")
        
        return has_chinese_indices
        
    except Exception as e:
        print(f"   ❌ 测试分类API异常: {e}")
        return False

def main():
    """主测试函数"""
    print("="*60)
    print("🚀 Web API中国指数修复验证")
    print("="*60)
    
    results = []
    
    # 测试1: 分析API
    try:
        analysis_ok = test_analysis_api()
        results.append(("分析API", analysis_ok))
    except Exception as e:
        print(f"❌ 分析API测试失败: {e}")
        results.append(("分析API", False))
    
    # 测试2: 结果API
    try:
        results_ok = test_results_api()
        results.append(("结果API", results_ok))
    except Exception as e:
        print(f"❌ 结果API测试失败: {e}")
        results.append(("结果API", False))
    
    # 测试3: 分类API
    try:
        categories_ok = test_categories_api()
        results.append(("分类API", categories_ok))
    except Exception as e:
        print(f"❌ 分类API测试失败: {e}")
        results.append(("分类API", False))
    
    # 汇总结果
    print("\n" + "="*60)
    print("📊 Web API测试结果汇总:")
    print("="*60)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not passed:
            all_passed = False
    
    print(f"\n🎯 总体结果: {'✅ 全部通过' if all_passed else '❌ 存在失败'}")
    
    if all_passed:
        print("\n🎉 Web API中国指数修复成功！")
        print("   现在可以通过 http://127.0.0.1:50505 查看包含中国指数的完整分析结果。")
    else:
        print("\n⚠️  还有问题需要解决，请检查失败的测试项目。")

if __name__ == "__main__":
    main()