#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Step 2.3: 计算结果对比验证
保存修改前结果并对比修改后结果100%一致
"""

import sys
import os
import json
import pandas as pd
import numpy as np
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))

def save_pre_modification_baseline():
    """保存修改前的结果作为基准（使用Step 1的基准）"""
    print("=== 保存修改前基准结果 ===")
    
    # Step 1已经生成了baseline_scores.json，作为修改前的基准
    baseline_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'baseline_scores.json')
    
    if os.path.exists(baseline_path):
        with open(baseline_path, 'r', encoding='utf-8') as f:
            baseline_data = json.load(f)
        
        print(f"✅ 加载Step 1基准数据: {len(baseline_data['scores'])} 个指标")
        
        # 保存为修改前基准
        pre_mod_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'pre_modification_baseline.json')
        with open(pre_mod_path, 'w', encoding='utf-8') as f:
            json.dump(baseline_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 保存修改前基准到: {pre_mod_path}")
        return baseline_data
    else:
        print(f"❌ 基准文件不存在: {baseline_path}")
        return None

def generate_post_modification_results():
    """生成修改后的结果（使用适配器）"""
    print("\n=== 生成修改后结果 ===")
    
    try:
        from core.scoring_units.mcsi_adapter import MCSIAdapter
    except ImportError as e:
        print(f"❌ 导入适配器失败: {e}")
        return None
    
    # 加载相同的测试数据
    csv_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'stock_data', 'cnindex_000001_上证指数.csv')
    df = pd.read_csv(csv_path)
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df = df.sort_values('timestamp').reset_index(drop=True)
    
    # 使用与基准生成相同的200个数据点
    if len(df) > 200:
        df = df.tail(200).reset_index(drop=True)
    
    # 重命名列以匹配期望格式
    df = df.rename(columns={'timestamp': 'date'})
    
    print(f"测试数据: {len(df)} 行")
    
    post_mod_results = {
        'timestamp': datetime.now().isoformat(),
        'modification': 'adapter_integration',
        'test_data_points': len(df),
        'scores': {}
    }
    
    # 1. MCSI MACD修改后结果
    print("计算修改后 MCSI MACD...")
    try:
        macd_unit = MCSIAdapter.create_mcsi_macd_unit()
        macd_scores = []
        
        window_size = 100
        for i in range(window_size, len(df)):
            window_data = df.iloc[max(0, i-window_size):i+1]
            result = macd_unit.calculate_score(window_data)
            macd_scores.append(result.score)
        
        post_mod_results['scores']['mcsi_macd'] = {
            'values': macd_scores,
            'count': len(macd_scores),
            'stats': {
                'mean': float(np.mean(macd_scores)),
                'std': float(np.std(macd_scores)),
                'min': float(np.min(macd_scores)),
                'max': float(np.max(macd_scores))
            }
        }
        print(f"  完成: {len(macd_scores)} 个分数点, 均值: {np.mean(macd_scores):.2f}")
        
    except Exception as e:
        print(f"  ❌ MACD计算失败: {e}")
        post_mod_results['scores']['mcsi_macd'] = {'error': str(e)}
    
    # 2. MCSI MMT修改后结果
    print("计算修改后 MCSI MMT...")
    try:
        mmt_unit = MCSIAdapter.create_mcsi_mmt_unit()
        mmt_scores = []
        
        window_size = 100
        for i in range(window_size, len(df)):
            window_data = df.iloc[max(0, i-window_size):i+1]
            result = mmt_unit.calculate_score(window_data)
            mmt_scores.append(result.score)
        
        post_mod_results['scores']['mcsi_mmt'] = {
            'values': mmt_scores,
            'count': len(mmt_scores),
            'stats': {
                'mean': float(np.mean(mmt_scores)),
                'std': float(np.std(mmt_scores)),
                'min': float(np.min(mmt_scores)),
                'max': float(np.max(mmt_scores))
            }
        }
        print(f"  完成: {len(mmt_scores)} 个分数点, 均值: {np.mean(mmt_scores):.2f}")
        
    except Exception as e:
        print(f"  ❌ MMT计算失败: {e}")
        post_mod_results['scores']['mcsi_mmt'] = {'error': str(e)}
    
    # 3. MCSI RSI修改后结果
    print("计算修改后 MCSI RSI...")
    try:
        rsi_unit = MCSIAdapter.create_mcsi_rsi_unit()
        rsi_scores = []
        
        window_size = 100
        for i in range(window_size, len(df)):
            window_data = df.iloc[max(0, i-window_size):i+1]
            result = rsi_unit.calculate_score(window_data)
            rsi_scores.append(result.score)
        
        post_mod_results['scores']['mcsi_rsi'] = {
            'values': rsi_scores,
            'count': len(rsi_scores),
            'stats': {
                'mean': float(np.mean(rsi_scores)),
                'std': float(np.std(rsi_scores)),
                'min': float(np.min(rsi_scores)),
                'max': float(np.max(rsi_scores))
            }
        }
        print(f"  完成: {len(rsi_scores)} 个分数点, 均值: {np.mean(rsi_scores):.2f}")
        
    except Exception as e:
        print(f"  ❌ RSI计算失败: {e}")
        post_mod_results['scores']['mcsi_rsi'] = {'error': str(e)}
    
    # 4. MCSI TTM修改后结果
    print("计算修改后 MCSI TTM...")
    try:
        ttm_unit = MCSIAdapter.create_mcsi_ttm_unit()
        ttm_scores = []
        
        window_size = 100
        for i in range(window_size, len(df)):
            window_data = df.iloc[max(0, i-window_size):i+1]
            result = ttm_unit.calculate_score(window_data)
            ttm_scores.append(result.score)
        
        post_mod_results['scores']['mcsi_ttm'] = {
            'values': ttm_scores,
            'count': len(ttm_scores),
            'stats': {
                'mean': float(np.mean(ttm_scores)),
                'std': float(np.std(ttm_scores)),
                'min': float(np.min(ttm_scores)),
                'max': float(np.max(ttm_scores))
            }
        }
        print(f"  完成: {len(ttm_scores)} 个分数点, 均值: {np.mean(ttm_scores):.2f}")
        
    except Exception as e:
        print(f"  ❌ TTM计算失败: {e}")
        post_mod_results['scores']['mcsi_ttm'] = {'error': str(e)}
    
    # 保存修改后结果
    post_mod_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'post_modification_results.json')
    with open(post_mod_path, 'w', encoding='utf-8') as f:
        json.dump(post_mod_results, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 修改后结果保存到: {post_mod_path}")
    return post_mod_results

def compare_results(baseline_data, post_mod_data):
    """对比修改前后的结果"""
    print("\n=== 对比修改前后结果 ===")
    
    if not baseline_data or not post_mod_data:
        print("❌ 缺少对比数据")
        return
    
    comparison_results = {
        'timestamp': datetime.now().isoformat(),
        'comparison_summary': {},
        'detailed_comparison': {}
    }
    
    total_consistent = 0
    total_indicators = 0
    
    for indicator in ['mcsi_macd', 'mcsi_mmt', 'mcsi_rsi', 'mcsi_ttm']:
        if indicator not in baseline_data['scores'] or indicator not in post_mod_data['scores']:
            print(f"⚠️  {indicator}: 缺少数据")
            continue
        
        baseline_scores = baseline_data['scores'][indicator]
        post_mod_scores = post_mod_data['scores'][indicator]
        
        if 'error' in baseline_scores or 'error' in post_mod_scores:
            print(f"❌ {indicator}: 存在计算错误")
            continue
        
        baseline_values = baseline_scores['values']
        post_mod_values = post_mod_scores['values']
        
        if len(baseline_values) != len(post_mod_values):
            print(f"❌ {indicator}: 分数序列长度不匹配")
            continue
        
        # 计算差异
        differences = np.array(baseline_values) - np.array(post_mod_values)
        max_diff = np.max(np.abs(differences))
        mean_diff = np.mean(np.abs(differences))
        
        # 一致性检查（tolerance=1e-10）
        tolerance = 1e-10
        consistent = max_diff <= tolerance
        match_count = np.sum(np.abs(differences) <= tolerance)
        match_rate = match_count / len(differences) * 100
        
        total_indicators += 1
        if consistent:
            total_consistent += 1
        
        comparison_results['detailed_comparison'][indicator] = {
            'consistent': consistent,
            'max_difference': float(max_diff),
            'mean_difference': float(mean_diff),
            'match_rate': float(match_rate),
            'tolerance': tolerance
        }
        
        if consistent:
            print(f"✅ {indicator}: 100%一致 (最大差异: {max_diff:.2e})")
        else:
            print(f"❌ {indicator}: 不一致 (最大差异: {max_diff:.2f}, 匹配率: {match_rate:.1f}%)")
    
    # 总体一致性
    if total_indicators > 0:
        overall_consistency = total_consistent / total_indicators * 100
        comparison_results['comparison_summary'] = {
            'total_indicators': total_indicators,
            'consistent_indicators': total_consistent,
            'overall_consistency_rate': float(overall_consistency)
        }
        
        print(f"\n总体一致性: {total_consistent}/{total_indicators} ({overall_consistency:.1f}%)")
        
        if overall_consistency == 100:
            print("🎉 所有指标100%一致！修改未影响计算结果。")
        else:
            print("⚠️  存在不一致，需要检查修改的影响。")
    
    # 保存对比结果  
    def json_serializer(obj):
        """自定义JSON序列化器"""
        if isinstance(obj, np.bool_):
            return bool(obj)
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            if np.isnan(obj):
                return 0.0
            return float(obj)
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
    
    comparison_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'baseline_comparison_report.json')
    with open(comparison_path, 'w', encoding='utf-8') as f:
        json.dump(comparison_results, f, indent=2, ensure_ascii=False, default=json_serializer)
    
    print(f"\n=== 对比报告已保存到: {comparison_path} ===")
    return comparison_results

def main():
    """主函数"""
    print("=== Step 2.3: 计算结果对比验证 ===")
    
    # 1. 保存修改前基准
    baseline_data = save_pre_modification_baseline()
    
    # 2. 生成修改后结果
    post_mod_data = generate_post_modification_results()
    
    # 3. 对比结果
    compare_results(baseline_data, post_mod_data)

if __name__ == "__main__":
    main()