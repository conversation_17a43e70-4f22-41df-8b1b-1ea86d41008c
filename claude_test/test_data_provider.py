#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DataProvider是否支持adapter指定数据源
Step 2.2 验证
"""

import sys
import os
import pandas as pd

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))

try:
    from core.scoring_units.data_provider import DataProvider
except ImportError as e:
    print(f"导入DataProvider失败: {e}")
    sys.exit(1)

def test_data_provider():
    """测试DataProvider是否支持adapter指定数据源"""
    print("=== 测试DataProvider数据源指定功能 ===")
    
    # 初始化DataProvider
    provider = DataProvider(cache_enabled=False)  # 禁用缓存以便测试
    
    # 1. 测试CSV数据源
    print("\n=== 测试CSV数据源 ===")
    csv_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'stock_data', 'cnindex_000001_上证指数.csv')
    
    if os.path.exists(csv_path):
        ohlc_df = pd.read_csv(csv_path).tail(100)
        
        # adapter指定CSV数据源
        df_csv = provider.get_stock_data(
            db_conn=None,  # 不指定数据库
            symbol=None,
            ohlc=ohlc_df,  # 指定CSV数据
            period='daily'
        )
        
        print(f"CSV数据源结果: {len(df_csv)} 行")
        if not df_csv.empty:
            print(f"数据列: {list(df_csv.columns)}")
            print(f"数据范围: {df_csv['close'].min():.2f} - {df_csv['close'].max():.2f}")
            print("✅ CSV数据源工作正常")
        else:
            print("❌ CSV数据源返回空数据")
    else:
        print(f"❌ CSV文件不存在: {csv_path}")
    
    # 2. 测试数据库数据源（如果可用）
    print("\n=== 测试数据库数据源 ===")
    try:
        # adapter指定数据库数据源
        db_conn = '************************************************/fintech_db'
        df_db = provider.get_stock_data(
            db_conn=db_conn,
            symbol='cnindex_000001_上证指数',
            period='daily'
        )
        
        print(f"数据库数据源结果: {len(df_db)} 行")
        if not df_db.empty:
            print(f"数据列: {list(df_db.columns)}")
            print(f"数据范围: {df_db['close'].min():.2f} - {df_db['close'].max():.2f}")
            print("✅ 数据库数据源工作正常")
        else:
            print("⚠️  数据库数据源返回空数据")
            
    except Exception as e:
        print(f"⚠️  数据库连接失败: {e}")
        print("这是正常的，如果数据库服务不可用")
    
    # 3. 测试周线数据支持
    print("\n=== 测试周线数据支持 ===")
    try:
        db_conn = '************************************************/fintech_db'
        df_weekly = provider.get_stock_data(
            db_conn=db_conn,
            symbol='cnindex_000001_上证指数',
            period='weekly'  # 测试周线数据
        )
        
        print(f"周线数据结果: {len(df_weekly)} 行")
        if not df_weekly.empty:
            print("✅ 周线数据支持正常")
        else:
            print("⚠️  周线数据为空")
            
    except Exception as e:
        print(f"⚠️  周线数据获取失败: {e}")
    
    # 4. 测试adapter指定数据源的逻辑
    print("\n=== 测试adapter数据源指定逻辑 ===")
    
    # 场景1：adapter指定数据库优先
    data_config_db = {
        'db_conn': '************************************************/fintech_db',
        'symbol': 'cnindex_000001_上证指数',
        'period': 'daily'
    }
    
    try:
        df_adapter_db = provider.get_stock_data(**data_config_db)
        print(f"adapter指定数据库: {len(df_adapter_db)} 行")
        if not df_adapter_db.empty:
            print("✅ adapter数据库指定工作正常")
    except Exception as e:
        print(f"⚠️  adapter数据库指定失败: {e}")
    
    # 场景2：adapter指定CSV备用
    data_config_csv = {
        'db_conn': None,
        'symbol': None,
        'ohlc': ohlc_df if 'ohlc_df' in locals() else None,
        'period': 'daily'
    }
    
    if data_config_csv['ohlc'] is not None:
        df_adapter_csv = provider.get_stock_data(**data_config_csv)
        print(f"adapter指定CSV: {len(df_adapter_csv)} 行")
        if not df_adapter_csv.empty:
            print("✅ adapter CSV指定工作正常")
    
    print("\n=== DataProvider测试完成 ===")

if __name__ == "__main__":
    test_data_provider()