#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试适配器与不同数据源的集成
Step 2.2 验证（绕过DataProvider的bug）
"""

import sys
import os
import pandas as pd

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))

try:
    from core.scoring_units.mcsi_adapter import MCSIAdapter
except ImportError as e:
    print(f"导入适配器失败: {e}")
    sys.exit(1)

def test_adapter_with_data_sources():
    """测试适配器与不同数据源的集成"""
    print("=== 测试适配器与数据源集成 ===")
    
    # 1. 准备CSV数据源
    csv_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'stock_data', 'cnindex_000001_上证指数.csv')
    df_csv = pd.read_csv(csv_path).tail(150)
    df_csv = df_csv.rename(columns={'timestamp': 'date'})
    df_csv['date'] = pd.to_datetime(df_csv['date'])
    
    print(f"CSV数据准备: {len(df_csv)} 行")
    
    # 2. 测试适配器直接使用DataFrame数据源
    print("\n=== 测试适配器直接使用DataFrame ===")
    
    try:
        # MCSI MACD
        macd_unit = MCSIAdapter.create_mcsi_macd_unit()
        result_macd = macd_unit.calculate_score(df_csv)
        print(f"MACD分数: {result_macd.score:.2f}, 数据源: {result_macd.metadata.get('data_source', 'unknown')}")
        
        # MCSI MMT
        mmt_unit = MCSIAdapter.create_mcsi_mmt_unit()
        result_mmt = mmt_unit.calculate_score(df_csv)
        print(f"MMT分数: {result_mmt.score:.2f}, 数据源: {result_mmt.metadata.get('data_source', 'unknown')}")
        
        # MCSI RSI
        rsi_unit = MCSIAdapter.create_mcsi_rsi_unit()
        result_rsi = rsi_unit.calculate_score(df_csv)
        print(f"RSI分数: {result_rsi.score:.2f}, 数据源: {result_rsi.metadata.get('data_source', 'unknown')}")
        
        # MCSI TTM
        ttm_unit = MCSIAdapter.create_mcsi_ttm_unit()
        result_ttm = ttm_unit.calculate_score(df_csv)
        print(f"TTM分数: {result_ttm.score:.2f}, 数据源: {result_ttm.metadata.get('data_source', 'unknown')}")
        
        print("✅ 适配器DataFrame数据源工作正常")
        
    except Exception as e:
        print(f"❌ 适配器DataFrame测试失败: {e}")
    
    # 3. 测试适配器使用字典数据源（模拟数据库配置）
    print("\n=== 测试适配器使用字典数据源 ===")
    
    # 数据库配置（adapter指定）
    db_config = {
        'db_conn': '************************************************/fintech_db',
        'symbol': 'cnindex_000001_上证指数',
        'period': 'daily'
    }
    
    try:
        # 测试数据库数据源
        macd_unit = MCSIAdapter.create_mcsi_macd_unit()
        result_macd_db = macd_unit.calculate_score(db_config)
        
        print(f"数据库MACD分数: {result_macd_db.score:.2f}")
        print(f"数据源: {result_macd_db.metadata.get('data_source', 'unknown')}")
        print(f"处理行数: {result_macd_db.metadata.get('rows_processed', 0)}")
        
        if result_macd_db.metadata.get('rows_processed', 0) > 0:
            print("✅ 适配器数据库数据源工作正常")
        else:
            print("⚠️  数据库返回空数据，可能服务不可用")
        
    except Exception as e:
        print(f"⚠️  数据库测试失败: {e}")
        print("这是正常的，如果数据库服务不可用")
    
    # 4. 测试数据源指定的灵活性
    print("\n=== 测试数据源指定灵活性 ===")
    
    # CSV备用配置（adapter指定）
    csv_config = {
        'db_conn': None,  # 不使用数据库
        'symbol': None,
        'ohlc': df_csv,  # 指定使用CSV数据
        'period': 'daily'
    }
    
    try:
        macd_unit = MCSIAdapter.create_mcsi_macd_unit()
        result_macd_csv = macd_unit.calculate_score(csv_config)
        
        print(f"CSV配置MACD分数: {result_macd_csv.score:.2f}")
        print(f"数据源: {result_macd_csv.metadata.get('data_source', 'unknown')}")
        
        # 比较直接DataFrame和通过配置的结果
        if abs(result_macd.score - result_macd_csv.score) < 0.01:
            print("✅ 直接DataFrame和配置方式结果一致")
        else:
            print(f"⚠️  结果差异: 直接={result_macd.score:.2f}, 配置={result_macd_csv.score:.2f}")
        
    except Exception as e:
        print(f"❌ CSV配置测试失败: {e}")
    
    print("\n=== 适配器数据源集成测试完成 ===")

if __name__ == "__main__":
    test_adapter_with_data_sources()