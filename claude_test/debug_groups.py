#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试分组问题
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

import logging
from core.composite.scorer import NewCompositeScorer
from core.data.hybrid_data_loader import HybridDataLoader

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s: %(message)s')

def debug_groups():
    """调试分组问题"""
    print("🔍 调试分组初始化问题")
    print("=" * 50)
    
    # 1. 测试数据加载器
    print("1. 测试混合数据加载器...")
    try:
        hybrid_loader = HybridDataLoader()
        stock_list = hybrid_loader.get_stock_list()
        print(f"   ✅ 获取到 {len(stock_list)} 只股票")
        
        # 检查中国指数
        chinese_stocks = [s for s in stock_list if any(keyword in str(s).lower() 
                        for keyword in ['cnindex', '上证', '深证', '创业板', '沪深'])]
        print(f"   🇨🇳 中国指数: {len(chinese_stocks)} 个")
        for stock in chinese_stocks:
            print(f"     - {stock.get('symbol', '')}: {stock.get('name', '')}")
            
    except Exception as e:
        print(f"   ❌ 混合数据加载器失败: {e}")
        return
    
    # 2. 测试评分器初始化
    print(f"\n2. 测试评分器初始化...")
    try:
        scorer = NewCompositeScorer()
        print("   ✅ 评分器初始化成功")
        
        # 检查分组
        groups = scorer.list_groups()
        print(f"   分组数量: {len(groups)}")
        for group in groups:
            print(f"     - {group}")
            
        # 检查分组管理器
        if hasattr(scorer, 'group_manager'):
            print(f"   分组管理器: {type(scorer.group_manager)}")
            if hasattr(scorer.group_manager, 'groups'):
                print(f"   管理器中的分组: {list(scorer.group_manager.groups.keys())}")
                for group_name, group_obj in scorer.group_manager.groups.items():
                    print(f"     {group_name}: {type(group_obj)}")
                    if hasattr(group_obj, 'stocks'):
                        print(f"       股票数量: {len(group_obj.stocks) if group_obj.stocks else 0}")
                    if hasattr(group_obj, '_stocks'):
                        print(f"       _stocks数量: {len(group_obj._stocks) if group_obj._stocks else 0}")
        
    except Exception as e:
        print(f"   ❌ 评分器初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 3. 测试手动设置股票数据
    print(f"\n3. 测试手动设置股票数据...")
    try:
        # 尝试手动设置股票列表到分组
        for group_name, group_obj in scorer.group_manager.groups.items():
            if hasattr(group_obj, 'set_stocks'):
                print(f"   为 {group_name} 设置股票...")
                group_obj.set_stocks(stock_list)
                print(f"     设置完成，股票数量: {len(group_obj.stocks) if hasattr(group_obj, 'stocks') and group_obj.stocks else 0}")
            elif hasattr(group_obj, '_stocks'):
                print(f"   为 {group_name} 直接设置 _stocks...")
                group_obj._stocks = stock_list
                print(f"     设置完成，_stocks数量: {len(group_obj._stocks)}")
        
        # 重新检查分组
        print(f"\n   重新检查分组...")
        groups = scorer.list_groups()
        print(f"   分组数量: {len(groups)}")
        
        # 如果还是有问题，查看分组的具体结构
        if isinstance(groups, dict):
            for group_name, group_info in groups.items():
                print(f"     {group_name}: {type(group_info)}")
                if isinstance(group_info, dict):
                    stocks = group_info.get('stocks', [])
                    print(f"       stocks: {len(stocks)} 个")
                    if stocks:
                        for stock in stocks[:2]:
                            print(f"         - {stock}")
                        
    except Exception as e:
        print(f"   ❌ 设置股票数据失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("调试完成!")

if __name__ == "__main__":
    debug_groups()