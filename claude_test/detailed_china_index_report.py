#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的中国指数数据库分析报告
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import pandas as pd
from datetime import datetime

# 数据库连接配置
DB_CONFIG = {
    'host': '***********',
    'port': 5433,
    'database': 'fintech_db',
    'user': 'postgres',
    'password': 'robot2025'
}

def analyze_table_quality(cursor, table_name):
    """分析表数据质量"""
    full_name = f'public."{table_name}"'
    
    try:
        # 获取数据时间范围
        cursor.execute(f"""
            SELECT 
                MIN(timestamp) as start_date,
                MAX(timestamp) as end_date,
                COUNT(*) as total_rows
            FROM {full_name}
        """)
        date_info = cursor.fetchone()
        
        # 检查数据完整性
        cursor.execute(f"""
            SELECT 
                COUNT(*) as total_rows,
                COUNT(CASE WHEN open IS NULL THEN 1 END) as null_open,
                COUNT(CASE WHEN high IS NULL THEN 1 END) as null_high,
                COUNT(CASE WHEN low IS NULL THEN 1 END) as null_low,
                COUNT(CASE WHEN close IS NULL THEN 1 END) as null_close,
                COUNT(CASE WHEN volume IS NULL THEN 1 END) as null_volume
            FROM {full_name}
        """)
        quality_info = cursor.fetchone()
        
        # 获取最新几条记录
        cursor.execute(f"""
            SELECT timestamp, open, high, low, close, volume
            FROM {full_name}
            ORDER BY timestamp DESC
            LIMIT 5
        """)
        latest_data = cursor.fetchall()
        
        return {
            'date_info': date_info,
            'quality_info': quality_info,
            'latest_data': latest_data
        }
    except Exception as e:
        print(f"⚠️ 分析 {table_name} 时出错: {e}")
        return None

def main():
    print("📊 中国指数数据详细分析报告")
    print("=" * 60)
    print(f"📅 报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # 中国指数相关表
        tables = [
            'cnindex_000001_上证指数',
            'cnindex_399006_创业板指', 
            'cnindex_399300_沪深300',
            'hkstocks_01129_中国水业集团'
        ]
        
        print(f"\n🎯 检查 {len(tables)} 个中国指数相关表:")
        for i, table in enumerate(tables, 1):
            print(f"  {i}. {table}")
        
        print("\n" + "="*80)
        
        for i, table_name in enumerate(tables, 1):
            print(f"\n【{i}/{len(tables)}】 {table_name}")
            print("-" * 60)
            
            # 分析表质量
            analysis = analyze_table_quality(cursor, table_name)
            if not analysis:
                continue
                
            date_info = analysis['date_info']
            quality_info = analysis['quality_info']
            latest_data = analysis['latest_data']
            
            # 输出基本信息
            print(f"📈 数据量: {date_info['total_rows']:,} 条记录")
            print(f"📅 时间范围: {date_info['start_date'].strftime('%Y-%m-%d')} 至 {date_info['end_date'].strftime('%Y-%m-%d')}")
            
            # 计算数据跨度
            time_span = (date_info['end_date'] - date_info['start_date']).days
            print(f"📊 数据跨度: {time_span} 天 ({time_span/365.25:.1f} 年)")
            
            # 数据质量分析
            total = quality_info['total_rows']
            print(f"\n🔍 数据完整性分析:")
            print(f"  - Open价格: {total - quality_info['null_open']:,}/{total:,} ({(total - quality_info['null_open'])/total*100:.1f}%)")
            print(f"  - High价格: {total - quality_info['null_high']:,}/{total:,} ({(total - quality_info['null_high'])/total*100:.1f}%)")
            print(f"  - Low价格: {total - quality_info['null_low']:,}/{total:,} ({(total - quality_info['null_low'])/total*100:.1f}%)")
            print(f"  - Close价格: {total - quality_info['null_close']:,}/{total:,} ({(total - quality_info['null_close'])/total*100:.1f}%)")
            print(f"  - 成交量: {total - quality_info['null_volume']:,}/{total:,} ({(total - quality_info['null_volume'])/total*100:.1f}%)")
            
            # 最新数据
            print(f"\n📋 最新5条记录:")
            if latest_data:
                df = pd.DataFrame(latest_data)
                df['timestamp'] = df['timestamp'].dt.strftime('%Y-%m-%d')
                pd.set_option('display.float_format', '{:.3f}'.format)
                print(df.to_string(index=False))
            
            print("\n" + "="*80)
        
        # 汇总统计
        print(f"\n📈 汇总统计")
        print("-" * 40)
        
        cursor.execute("""
            SELECT 
                '上证指数' as index_name,
                COUNT(*) as records,
                MIN(timestamp) as start_date,
                MAX(timestamp) as end_date
            FROM public.cnindex_000001_上证指数
            UNION ALL
            SELECT 
                '创业板指' as index_name,
                COUNT(*) as records,
                MIN(timestamp) as start_date,
                MAX(timestamp) as end_date
            FROM public.cnindex_399006_创业板指
            UNION ALL
            SELECT 
                '沪深300' as index_name,
                COUNT(*) as records,
                MIN(timestamp) as start_date,
                MAX(timestamp) as end_date
            FROM public.cnindex_399300_沪深300
            UNION ALL
            SELECT 
                '中国水业集团' as index_name,
                COUNT(*) as records,
                MIN(timestamp) as start_date,
                MAX(timestamp) as end_date
            FROM public.hkstocks_01129_中国水业集团
            ORDER BY records DESC;
        """)
        
        summary = cursor.fetchall()
        
        total_records = sum(row['records'] for row in summary)
        print(f"\n🎯 总数据量: {total_records:,} 条记录")
        print(f"📊 平均每个指数: {total_records/len(summary):,.0f} 条记录")
        print(f"\n各指数详情:")
        
        for row in summary:
            span_days = (row['end_date'] - row['start_date']).days
            avg_records_per_year = row['records'] / (span_days / 365.25) if span_days > 0 else 0
            print(f"  • {row['index_name']}: {row['records']:,} 条 "
                  f"({row['start_date'].strftime('%Y-%m-%d')} ~ {row['end_date'].strftime('%Y-%m-%d')}, "
                  f"年均 {avg_records_per_year:.0f} 条)")
        
        # 数据建议
        print(f"\n💡 数据质量评估与建议:")
        print(f"✅ 数据覆盖完整，包含主要中国股指和港股代表")
        print(f"✅ 时间跨度较长，适合长期趋势分析")  
        print(f"✅ 数据结构统一，便于批量处理")
        
        # 检查数据更新频率
        cursor.execute("""
            SELECT 
                MAX(timestamp) as latest_date,
                MAX(created_at) as latest_update
            FROM (
                SELECT timestamp, created_at FROM public.cnindex_000001_上证指数
                UNION ALL
                SELECT timestamp, created_at FROM public.cnindex_399006_创业板指
                UNION ALL  
                SELECT timestamp, created_at FROM public.cnindex_399300_沪深300
                UNION ALL
                SELECT timestamp, created_at FROM public.hkstocks_01129_中国水业集团
            ) all_data;
        """)
        
        update_info = cursor.fetchone()
        if update_info:
            days_since_update = (datetime.now() - update_info['latest_date'].replace(tzinfo=None)).days
            print(f"\n🔄 数据更新状态:")
            print(f"  - 最新数据日期: {update_info['latest_date'].strftime('%Y-%m-%d')}")
            print(f"  - 最后更新时间: {update_info['latest_update'].strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"  - 距今天数: {days_since_update} 天")
            
            if days_since_update > 7:
                print(f"⚠️ 建议更新数据，距离最新数据已超过1周")
        
        cursor.close()
        conn.close()
        
        print(f"\n✅ 分析报告生成完成!")
        
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()