#!/bin/bash
# 使用curl测试Web应用中国指数分析功能

echo "🔍 测试Web应用中国指数分析功能"
echo "=================================================="

BASE_URL="http://127.0.0.1:50505"

# 1. 测试基础连接
echo "1. 测试基础连接..."
STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/")
echo "   主页响应状态: $STATUS"

# 2. 测试分组API
echo -e "\n2. 测试分组API..."
GROUPS_RESPONSE=$(curl -s "$BASE_URL/api/groups")
if [ $? -eq 0 ]; then
    echo "   ✅ 分组API响应成功"
    # 检查是否包含中国指数
    if echo "$GROUPS_RESPONSE" | grep -q "cnindex\|上证\|深证\|创业板\|沪深"; then
        echo "   🇨🇳 发现中国指数数据!"
        echo "$GROUPS_RESPONSE" | grep -o '"cnindex[^"]*"' | head -3 | sed 's/^/     - /'
    else
        echo "   ⚠️  分组中未发现中国指数"
    fi
else
    echo "   ❌ 分组API请求失败"
fi

# 3. 测试分析API
echo -e "\n3. 测试中国指数分析..."
PAYLOAD='{"selected_groups": ["trend_group", "oscillation_group"], "data_limit": 250, "custom_symbols": ["cnindex_000001_上证指数"]}'

echo "   分析上证指数..."
ANALYSIS_RESPONSE=$(curl -s -X POST "$BASE_URL/api/analyze" \
  -H "Content-Type: application/json" \
  -d "$PAYLOAD")

if [ $? -eq 0 ]; then
    echo "   ✅ 分析API响应成功"
    
    # 检查是否有成功结果
    if echo "$ANALYSIS_RESPONSE" | grep -q '"success_count"'; then
        SUCCESS_COUNT=$(echo "$ANALYSIS_RESPONSE" | sed -n 's/.*"success_count":\s*\([0-9]*\).*/\1/p')
        ERROR_COUNT=$(echo "$ANALYSIS_RESPONSE" | sed -n 's/.*"error_count":\s*\([0-9]*\).*/\1/p')
        echo "     成功: $SUCCESS_COUNT, 失败: $ERROR_COUNT"
    fi
    
    # 检查是否有错误信息
    if echo "$ANALYSIS_RESPONSE" | grep -q '"error"'; then
        echo "     ⚠️ 分析中包含错误信息"
        echo "$ANALYSIS_RESPONSE" | sed -n 's/.*"error":\s*"\([^"]*\)".*/       \1/p' | head -1
    fi
else
    echo "   ❌ 分析API请求失败"
fi

# 4. 检查分析结果
echo -e "\n4. 检查分析结果..."
sleep 2  # 等待分析完成

RESULTS_RESPONSE=$(curl -s "$BASE_URL/api/results")
if [ $? -eq 0 ]; then
    echo "   ✅ 结果API响应成功"
    
    # 检查中国指数结果
    if echo "$RESULTS_RESPONSE" | grep -q "cnindex\|上证\|深证\|创业板\|沪深"; then
        echo "   🇨🇳 找到中国指数分析结果!"
        CHINESE_COUNT=$(echo "$RESULTS_RESPONSE" | grep -o '"cnindex[^"]*"' | wc -l)
        echo "     中国指数结果数量: $CHINESE_COUNT"
        
        # 显示一些具体结果
        echo "     示例结果:"
        echo "$RESULTS_RESPONSE" | grep -o '"cnindex[^"]*":[^}]*}' | head -2 | sed 's/^/       /'
    else
        echo "   ❌ 未找到中国指数分析结果"
        echo "     总结果数量: $(echo "$RESULTS_RESPONSE" | grep -o '"[^"]*":' | wc -l)"
    fi
else
    echo "   ❌ 结果API请求失败"
fi

echo -e "\n=================================================="
echo "测试完成!"