#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国指数数据库检查脚本
检查PostgreSQL数据库中的中国指数相关数据
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import pandas as pd
import sys
import re

# 数据库连接配置
DB_CONFIG = {
    'host': '***********',
    'port': 5433,
    'database': 'fintech_db',
    'user': 'postgres',
    'password': 'robot2025'
}

def connect_database():
    """连接PostgreSQL数据库"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except psycopg2.Error as e:
        print(f"数据库连接失败: {e}")
        return None

def find_chinese_index_tables(conn):
    """查找所有包含中国指数相关的表名"""
    print("=== 1. 查找中国指数相关表名 ===")
    
    # 定义搜索关键词
    keywords = ['cnindex', '中国', '上证', '深证', 'sse', 'szse', 'csi', 'index', 'idx', 'china', 'cn']
    
    cursor = conn.cursor(cursor_factory=RealDictCursor)
    
    # 查询所有表名
    cursor.execute("""
        SELECT table_name, table_schema
        FROM information_schema.tables 
        WHERE table_type = 'BASE TABLE' 
        AND table_schema NOT IN ('information_schema', 'pg_catalog')
        ORDER BY table_schema, table_name;
    """)
    
    all_tables = cursor.fetchall()
    
    # 筛选包含关键词的表
    chinese_tables = []
    
    print(f"\n总共找到 {len(all_tables)} 个表，正在筛选包含中国指数关键词的表...")
    print("\n匹配的表名:")
    print("-" * 60)
    
    for table in all_tables:
        table_name = table['table_name'].lower()
        table_schema = table['table_schema']
        
        # 检查是否包含任何关键词
        matched_keywords = []
        for keyword in keywords:
            if keyword.lower() in table_name:
                matched_keywords.append(keyword)
        
        if matched_keywords:
            chinese_tables.append(table)
            print(f"📊 {table_schema}.{table['table_name']} (匹配关键词: {', '.join(matched_keywords)})")
    
    print(f"\n找到 {len(chinese_tables)} 个相关表")
    cursor.close()
    return chinese_tables

def analyze_table_structure(conn, table_info):
    """分析表结构"""
    table_name = table_info['table_name']
    table_schema = table_info['table_schema']
    
    print(f"\n=== 表结构分析: {table_schema}.{table_name} ===")
    
    cursor = conn.cursor(cursor_factory=RealDictCursor)
    
    # 查询表结构
    cursor.execute("""
        SELECT 
            column_name,
            data_type,
            is_nullable,
            column_default,
            character_maximum_length,
            numeric_precision,
            numeric_scale
        FROM information_schema.columns 
        WHERE table_schema = %s AND table_name = %s
        ORDER BY ordinal_position;
    """, (table_schema, table_name))
    
    columns = cursor.fetchall()
    
    print(f"\n字段信息 (共 {len(columns)} 个字段):")
    print("-" * 80)
    print(f"{'字段名':<20} {'数据类型':<15} {'可空':<6} {'长度/精度':<12} {'默认值':<15}")
    print("-" * 80)
    
    for col in columns:
        length_info = ""
        if col['character_maximum_length']:
            length_info = f"({col['character_maximum_length']})"
        elif col['numeric_precision']:
            if col['numeric_scale']:
                length_info = f"({col['numeric_precision']},{col['numeric_scale']})"
            else:
                length_info = f"({col['numeric_precision']})"
        
        default_val = str(col['column_default'])[:14] if col['column_default'] else ""
        
        print(f"{col['column_name']:<20} {col['data_type']:<15} {col['is_nullable']:<6} {length_info:<12} {default_val:<15}")
    
    cursor.close()
    return columns

def check_table_data_count(conn, table_info):
    """检查表数据量"""
    table_name = table_info['table_name']
    table_schema = table_info['table_schema']
    full_table_name = f'"{table_schema}"."{table_name}"'
    
    cursor = conn.cursor()
    
    try:
        # 获取行数
        cursor.execute(f"SELECT COUNT(*) FROM {full_table_name};")
        result = cursor.fetchone()
        row_count = result[0] if result else 0
        
        print(f"\n📈 数据量: {row_count:,} 行")
        
        # 如果有数据，获取日期范围（尝试常见的日期字段名）
        if row_count > 0:
            date_columns = ['date', 'trading_date', 'trade_date', 'timestamp', 'time', 'created_at', 'updated_at']
            
            cursor.execute(f"""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_schema = %s AND table_name = %s 
                AND (data_type LIKE '%date%' OR data_type LIKE '%time%' OR column_name IN %s)
                ORDER BY ordinal_position LIMIT 1;
            """, (table_schema, table_name, tuple(date_columns)))
            
            date_col_result = cursor.fetchone()
            
            if date_col_result:
                date_col = date_col_result[0]
                try:
                    cursor.execute(f"""
                        SELECT MIN("{date_col}") as min_date, MAX("{date_col}") as max_date 
                        FROM {full_table_name} 
                        WHERE "{date_col}" IS NOT NULL;
                    """)
                    date_range = cursor.fetchone()
                    if date_range and date_range[0]:
                        print(f"📅 数据时间范围: {date_range[0]} 至 {date_range[1]}")
                except psycopg2.Error as date_error:
                    print(f"⚠️ 获取日期范围失败: {date_error}")
        
        cursor.close()
        return row_count
        
    except psycopg2.Error as e:
        print(f"❌ 查询数据量失败: {e}")
        if cursor:
            cursor.close()
        return 0

def show_sample_data(conn, table_info, limit=5):
    """显示示例数据"""
    table_name = table_info['table_name']
    table_schema = table_info['table_schema']
    full_table_name = f'"{table_schema}"."{table_name}"'
    
    print(f"\n=== 示例数据 (前 {limit} 行) ===")
    
    cursor = conn.cursor(cursor_factory=RealDictCursor)
    
    try:
        cursor.execute(f"SELECT * FROM {full_table_name} LIMIT %s;", (limit,))
        sample_data = cursor.fetchall()
        
        if not sample_data:
            print("❌ 表中没有数据")
            cursor.close()
            return
        
        # 转换为DataFrame以便更好的显示
        df = pd.DataFrame(sample_data)
        
        print(f"\n数据预览:")
        print("-" * 100)
        
        # 设置pandas显示选项
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', None)
        pd.set_option('display.max_colwidth', 30)
        
        print(df.to_string(index=False))
        
        cursor.close()
        
    except psycopg2.Error as e:
        print(f"❌ 查询示例数据失败: {e}")
        cursor.close()

def main():
    """主函数"""
    print("🔍 开始检查PostgreSQL数据库中的中国指数相关数据")
    print("=" * 60)
    
    # 连接数据库
    conn = connect_database()
    if not conn:
        sys.exit(1)
    
    try:
        # 1. 查找相关表名
        chinese_tables = find_chinese_index_tables(conn)
        
        if not chinese_tables:
            print("\n❌ 未找到包含中国指数关键词的表")
            return
        
        print(f"\n🎯 将详细分析 {len(chinese_tables)} 个相关表:")
        print("=" * 60)
        
        # 2. 逐个分析每个表
        for i, table_info in enumerate(chinese_tables, 1):
            print(f"\n【{i}/{len(chinese_tables)}】正在分析表: {table_info['table_schema']}.{table_info['table_name']}")
            print("=" * 80)
            
            # 分析表结构
            columns = analyze_table_structure(conn, table_info)
            
            # 检查数据量
            row_count = check_table_data_count(conn, table_info)
            
            # 显示示例数据（如果有数据的话）
            if row_count > 0:
                show_sample_data(conn, table_info, limit=3)
            else:
                print("\n📝 表中暂无数据")
            
            print("\n" + "="*80)
        
        print(f"\n✅ 检查完成！共分析了 {len(chinese_tables)} 个相关表")
        
    except Exception as e:
        print(f"❌ 执行过程中发生错误: {e}")
    finally:
        conn.close()
        print("\n🔒 数据库连接已关闭")

if __name__ == "__main__":
    main()