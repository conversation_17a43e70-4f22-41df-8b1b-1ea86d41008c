#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试 MCSI MMT 指标计算问题
"""

import sys
import os
import numpy as np
import pandas as pd

sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'TV-code', 'py-code'))

from mcsi_mmt import MCSIMMTIndicator

def debug_mmt():
    # 加载数据
    csv_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'stock_data', 'cnindex_000001_上证指数.csv')
    df = pd.read_csv(csv_path).tail(150)  # 只取150行用于调试
    
    close = df['close'].values
    high = df['high'].values
    low = df['low'].values
    
    print(f"数据形状: close={len(close)}, high={len(high)}, low={len(low)}")
    print(f"数据范围: close=[{close.min():.2f}, {close.max():.2f}]")
    
    mmt_indicator = MCSIMMTIndicator()
    
    # 使用少量数据测试
    test_close = close[:100]
    test_high = high[:100]
    test_low = low[:100]
    
    print(f"\n测试数据长度: {len(test_close)}")
    
    try:
        result = mmt_indicator.calculate(test_close, test_high, test_low)
        print(f"\n计算结果键: {list(result.keys())}")
        
        for key, value in result.items():
            if isinstance(value, np.ndarray):
                print(f"{key}: array长度={len(value)}, 范围=[{value.min():.2f}, {value.max():.2f}], 最后3个值={value[-3:]}")
            else:
                print(f"{key}: {value}")
                
        # 检查composite_score
        if 'composite_score' in result:
            composite = result['composite_score']
            if isinstance(composite, np.ndarray) and len(composite) > 0:
                non_zero = composite[composite != 0]
                print(f"\ncomposite_score非零值: {len(non_zero)} 个")
                if len(non_zero) > 0:
                    print(f"非零值范围: [{non_zero.min():.2f}, {non_zero.max():.2f}]")
                    print(f"最后10个值: {composite[-10:]}")
                else:
                    print("所有composite_score都是0!")
                    
    except Exception as e:
        print(f"计算错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_mmt()