{"timestamp": "2025-08-20T08:35:19.030275", "modification": "adapter_integration", "test_data_points": 200, "scores": {"mcsi_macd": {"values": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -50.39176173515284, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -52.0245469229289, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 51.306687597109494, 0.0, 0.0, 0.0, 0.0, 63.33801139942251, 63.5619635688762, 63.47710726816068, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -60.24272294301533, -60.467269435533474, -59.9033902020342, -59.85652351467974, -59.63686445220027, -59.82932248584447, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -63.5839282723105, -64.07426361436842, -64.48630773295879], "count": 100, "stats": {"mean": -4.12813131477458, "std": 22.837467673064722, "min": -64.48630773295879, "max": 63.5619635688762}}, "mcsi_mmt": {"values": [-10.0, -40.0, -10.0, -40.0, -50.0, -50.0, -50.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 40.0, 50.0, 50.0, 50.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -50.0, -50.0, 0.0, 0.0, 0.0, 0.0, 10.0, 40.0, 50.0, 50.0, 10.0, 10.0, 40.0, 40.0, 50.0, 50.0, 50.0, -10.0, -10.0, -10.0, -10.0, -10.0, -10.0, -40.0, -75.0, -85.0, -50.0, -50.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -50.0, -50.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 75.0, 85.0, 50.0, 50.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -10.0, -10.0, -40.0, -40.0, -50.0], "count": 100, "stats": {"mean": -1.1, "std": 30.212414666821985, "min": -85.0, "max": 85.0}}, "mcsi_rsi": {"values": [-13.0, -13.0, -13.0, -27.0, -27.0, -13.0, -67.0, -67.0, -67.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 13.0, 13.0, 67.0, 67.0, 67.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 13.0, 13.0, 13.0, 27.0, 13.0, 13.0, 27.0, 13.0, 67.0, 13.0, 13.0, 27.0, 67.0, 67.0, 67.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -13.0, -13.0, -13.0, -13.0, -13.0, -13.0, -27.0, -67.0, -67.0, -67.0, 0.0, 0.0, 0.0, 13.0, 13.0, 13.0, 27.0, 67.0, 67.0, 67.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 13.0, 67.0, 67.0, 67.0, -13.0, -27.0, -27.0, -67.0, -67.0, -67.0, -13.0], "count": 100, "stats": {"mean": 2.67, "std": 33.0151646974538, "min": -67.0, "max": 67.0}}, "mcsi_ttm": {"values": [-0.0, -20.0, -50.0, -100.0, -80.0, -80.0, 0.0, -0.0, -0.0, -0.0, -0.0, 0.0, -0.0, -0.0, 0.0, 0.0, 0.0, 0.0, -0.0, -0.0, -0.0, -0.0, 0.0, 0.0, -0.0, -0.0, -0.0, -0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 20.0, 50.0, 100.0, -0.0, -0.0, -0.0, -0.0, -0.0, -0.0, -20.0, -50.0, -100.0, -80.0, -80.0, 0.0, 0.0, 0.0, -0.0, -0.0, -0.0, -0.0, -0.0, -0.0, -20.0, -50.0, 0.0, 0.0, 0.0, -0.0, -0.0, 0.0, 0.0, 0.0, 0.0, -0.0, -0.0, -0.0, -0.0, -0.0, -0.0, -20.0, -50.0, -100.0, -80.0, 0.0, -0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.0, -0.0, -0.0, -0.0, -0.0, -0.0, -20.0, -50.0, -100.0], "count": 100, "stats": {"mean": -9.8, "std": 29.965980711466795, "min": -100.0, "max": 100.0}}}}