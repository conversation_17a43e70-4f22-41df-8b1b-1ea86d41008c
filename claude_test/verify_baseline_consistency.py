#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证统一接口输出与基准100%一致性
PyArmor迁移项目的关键验证步骤
"""

import sys
import os
import json
import numpy as np
import pandas as pd
from datetime import datetime

# 添加core模块路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))

try:
    from core.scoring_units.mcsi_macd_scoring import MCSIMACDScoringUnit
    from core.scoring_units.mcsi_mmt_scoring import MCSIMMTScoringUnit
    from core.scoring_units.mcsi_rsi_scoring import MCSIRSIScoringUnit
    from core.scoring_units.mcsi_ttm_scoring import MCSITTMScoringUnit
except ImportError as e:
    print(f"导入统一接口失败: {e}")
    sys.exit(1)

def load_baseline_scores():
    """加载基准分数"""
    baseline_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'baseline_scores.json')
    
    with open(baseline_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def load_test_data():
    """加载测试数据"""
    csv_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'stock_data', 'cnindex_000001_上证指数.csv')
    
    df = pd.read_csv(csv_path)
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df = df.sort_values('timestamp').reset_index(drop=True)
    
    # 使用与基准生成相同的200个数据点
    if len(df) > 200:
        df = df.tail(200).reset_index(drop=True)
    
    return df

def compare_scores(baseline_scores, unified_scores, indicator_name, tolerance=1e-10):
    """
    比较分数一致性
    
    Args:
        baseline_scores: 基准分数列表
        unified_scores: 统一接口分数列表  
        indicator_name: 指标名称
        tolerance: 容差范围
    
    Returns:
        dict: 比较结果
    """
    if len(baseline_scores) != len(unified_scores):
        return {
            'consistent': False,
            'error': f'分数序列长度不匹配: 基准{len(baseline_scores)} vs 统一接口{len(unified_scores)}'
        }
    
    # 计算差异
    differences = np.array(baseline_scores) - np.array(unified_scores)
    max_diff = np.max(np.abs(differences))
    mean_diff = np.mean(np.abs(differences))
    
    # 检查一致性
    consistent = max_diff <= tolerance
    
    # 计算统计信息
    match_count = np.sum(np.abs(differences) <= tolerance)
    match_rate = match_count / len(differences) * 100
    
    return {
        'consistent': consistent,
        'max_difference': float(max_diff),
        'mean_difference': float(mean_diff),
        'match_count': int(match_count),
        'total_count': len(differences),
        'match_rate': float(match_rate),
        'tolerance': tolerance,
        'details': {
            'baseline_range': [float(np.min(baseline_scores)), float(np.max(baseline_scores))],
            'unified_range': [float(np.min(unified_scores)), float(np.max(unified_scores))],
            'baseline_mean': float(np.mean(baseline_scores)),
            'unified_mean': float(np.mean(unified_scores))
        }
    }

def verify_consistency():
    """主验证函数"""
    print("=== MCSI指标统一接口一致性验证 ===")
    
    # 1. 加载基准和测试数据
    baseline_data = load_baseline_scores()
    test_df = load_test_data()
    
    print(f"基准数据: {len(baseline_data['scores'])} 个指标")
    print(f"测试数据: {len(test_df)} 行")
    
    verification_results = {
        'timestamp': datetime.now().isoformat(),
        'baseline_metadata': baseline_data['metadata'],
        'test_data_points': len(test_df),
        'results': {}
    }
    
    # 2. 验证MCSI MACD
    print("\n验证 MCSI MACD...")
    try:
        macd_unit = MCSIMACDScoringUnit()
        macd_scores = []
        
        window_size = 100
        for i in range(window_size, len(test_df)):
            window_data = test_df.iloc[max(0, i-window_size):i+1]
            result = macd_unit.calculate_score(window_data)
            macd_scores.append(result.score)
        
        baseline_macd = baseline_data['scores']['mcsi_macd']['values']
        macd_comparison = compare_scores(baseline_macd, macd_scores, 'MCSI MACD')
        verification_results['results']['mcsi_macd'] = macd_comparison
        
        if macd_comparison['consistent']:
            print(f"  ✅ MCSI MACD: 100%一致 (最大差异: {macd_comparison['max_difference']:.2e})")
        else:
            print(f"  ❌ MCSI MACD: 不一致 (最大差异: {macd_comparison['max_difference']:.2f}, 匹配率: {macd_comparison['match_rate']:.1f}%)")
            
    except Exception as e:
        print(f"  ❌ MCSI MACD 验证失败: {e}")
        verification_results['results']['mcsi_macd'] = {'error': str(e)}
    
    # 3. 验证MCSI MMT  
    print("\n验证 MCSI MMT...")
    try:
        mmt_unit = MCSIMMTScoringUnit()
        mmt_scores = []
        
        window_size = 100
        for i in range(window_size, len(test_df)):
            window_data = test_df.iloc[max(0, i-window_size):i+1]
            result = mmt_unit.calculate_score(window_data)
            mmt_scores.append(result.score)
        
        baseline_mmt = baseline_data['scores']['mcsi_mmt']['values'] 
        mmt_comparison = compare_scores(baseline_mmt, mmt_scores, 'MCSI MMT')
        verification_results['results']['mcsi_mmt'] = mmt_comparison
        
        if mmt_comparison['consistent']:
            print(f"  ✅ MCSI MMT: 100%一致 (最大差异: {mmt_comparison['max_difference']:.2e})")
        else:
            print(f"  ❌ MCSI MMT: 不一致 (最大差异: {mmt_comparison['max_difference']:.2f}, 匹配率: {mmt_comparison['match_rate']:.1f}%)")
            
    except Exception as e:
        print(f"  ❌ MCSI MMT 验证失败: {e}")
        verification_results['results']['mcsi_mmt'] = {'error': str(e)}
    
    # 4. 验证MCSI RSI
    print("\n验证 MCSI RSI...")
    try:
        rsi_unit = MCSIRSIScoringUnit()
        rsi_scores = []
        
        window_size = 100
        for i in range(window_size, len(test_df)):
            window_data = test_df.iloc[max(0, i-window_size):i+1]
            result = rsi_unit.calculate_score(window_data)
            rsi_scores.append(result.score)
        
        baseline_rsi = baseline_data['scores']['mcsi_rsi']['values']
        rsi_comparison = compare_scores(baseline_rsi, rsi_scores, 'MCSI RSI')
        verification_results['results']['mcsi_rsi'] = rsi_comparison
        
        if rsi_comparison['consistent']:
            print(f"  ✅ MCSI RSI: 100%一致 (最大差异: {rsi_comparison['max_difference']:.2e})")
        else:
            print(f"  ❌ MCSI RSI: 不一致 (最大差异: {rsi_comparison['max_difference']:.2f}, 匹配率: {rsi_comparison['match_rate']:.1f}%)")
            
    except Exception as e:
        print(f"  ❌ MCSI RSI 验证失败: {e}")
        verification_results['results']['mcsi_rsi'] = {'error': str(e)}
    
    # 5. 验证MCSI TTM
    print("\n验证 MCSI TTM...")
    try:
        ttm_unit = MCSITTMScoringUnit()
        ttm_scores = []
        
        window_size = 100
        for i in range(window_size, len(test_df)):
            window_data = test_df.iloc[max(0, i-window_size):i+1]
            result = ttm_unit.calculate_score(window_data)
            ttm_scores.append(result.score)
        
        baseline_ttm = baseline_data['scores']['mcsi_ttm']['values']
        ttm_comparison = compare_scores(baseline_ttm, ttm_scores, 'MCSI TTM')
        verification_results['results']['mcsi_ttm'] = ttm_comparison
        
        if ttm_comparison['consistent']:
            print(f"  ✅ MCSI TTM: 100%一致 (最大差异: {ttm_comparison['max_difference']:.2e})")
        else:
            print(f"  ❌ MCSI TTM: 不一致 (最大差异: {ttm_comparison['max_difference']:.2f}, 匹配率: {ttm_comparison['match_rate']:.1f}%)")
            
    except Exception as e:
        print(f"  ❌ MCSI TTM 验证失败: {e}")
        verification_results['results']['mcsi_ttm'] = {'error': str(e)}
    
    # 6. 保存验证结果
    def json_serializer(obj):
        """自定义JSON序列化器"""
        if isinstance(obj, np.bool_):
            return bool(obj)
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            if np.isnan(obj):
                return 0.0
            return float(obj)
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
    
    output_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'verification_results.json')
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(verification_results, f, indent=2, ensure_ascii=False, default=json_serializer)
    
    # 7. 输出总结
    print(f"\n=== 验证结果已保存到: {output_path} ===")
    
    total_consistent = 0
    total_indicators = 0
    
    for indicator, result in verification_results['results'].items():
        if 'error' not in result:
            total_indicators += 1
            if result['consistent']:
                total_consistent += 1
    
    if total_indicators > 0:
        consistency_rate = total_consistent / total_indicators * 100
        print(f"\n总体一致性: {total_consistent}/{total_indicators} ({consistency_rate:.1f}%)")
        
        if consistency_rate == 100:
            print("🎉 所有指标100%一致！可以进行PyArmor迁移。")
        else:
            print("⚠️  存在不一致，需要修复后再进行PyArmor迁移。")
    else:
        print("❌ 所有指标验证都失败了。")
    
    return verification_results

if __name__ == "__main__":
    verify_consistency()