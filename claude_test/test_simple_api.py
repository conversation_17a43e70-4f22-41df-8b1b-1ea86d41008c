#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Step 2.4: 简化API功能验证
不依赖requests库，直接测试核心组件
"""

import sys
import os
import pandas as pd

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))

def test_mcsi_adapter_api():
    """测试MCSI适配器API"""
    print("=== 测试MCSI适配器API ===")
    
    try:
        from core.scoring_units.mcsi_adapter import MCSIAdapter
        
        # 准备测试数据
        csv_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'stock_data', 'cnindex_000001_上证指数.csv')
        df = pd.read_csv(csv_path).tail(150)
        df = df.rename(columns={'timestamp': 'date'})
        df['date'] = pd.to_datetime(df['date'])
        
        print(f"测试数据: {len(df)} 行")
        
        # 测试各个MCSI指标的适配器API
        indicators = [
            ('MACD', MCSIAdapter.create_mcsi_macd_unit),
            ('MMT', MCSIAdapter.create_mcsi_mmt_unit),
            ('RSI', MCSIAdapter.create_mcsi_rsi_unit),
            ('TTM', MCSIAdapter.create_mcsi_ttm_unit)
        ]
        
        results = []
        
        for name, create_func in indicators:
            try:
                unit = create_func()
                result = unit.calculate_score(df)
                
                results.append({
                    'indicator': name,
                    'score': result.score,
                    'signal': result.signal,
                    'confidence': result.confidence,
                    'unit_type': result.metadata.get('unit_type', 'unknown'),
                    'data_source': result.metadata.get('data_source', 'unknown')
                })
                
                print(f"✅ {name}: 分数={result.score:.2f}, 信号={result.signal}, 类型={result.metadata.get('unit_type', 'unknown')}")
                
            except Exception as e:
                print(f"❌ {name} 测试失败: {e}")
                results.append({
                    'indicator': name,
                    'error': str(e)
                })
        
        print(f"\n✅ MCSI适配器API测试完成: {len([r for r in results if 'error' not in r])}/{len(indicators)} 成功")
        return results
        
    except Exception as e:
        print(f"❌ MCSI适配器导入失败: {e}")
        return []

def test_composite_scorer():
    """测试综合评分器"""
    print("\n=== 测试综合评分器 ===")
    
    try:
        from core.composite.scorer import NewCompositeScorer
        
        # 初始化评分器
        scorer = NewCompositeScorer()
        
        print(f"✅ 综合评分器初始化成功")
        print(f"评分器类型: {type(scorer).__name__}")
        
        # 获取可用的评分单元
        units = scorer.get_available_units()
        print(f"可用评分单元: {len(units)} 个")
        
        for unit_id, unit_info in units.items():
            print(f"  - {unit_id}: {unit_info.get('name', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 综合评分器测试失败: {e}")
        return False

def test_data_provider():
    """测试数据提供者"""
    print("\n=== 测试数据提供者 ===")
    
    try:
        from core.scoring_units.data_provider import DataProvider
        
        provider = DataProvider(cache_enabled=False)
        
        # 测试CSV数据源
        csv_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'stock_data', 'cnindex_000001_上证指数.csv')
        ohlc_df = pd.read_csv(csv_path).tail(100)
        
        df_csv = provider.get_stock_data(
            db_conn=None,
            symbol=None,
            ohlc=ohlc_df,
            period='daily'
        )
        
        if not df_csv.empty:
            print(f"✅ CSV数据源工作正常: {len(df_csv)} 行")
        else:
            print("⚠️  CSV数据源返回空数据")
        
        # 测试数据库数据源（如果可用）
        try:
            db_conn = '************************************************/fintech_db'
            df_db = provider.get_stock_data(
                db_conn=db_conn,
                symbol='cnindex_000001_上证指数',
                period='daily'
            )
            
            if not df_db.empty:
                print(f"✅ 数据库数据源工作正常: {len(df_db)} 行")
            else:
                print("⚠️  数据库数据源返回空数据")
                
        except Exception as e:
            print(f"⚠️  数据库连接失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据提供者测试失败: {e}")
        return False

def test_trend_analyzer():
    """测试趋势分析器"""
    print("\n=== 测试趋势分析器 ===")
    
    try:
        from trend_analyzer import TrendAnalyzer
        
        analyzer = TrendAnalyzer()
        
        # 准备测试数据
        csv_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'stock_data', 'cnindex_000001_上证指数.csv')
        df = pd.read_csv(csv_path).tail(100)
        
        # 分析趋势
        result = analyzer.analyze_trend(df, symbol='000001')
        
        if result:
            print(f"✅ 趋势分析成功")
            print(f"  综合评分: {result.get('composite_score', 'N/A')}")
            print(f"  趋势评级: {result.get('trend_rating', 'N/A')}")
        else:
            print("⚠️  趋势分析返回空结果")
        
        return True
        
    except Exception as e:
        print(f"❌ 趋势分析器测试失败: {e}")
        return False

def test_api_compatibility():
    """测试API兼容性"""
    print("\n=== 测试API兼容性 ===")
    
    # 模拟API调用数据格式
    api_data = {
        'symbol': 'cnindex_000001_上证指数',
        'period': 'daily',
        'data_limit': 100
    }
    
    try:
        from core.scoring_units.mcsi_adapter import MCSIAdapter
        
        # 测试adapter能否处理API格式的数据
        csv_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'stock_data', 'cnindex_000001_上证指数.csv')
        df = pd.read_csv(csv_path).tail(api_data['data_limit'])
        df = df.rename(columns={'timestamp': 'date'})
        df['date'] = pd.to_datetime(df['date'])
        
        # 测试MACD适配器处理API数据
        macd_unit = MCSIAdapter.create_mcsi_macd_unit()
        result = macd_unit.calculate_score(df)
        
        # 检查返回的结果格式是否符合API要求
        api_response = {
            'success': True,
            'indicator': 'MCSI_MACD',
            'score': result.score,
            'signal': result.signal,
            'confidence': result.confidence,
            'metadata': result.metadata,
            'timestamp': result.timestamp.isoformat()
        }
        
        print(f"✅ API兼容性测试成功")
        print(f"  响应格式: {list(api_response.keys())}")
        print(f"  分数: {api_response['score']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ API兼容性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== Step 2.4: 简化API功能验证 ===")
    
    test_results = []
    
    # 1. 测试MCSI适配器API
    mcsi_results = test_mcsi_adapter_api()
    test_results.append(('MCSI适配器', len(mcsi_results) > 0))
    
    # 2. 测试综合评分器
    scorer_ok = test_composite_scorer()
    test_results.append(('综合评分器', scorer_ok))
    
    # 3. 测试数据提供者
    provider_ok = test_data_provider()
    test_results.append(('数据提供者', provider_ok))
    
    # 4. 测试趋势分析器
    analyzer_ok = test_trend_analyzer()
    test_results.append(('趋势分析器', analyzer_ok))
    
    # 5. 测试API兼容性
    api_ok = test_api_compatibility()
    test_results.append(('API兼容性', api_ok))
    
    # 输出总结
    print("\n=== API功能验证总结 ===")
    success_count = 0
    total_count = len(test_results)
    
    for test_name, success in test_results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n总体成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count == total_count:
        print("🎉 所有API功能验证通过！")
    else:
        print("⚠️  部分API功能需要修复")

if __name__ == "__main__":
    main()