#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基准分数生成器
使用TV-code/py-code/的权威版本生成4个MCSI指标的基准分数
用于后续PyArmor迁移的100%一致性验证
"""

import sys
import os
import numpy as np
import pandas as pd
import json
from datetime import datetime

# 添加路径以导入权威版本的MCSI指标
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'TV-code', 'py-code'))

try:
    from mcsi_macd import MCSIMACDIndicator
    from mcsi_mmt import MCSIMMTIndicator  
    from mcsi_rsi import MCSIRSIIndicator
    from mcsi_ttm import MCSITTMIndicator
except ImportError as e:
    print(f"导入权威版本MCSI指标失败: {e}")
    sys.exit(1)

def load_shanghai_index_data():
    """加载上证指数数据"""
    csv_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'stock_data', 'cnindex_000001_上证指数.csv')
    
    try:
        df = pd.read_csv(csv_path)
        print(f"加载上证指数数据: {len(df)} 行")
        
        # 确保必要的列存在
        required_cols = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        for col in required_cols:
            if col not in df.columns:
                raise ValueError(f"缺少必要列: {col}")
        
        # 按时间排序
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.sort_values('timestamp').reset_index(drop=True)
        
        # 取最后200个数据点用于计算（确保有足够的历史数据）
        if len(df) > 200:
            df = df.tail(200).reset_index(drop=True)
            print(f"使用最近200个数据点进行计算")
            
        return df
        
    except Exception as e:
        print(f"加载数据失败: {e}")
        raise

def generate_baseline_scores():
    """生成基准分数"""
    print("=== MCSI指标基准分数生成器 ===")
    
    # 1. 加载数据
    df = load_shanghai_index_data()
    
    # 2. 准备数据
    close_prices = df['close'].values
    high_prices = df['high'].values  
    low_prices = df['low'].values
    volume = df['volume'].values
    
    baseline_scores = {
        'metadata': {
            'generated_at': datetime.now().isoformat(),
            'data_source': 'cnindex_000001_上证指数.csv',
            'data_points': len(df),
            'date_range': {
                'start': df['timestamp'].iloc[0].isoformat(),
                'end': df['timestamp'].iloc[-1].isoformat()
            },
            'authority_version': 'TV-code/py-code',
            'purpose': 'PyArmor迁移基准验证'
        },
        'scores': {}
    }
    
    # 3. MCSI MACD 基准
    print("生成 MCSI MACD 基准...")
    try:
        macd_indicator = MCSIMACDIndicator()
        macd_scores = []
        
        # 滑动窗口计算
        window_size = 100  # 至少100个历史点
        for i in range(window_size, len(close_prices)):
            window_data = close_prices[max(0, i-window_size):i+1]
            result = macd_indicator.calculate(window_data)
            # 提取分数或使用最后一个非NaN值
            if 'macd_score' in result:
                score = result['macd_score'][-1] if hasattr(result['macd_score'], '__getitem__') else result['macd_score']
            else:
                # 使用MACD histogram的最后一个值作为分数
                score = result.get('histogram', [0])[-1] if result.get('histogram') is not None else 0
            macd_scores.append(float(score) if not np.isnan(score) else 0.0)
            
        baseline_scores['scores']['mcsi_macd'] = {
            'values': macd_scores,
            'count': len(macd_scores),
            'stats': {
                'mean': float(np.mean(macd_scores)),
                'std': float(np.std(macd_scores)),
                'min': float(np.min(macd_scores)),
                'max': float(np.max(macd_scores))
            }
        }
        print(f"  完成: {len(macd_scores)} 个分数点, 均值: {np.mean(macd_scores):.2f}")
        
    except Exception as e:
        print(f"  MCSI MACD 计算失败: {e}")
        baseline_scores['scores']['mcsi_macd'] = {'error': str(e)}
    
    # 4. MCSI MMT 基准
    print("生成 MCSI MMT 基准...")
    try:
        mmt_indicator = MCSIMMTIndicator()
        mmt_scores = []
        
        window_size = 100
        for i in range(window_size, len(close_prices)):
            window_close = close_prices[max(0, i-window_size):i+1]
            window_high = high_prices[max(0, i-window_size):i+1]
            window_low = low_prices[max(0, i-window_size):i+1]
            result = mmt_indicator.calculate(window_close, window_high, window_low)
            # 提取MMT分数
            score = result.get('mmt_score', 0)[-1] if hasattr(result.get('mmt_score', 0), '__getitem__') else result.get('mmt_score', 0)
            mmt_scores.append(float(score) if not np.isnan(score) else 0.0)
            
        baseline_scores['scores']['mcsi_mmt'] = {
            'values': mmt_scores,
            'count': len(mmt_scores),
            'stats': {
                'mean': float(np.mean(mmt_scores)),
                'std': float(np.std(mmt_scores)),
                'min': float(np.min(mmt_scores)),
                'max': float(np.max(mmt_scores))
            }
        }
        print(f"  完成: {len(mmt_scores)} 个分数点, 均值: {np.mean(mmt_scores):.2f}")
        
    except Exception as e:
        print(f"  MCSI MMT 计算失败: {e}")
        baseline_scores['scores']['mcsi_mmt'] = {'error': str(e)}
    
    # 5. MCSI RSI 基准
    print("生成 MCSI RSI 基准...")
    try:
        rsi_indicator = MCSIRSIIndicator()
        rsi_scores = []
        
        window_size = 100
        for i in range(window_size, len(close_prices)):
            window_data = close_prices[max(0, i-window_size):i+1]
            result = rsi_indicator.calculate(window_data)
            # 提取RSI分数
            score = result.get('rsi_score', 0)[-1] if hasattr(result.get('rsi_score', 0), '__getitem__') else result.get('rsi_score', 0)
            rsi_scores.append(float(score) if not np.isnan(score) else 0.0)
            
        baseline_scores['scores']['mcsi_rsi'] = {
            'values': rsi_scores,
            'count': len(rsi_scores),
            'stats': {
                'mean': float(np.mean(rsi_scores)),
                'std': float(np.std(rsi_scores)),
                'min': float(np.min(rsi_scores)),
                'max': float(np.max(rsi_scores))
            }
        }
        print(f"  完成: {len(rsi_scores)} 个分数点, 均值: {np.mean(rsi_scores):.2f}")
        
    except Exception as e:
        print(f"  MCSI RSI 计算失败: {e}")
        baseline_scores['scores']['mcsi_rsi'] = {'error': str(e)}
    
    # 6. MCSI TTM 基准
    print("生成 MCSI TTM 基准...")
    try:
        ttm_indicator = MCSITTMIndicator()
        ttm_scores = []
        
        window_size = 100
        for i in range(window_size, len(close_prices)):
            window_data = close_prices[max(0, i-window_size):i+1]
            result = ttm_indicator.calculate(window_data)
            # 提取TTM分数
            score = result.get('ttm_score', 0)[-1] if hasattr(result.get('ttm_score', 0), '__getitem__') else result.get('ttm_score', 0)
            ttm_scores.append(float(score) if not np.isnan(score) else 0.0)
            
        baseline_scores['scores']['mcsi_ttm'] = {
            'values': ttm_scores,
            'count': len(ttm_scores),
            'stats': {
                'mean': float(np.mean(ttm_scores)),
                'std': float(np.std(ttm_scores)),
                'min': float(np.min(ttm_scores)),
                'max': float(np.max(ttm_scores))
            }
        }
        print(f"  完成: {len(ttm_scores)} 个分数点, 均值: {np.mean(ttm_scores):.2f}")
        
    except Exception as e:
        print(f"  MCSI TTM 计算失败: {e}")
        baseline_scores['scores']['mcsi_ttm'] = {'error': str(e)}
    
    # 7. 保存基准分数
    output_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'baseline_scores.json')
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(baseline_scores, f, indent=2, ensure_ascii=False)
    
    print(f"\n=== 基准分数已保存到: {output_path} ===")
    
    # 8. 输出摘要
    print("\n基准分数摘要:")
    for indicator, data in baseline_scores['scores'].items():
        if 'error' in data:
            print(f"  {indicator}: 错误 - {data['error']}")
        else:
            stats = data['stats']
            print(f"  {indicator}: {data['count']}个分数点, 范围[{stats['min']:.2f}, {stats['max']:.2f}], 均值{stats['mean']:.2f}")
    
    return baseline_scores

if __name__ == "__main__":
    generate_baseline_scores()