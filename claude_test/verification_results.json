{"timestamp": "2025-08-20T03:06:20.548027", "baseline_metadata": {"generated_at": "2025-08-19T16:14:58.726703", "data_source": "cnindex_000001_上证指数.csv", "data_points": 200, "date_range": {"start": "2024-09-03T00:00:00", "end": "2025-07-04T00:00:00"}, "authority_version": "TV-code/py-code", "purpose": "PyArmor迁移基准验证"}, "test_data_points": 200, "results": {"mcsi_macd": {"consistent": true, "max_difference": 0.0, "mean_difference": 0.0, "match_count": 100, "total_count": 100, "match_rate": 100.0, "tolerance": 1e-10, "details": {"baseline_range": [-64.48630773295879, 63.5619635688762], "unified_range": [-64.48630773295879, 63.5619635688762], "baseline_mean": -4.12813131477458, "unified_mean": -4.12813131477458}}, "mcsi_mmt": {"consistent": true, "max_difference": 0.0, "mean_difference": 0.0, "match_count": 100, "total_count": 100, "match_rate": 100.0, "tolerance": 1e-10, "details": {"baseline_range": [-85.0, 85.0], "unified_range": [-85.0, 85.0], "baseline_mean": -1.1, "unified_mean": -1.1}}, "mcsi_rsi": {"consistent": true, "max_difference": 0.0, "mean_difference": 0.0, "match_count": 100, "total_count": 100, "match_rate": 100.0, "tolerance": 1e-10, "details": {"baseline_range": [-67.0, 67.0], "unified_range": [-67.0, 67.0], "baseline_mean": 2.67, "unified_mean": 2.67}}, "mcsi_ttm": {"consistent": true, "max_difference": 0.0, "mean_difference": 0.0, "match_count": 100, "total_count": 100, "match_rate": 100.0, "tolerance": 1e-10, "details": {"baseline_range": [-100.0, 100.0], "unified_range": [-100.0, 100.0], "baseline_mean": -9.8, "unified_mean": -9.8}}}}