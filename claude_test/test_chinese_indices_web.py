#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web应用是否能正确分析中国指数
"""

import requests
import json
import sys
import time

def test_web_api():
    """测试Web API"""
    base_url = "http://127.0.0.1:50505"
    
    print("🔍 测试Web应用中国指数分析功能")
    print("=" * 50)
    
    # 1. 测试基础连接
    print("1. 测试基础连接...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"   主页响应状态: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")
        return
    
    # 2. 测试分组API
    print("\n2. 测试分组API...")
    try:
        response = requests.get(f"{base_url}/api/groups", timeout=5)
        if response.status_code == 200:
            groups = response.json()
            print(f"   ✅ 获取到 {len(groups)} 个分组")
            for group_name, group_info in groups.items():
                if isinstance(group_info, dict):
                    stocks = group_info.get('stocks', [])
                    print(f"     - {group_name}: {len(stocks)} 只股票")
                    
                    # 检查是否包含中国指数
                    chinese_stocks = [s for s in stocks if any(keyword in str(s).lower() 
                                    for keyword in ['cnindex', '上证', '深证', '创业板', '沪深'])]
                    if chinese_stocks:
                        print(f"       包含中国指数: {len(chinese_stocks)} 个")
                        for stock in chinese_stocks[:3]:  # 显示前3个
                            print(f"         * {stock}")
                        if len(chinese_stocks) > 3:
                            print(f"         ... 还有 {len(chinese_stocks) - 3} 个")
        else:
            print(f"   ❌ 分组API响应失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 分组API请求失败: {e}")
    
    # 3. 测试评分单元API
    print("\n3. 测试评分单元API...")
    try:
        response = requests.get(f"{base_url}/api/scoring_units", timeout=5)
        if response.status_code == 200:
            units = response.json()
            print(f"   ✅ 获取到 {len(units)} 个评分单元")
        else:
            print(f"   ❌ 评分单元API响应失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 评分单元API请求失败: {e}")
    
    # 4. 测试分析API（使用中国指数）
    print("\n4. 测试中国指数分析...")
    chinese_indices = [
        "cnindex_000001_上证指数",
        "cnindex_399006_创业板指", 
        "cnindex_399300_沪深300"
    ]
    
    for index_name in chinese_indices:
        print(f"\n   测试 {index_name}...")
        try:
            # 构建分析请求
            payload = {
                "selected_groups": ["trend_group", "oscillation_group"],
                "data_limit": 250,
                "custom_symbols": [index_name]
            }
            
            response = requests.post(
                f"{base_url}/api/analyze",
                json=payload,
                timeout=30,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                success_count = result.get('success_count', 0)
                error_count = result.get('error_count', 0)
                print(f"     ✅ 分析完成: 成功 {success_count}, 失败 {error_count}")
                
                # 检查结果详情
                if 'results' in result:
                    for symbol, data in result['results'].items():
                        if index_name in symbol:
                            if 'error' in data:
                                print(f"     ❌ 分析错误: {data['error']}")
                            else:
                                scores = data.get('scores', {})
                                print(f"     📊 评分结果:")
                                for unit_name, score in scores.items():
                                    if isinstance(score, dict) and 'score' in score:
                                        print(f"       {unit_name}: {score['score']:.2f}")
                            break
            else:
                print(f"     ❌ 分析请求失败: {response.status_code}")
                if response.text:
                    print(f"     错误信息: {response.text[:200]}")
                
        except Exception as e:
            print(f"     ❌ 分析请求异常: {e}")
        
        time.sleep(1)  # 避免请求过快
    
    # 5. 检查最终结果
    print("\n5. 检查分析结果...")
    try:
        response = requests.get(f"{base_url}/api/results", timeout=5)
        if response.status_code == 200:
            results = response.json()
            total_results = len(results)
            print(f"   ✅ 获取到 {total_results} 个分析结果")
            
            # 统计中国指数结果
            chinese_results = []
            for symbol, data in results.items():
                if any(keyword in symbol.lower() for keyword in ['cnindex', '上证', '深证', '创业板', '沪深']):
                    chinese_results.append((symbol, data))
            
            print(f"   🇨🇳 中国指数分析结果: {len(chinese_results)} 个")
            for symbol, data in chinese_results[:3]:  # 显示前3个
                scores = data.get('scores', {})
                trend_score = scores.get('trend_unit', {}).get('score', 'N/A')
                print(f"     * {symbol}: 趋势得分 {trend_score}")
                
        else:
            print(f"   ❌ 结果API响应失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 结果API请求失败: {e}")
    
    print("\n" + "=" * 50)
    print("测试完成!")

if __name__ == "__main__":
    test_web_api()