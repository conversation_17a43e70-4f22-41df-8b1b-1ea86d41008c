#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的中国指数数据
使用现有DataLoader类进行查询
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.data.data_loader import DataLoader
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def main():
    """主函数"""
    print("🔍 检查数据库中的中国指数数据")
    print("=" * 50)
    
    # 创建数据加载器
    loader = DataLoader()
    
    # 尝试连接数据库
    print("📡 连接数据库...")
    if not loader.connect_db():
        print("❌ 数据库连接失败")
        return
    
    print("✅ 数据库连接成功")
    
    # 获取所有表名
    print("\n📊 获取所有数据表...")
    try:
        all_stocks = loader.get_all_data_tables()
        print(f"发现 {len(all_stocks)} 个数据表")
    except Exception as e:
        print(f"❌ 获取表名失败: {e}")
        return
    
    # 关键词搜索中国指数表
    chinese_keywords = ['cnindex', '中国', '上证', '深证', '创业板', '沪深', 'sh000', 'sz399']
    chinese_indices = []
    
    print(f"\n🔎 搜索包含中国指数关键词的表...")
    print(f"关键词: {', '.join(chinese_keywords)}")
    
    for stock in all_stocks:
        table_name = stock.get('table_name', '')
        symbol = stock.get('symbol', '')
        name = stock.get('name', '')
        
        # 检查是否包含关键词
        search_text = f"{table_name} {symbol} {name}".lower()
        if any(keyword.lower() in search_text for keyword in chinese_keywords):
            chinese_indices.append(stock)
    
    print(f"\n✅ 找到 {len(chinese_indices)} 个中国指数相关表:")
    print("-" * 60)
    
    # 显示找到的表并统计数据量
    for i, stock in enumerate(chinese_indices, 1):
        table_name = stock['table_name']
        symbol = stock.get('symbol', 'N/A')
        name = stock.get('name', 'N/A')
        
        print(f"{i}. {table_name}")
        print(f"   符号: {symbol}")
        print(f"   名称: {name}")
        
        # 统计数据量
        try:
            data = loader.get_stock_data(table_name, limit=1)
            if data is not None and not data.empty:
                # 获取总数据量
                count_query = f"SELECT COUNT(*) FROM \"{table_name}\""
                if loader.engine:
                    from sqlalchemy import text
                    with loader.engine.connect() as conn:
                        result = conn.execute(text(count_query))
                        total_count = result.fetchone()[0]
                elif loader.conn:
                    cursor = loader.conn.cursor()
                    cursor.execute(count_query)
                    total_count = cursor.fetchone()[0]
                    cursor.close()
                else:
                    total_count = "未知"
                
                print(f"   数据量: {total_count} 条记录")
                
                # 显示最新数据示例
                latest_data = loader.get_stock_data(table_name, limit=3)
                if latest_data is not None and not latest_data.empty:
                    print("   最新数据示例:")
                    for _, row in latest_data.head(1).iterrows():
                        print(f"     日期: {row.get('timestamp', 'N/A')}")
                        print(f"     开盘: {row.get('open', 'N/A'):.2f}")
                        print(f"     最高: {row.get('high', 'N/A'):.2f}")
                        print(f"     最低: {row.get('low', 'N/A'):.2f}")
                        print(f"     收盘: {row.get('close', 'N/A'):.2f}")
                        print(f"     成交量: {row.get('volume', 'N/A')}")
                        break
                else:
                    print("   ⚠️  无法获取数据示例")
            else:
                print("   ❌ 表中无数据")
                
        except Exception as e:
            print(f"   ❌ 查询数据失败: {e}")
        
        print()
    
    if not chinese_indices:
        print("❌ 未找到中国指数相关表")
        print("\n所有表名列表:")
        for stock in all_stocks[:10]:  # 显示前10个表名作为参考
            print(f"  - {stock.get('table_name', '')}")
        if len(all_stocks) > 10:
            print(f"  ... 还有 {len(all_stocks) - 10} 个表")
    
    print("\n" + "=" * 50)
    print("检查完成!")

if __name__ == "__main__":
    main()