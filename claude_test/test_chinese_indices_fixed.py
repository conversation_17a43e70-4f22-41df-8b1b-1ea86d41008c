#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试中国指数修复情况
验证Web应用现在是否能正确加载和分析中国指数
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目根目录到path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.data.hybrid_data_loader import HybridDataLoader
from core.composite.scorer import NewCompositeScorer
from core.data.hybrid_data_loader import HybridDataLoader

def test_chinese_indices_availability():
    """测试中国指数数据可用性"""
    print("🔍 测试中国指数数据可用性...")
    
    # 初始化混合数据加载器
    hybrid_loader = HybridDataLoader(prefer_database=True)
    
    # 获取所有股票
    all_stocks = hybrid_loader.get_stock_list()
    
    # 筛选中国指数
    chinese_indices = [stock for stock in all_stocks if stock['category'] == '中国指数']
    
    print(f"✅ 找到 {len(chinese_indices)} 个中国指数:")
    for idx in chinese_indices:
        print(f"   - {idx['symbol']}: {idx['name']} (表名: {idx['table_name']})")
    
    # 测试数据获取
    print(f"\n🔍 测试数据获取...")
    for idx in chinese_indices:
        try:
            data = hybrid_loader.get_stock_data(idx['table_name'], limit=10)
            if data is not None and not data.empty:
                print(f"   ✅ {idx['name']}: {len(data)} 条数据 (最新: {data['timestamp'].max()})")
            else:
                print(f"   ❌ {idx['name']}: 无数据")
        except Exception as e:
            print(f"   ❌ {idx['name']}: 错误 - {e}")
    
    return chinese_indices

def test_web_app_filter():
    """测试Web应用的过滤逻辑"""
    print(f"\n🔍 测试Web应用过滤逻辑...")
    
    # 模拟Web应用的分析逻辑
    hybrid_loader = HybridDataLoader(prefer_database=True)
    all_stocks = hybrid_loader.get_stock_list()
    
    # 使用修复后的逻辑（不过滤中国指数）
    stock_files = all_stocks
    
    # 统计各类型股票数量
    categories = {}
    for stock in stock_files:
        cat = stock['category']
        categories[cat] = categories.get(cat, 0) + 1
    
    print(f"✅ 修复后的股票分类统计:")
    for category, count in sorted(categories.items()):
        print(f"   - {category}: {count} 只")
    
    # 验证中国指数是否包含在内
    chinese_indices_count = categories.get('中国指数', 0)
    print(f"\n{'✅' if chinese_indices_count > 0 else '❌'} 中国指数包含情况: {chinese_indices_count} 只")
    
    return chinese_indices_count > 0

def test_scoring_with_chinese_indices():
    """测试对中国指数的评分"""
    print(f"\n🔍 测试中国指数评分...")
    
    # 初始化评分器
    try:
        scorer = NewCompositeScorer()
        hybrid_loader = HybridDataLoader(prefer_database=True)
        scorer.set_hybrid_data_loader(hybrid_loader)
        print("   ✅ 评分器初始化成功")
    except Exception as e:
        print(f"   ❌ 评分器初始化失败: {e}")
        return False
    
    # 获取中国指数
    hybrid_loader = HybridDataLoader(prefer_database=True)
    all_stocks = hybrid_loader.get_stock_list()
    chinese_indices = [stock for stock in all_stocks if stock['category'] == '中国指数']
    
    success_count = 0
    
    for idx in chinese_indices[:1]:  # 只测试第一个指数
        try:
            print(f"   测试评分: {idx['name']}")
            
            # 获取数据
            data = hybrid_loader.get_stock_data(idx['table_name'], limit=250)
            if data is None or data.empty:
                print(f"   ❌ {idx['name']}: 无数据")
                continue
            
            # 使用评分器分析
            result = scorer.score_stock(idx['table_name'], idx['name'], data_limit=250)
            
            if result:
                print(f"   ✅ {idx['name']}: 评分成功")
                print(f"      综合评分: {result.composite_score}")
                print(f"      评级等级: {result.grade}")
                success_count += 1
            else:
                print(f"   ❌ {idx['name']}: 评分失败")
                
        except Exception as e:
            print(f"   ❌ {idx['name']}: 评分异常 - {e}")
    
    print(f"\n{'✅' if success_count > 0 else '❌'} 评分测试结果: {success_count}/{len(chinese_indices[:1])} 成功")
    return success_count > 0

def main():
    """主测试函数"""
    print("="*60)
    print("🚀 中国指数修复验证测试")
    print("="*60)
    
    # 配置日志
    logging.basicConfig(level=logging.ERROR)  # 只显示错误，减少噪音
    
    results = []
    
    # 测试1: 数据可用性
    try:
        chinese_indices = test_chinese_indices_availability()
        results.append(("数据可用性", len(chinese_indices) > 0))
    except Exception as e:
        print(f"❌ 数据可用性测试失败: {e}")
        results.append(("数据可用性", False))
    
    # 测试2: Web应用过滤逻辑
    try:
        filter_ok = test_web_app_filter()
        results.append(("过滤逻辑", filter_ok))
    except Exception as e:
        print(f"❌ 过滤逻辑测试失败: {e}")
        results.append(("过滤逻辑", False))
    
    # 测试3: 评分功能
    try:
        scoring_ok = test_scoring_with_chinese_indices()
        results.append(("评分功能", scoring_ok))
    except Exception as e:
        print(f"❌ 评分功能测试失败: {e}")
        results.append(("评分功能", False))
    
    # 汇总结果
    print("\n" + "="*60)
    print("📊 测试结果汇总:")
    print("="*60)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not passed:
            all_passed = False
    
    print(f"\n🎯 总体结果: {'✅ 全部通过' if all_passed else '❌ 存在失败'}")
    
    if all_passed:
        print("\n🎉 中国指数修复成功！现在Web应用应该能正确分析中国指数数据了。")
        print("   请访问 http://127.0.0.1:50505 查看修复结果。")
    else:
        print("\n⚠️  还有问题需要解决，请检查失败的测试项目。")

if __name__ == "__main__":
    main()