#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复后的评分时间分辨率
测试日线级别评分计算是否正常工作
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from core.data.hybrid_data_loader import HybridDataLoader
from core.scoring_units.mcsi_macd_scoring import MCSIMACDScoringUnit
from core.scoring_units.mcsi_mmt_scoring import MCSIMMTScoringUnit

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def create_test_data(days=150):
    """创建测试数据"""
    dates = pd.date_range(start='2023-01-01', periods=days)
    
    # 创建有趋势变化的价格数据
    base_price = 100
    prices = []
    
    for i in range(days):
        # 添加不同的趋势段
        if i < days // 3:
            trend = 0.02  # 上涨趋势
        elif i < 2 * days // 3:
            trend = -0.01  # 下跌趋势  
        else:
            trend = 0.005  # 震荡趋势
            
        # 添加随机波动
        noise = np.random.normal(0, 0.01)
        base_price *= (1 + trend + noise)
        prices.append(base_price)
    
    data = pd.DataFrame({
        'date': dates,
        'open': [p * (1 + np.random.normal(0, 0.005)) for p in prices],
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': [1000000 + int(np.random.normal(0, 100000)) for _ in range(days)]
    })
    
    return data

def test_daily_resolution():
    """测试日线分辨率"""
    print("🧪 开始验证评分系统的日线分辨率...")
    
    # 创建测试数据
    test_data = create_test_data(100)
    print(f"✅ 创建了{len(test_data)}天的测试数据")
    
    # 初始化MCSI评分单元
    try:
        macd_unit = MCSIMACDScoringUnit()
        mmt_unit = MCSIMMTScoringUnit()
        print("✅ 成功初始化MCSI评分单元")
    except Exception as e:
        print(f"❌ 初始化MCSI评分单元失败: {e}")
        return False
    
    # 模拟web/app.py中的日线级别滑动窗口计算
    def calculate_daily_window_scores(data, macd_unit, mmt_unit, window_size=80):
        """高分辨率滑动窗口MCSI计算 - 日线级别精度"""
        data_length = len(data)
        min_required = 20  # 降低测试要求
        
        if data_length < 50:
            window_size = min(max(min_required, data_length), data_length)
        
        # ✅ 日线级别计算：每天都计算评分，无稀疏采样
        macd_scores = [0.0] * data_length
        mmt_scores = [0.0] * data_length
        
        print(f"📈 开始日线级别MCSI计算，数据长度: {data_length}")
        
        # 预缓存数据以减少重复访问
        cached_data = data.copy()
        
        for i in range(data_length):
            # 确保有足够的历史数据
            if i < min_required - 1:
                macd_scores[i] = 0.0
                mmt_scores[i] = 0.0
                continue
            
            start_idx = max(0, i + 1 - window_size)
            end_idx = i + 1
            window_data = cached_data.iloc[start_idx:end_idx]
            
            try:
                macd_result = macd_unit.calculate_score(window_data)
                mmt_result = mmt_unit.calculate_score(window_data)
                
                macd_scores[i] = macd_result.score if macd_result else 0.0
                mmt_scores[i] = mmt_result.score if mmt_result else 0.0
                
            except Exception as e:
                # 使用前一天的值作为回退
                if i > 0:
                    macd_scores[i] = macd_scores[i-1]
                    mmt_scores[i] = mmt_scores[i-1]
                else:
                    macd_scores[i] = 0.0
                    mmt_scores[i] = 0.0
                
                print(f"⚠️  第{i+1}天MCSI计算失败: {e}")
        
        print(f"✅ 日线级别MCSI计算完成，共{data_length}个评分点")
        return macd_scores, mmt_scores
    
    # 执行计算
    try:
        macd_scores, mmt_scores = calculate_daily_window_scores(
            test_data, macd_unit, mmt_unit, window_size=80
        )
        print("✅ 成功完成日线级别评分计算")
    except Exception as e:
        print(f"❌ 日线级别评分计算失败: {e}")
        return False
    
    # 验证分辨率
    print("\n📊 评分时间分辨率验证结果:")
    
    # 检查每日都有评分
    valid_macd = [i for i, score in enumerate(macd_scores) if score != 0.0]
    valid_mmt = [i for i, score in enumerate(mmt_scores) if score != 0.0]
    
    print(f"MACD有效评分天数: {len(valid_macd)} / {len(test_data)}")
    print(f"MMT有效评分天数: {len(valid_mmt)} / {len(test_data)}")
    
    # 检查评分变化频率
    macd_changes = sum(1 for i in range(1, len(macd_scores)) 
                      if abs(macd_scores[i] - macd_scores[i-1]) > 0.01)
    mmt_changes = sum(1 for i in range(1, len(mmt_scores)) 
                     if abs(mmt_scores[i] - mmt_scores[i-1]) > 0.01)
    
    print(f"MACD评分变化次数: {macd_changes} (应该 > {len(test_data) // 10})")
    print(f"MMT评分变化次数: {mmt_changes} (应该 > {len(test_data) // 10})")
    
    # 显示前10天和后10天的评分
    print(f"\n🔍 前10天MACD评分: {[f'{s:.2f}' for s in macd_scores[:10]]}")
    print(f"🔍 后10天MACD评分: {[f'{s:.2f}' for s in macd_scores[-10:]]}")
    
    print(f"\n🔍 前10天MMT评分: {[f'{s:.2f}' for s in mmt_scores[:10]]}")
    print(f"🔍 后10天MMT评分: {[f'{s:.2f}' for s in mmt_scores[-10:]]}")
    
    # 检查是否仍存在稀疏采样模式（每5天重复）
    sparse_pattern_detected = False
    for i in range(5, len(macd_scores) - 5):
        if (abs(macd_scores[i] - macd_scores[i-5]) < 0.001 and 
            abs(macd_scores[i+5] - macd_scores[i]) < 0.001):
            sparse_pattern_detected = True
            break
    
    if sparse_pattern_detected:
        print("❌ 检测到稀疏采样模式（每5天重复），修复可能不完整")
        return False
    else:
        print("✅ 未检测到稀疏采样模式，评分呈现日线级别变化")
    
    # 最终评估
    resolution_score = 0
    
    if len(valid_macd) > len(test_data) * 0.7:
        resolution_score += 25
        print("✅ MACD评分覆盖率良好")
    else:
        print("⚠️  MACD评分覆盖率较低")
    
    if len(valid_mmt) > len(test_data) * 0.7:
        resolution_score += 25
        print("✅ MMT评分覆盖率良好")
    else:
        print("⚠️  MMT评分覆盖率较低")
    
    if macd_changes > len(test_data) // 10:
        resolution_score += 25
        print("✅ MACD评分变化频率符合日线预期")
    else:
        print("⚠️  MACD评分变化频率过低")
    
    if mmt_changes > len(test_data) // 10:
        resolution_score += 25
        print("✅ MMT评分变化频率符合日线预期")
    else:
        print("⚠️  MMT评分变化频率过低")
    
    print(f"\n🎯 时间分辨率修复评分: {resolution_score}/100")
    
    if resolution_score >= 80:
        print("🎉 评分系统时间分辨率修复成功！")
        return True
    elif resolution_score >= 60:
        print("⚠️  评分系统时间分辨率基本修复，仍有改进空间")
        return True
    else:
        print("❌ 评分系统时间分辨率修复不完整")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("📈 评分系统时间分辨率验证工具")
    print("=" * 60)
    
    success = test_daily_resolution()
    
    if success:
        print("\n✅ 验证完成：评分系统已修复为日线级别分辨率")
        return 0
    else:
        print("\n❌ 验证失败：评分系统时间分辨率仍需改进")
        return 1

if __name__ == "__main__":
    exit(main())