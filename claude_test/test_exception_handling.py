#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Step 2.5: 测试基本异常处理和日志记录
验证系统在异常情况下的健壮性
"""

import sys
import os
import pandas as pd
import numpy as np
import logging

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))

def setup_test_logging():
    """设置测试日志"""
    log_format = '[%(asctime)s] %(levelname)s - %(name)s - %(message)s'
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 创建测试日志文件
    log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'test_exception_handling.log')
    file_handler = logging.FileHandler(log_file, mode='w')
    file_handler.setFormatter(logging.Formatter(log_format))
    
    # 添加到根logger
    root_logger = logging.getLogger()
    root_logger.addHandler(file_handler)
    
    print(f"测试日志将保存到: {log_file}")

def test_empty_data_handling():
    """测试空数据处理"""
    print("\n=== 测试空数据处理 ===")
    
    try:
        from core.scoring_units.mcsi_adapter import MCSIAdapter
        
        # 1. 测试空DataFrame
        empty_df = pd.DataFrame()
        
        macd_unit = MCSIAdapter.create_mcsi_macd_unit()
        result = macd_unit.calculate_score(empty_df)
        
        if result.score == 0.0 and 'error' in result.description.lower():
            print("✅ 空DataFrame处理正确")
        else:
            print(f"⚠️  空DataFrame处理异常: score={result.score}, desc={result.description}")
        
        # 2. 测试数据不足的DataFrame
        insufficient_df = pd.DataFrame({
            'date': pd.date_range('2023-01-01', periods=5),
            'open': [100, 101, 102, 103, 104],
            'high': [105, 106, 107, 108, 109],
            'low': [95, 96, 97, 98, 99],
            'close': [102, 103, 104, 105, 106],
            'volume': [1000, 1100, 1200, 1300, 1400]
        })
        
        result = macd_unit.calculate_score(insufficient_df)
        print(f"数据不足处理: score={result.score:.2f}, conf={result.confidence:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 空数据处理测试失败: {e}")
        return False

def test_invalid_data_handling():
    """测试无效数据处理"""
    print("\n=== 测试无效数据处理 ===")
    
    try:
        from core.scoring_units.mcsi_adapter import MCSIAdapter
        
        # 1. 测试包含NaN的数据
        nan_df = pd.DataFrame({
            'date': pd.date_range('2023-01-01', periods=100),
            'open': [100 + i + (np.nan if i % 10 == 0 else 0) for i in range(100)],
            'high': [105 + i for i in range(100)],
            'low': [95 + i for i in range(100)],
            'close': [102 + i for i in range(100)],
            'volume': [1000 + i*10 for i in range(100)]
        })
        
        macd_unit = MCSIAdapter.create_mcsi_macd_unit()
        result = macd_unit.calculate_score(nan_df)
        
        print(f"NaN数据处理: score={result.score:.2f}, signal={result.signal}")
        
        # 2. 测试缺少必要列的数据
        incomplete_df = pd.DataFrame({
            'date': pd.date_range('2023-01-01', periods=100),
            'close': [102 + i for i in range(100)]
            # 缺少其他OHLCV列
        })
        
        try:
            result = macd_unit.calculate_score(incomplete_df)
            print(f"缺少列处理: score={result.score:.2f}")
        except Exception as e:
            print(f"缺少列异常捕获: {type(e).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ 无效数据处理测试失败: {e}")
        return False

def test_calculation_errors():
    """测试计算错误处理"""
    print("\n=== 测试计算错误处理 ===")
    
    try:
        from core.scoring_units.mcsi_adapter import MCSIAdapter
        
        # 创建极端数据触发计算错误
        extreme_df = pd.DataFrame({
            'date': pd.date_range('2023-01-01', periods=100),
            'open': [1e10 for i in range(100)],    # 极大值
            'high': [1e10 for i in range(100)],
            'low': [1e-10 for i in range(100)],    # 极小值
            'close': [0 for i in range(100)],      # 零值
            'volume': [float('inf') if i % 2 == 0 else -float('inf') for i in range(100)]  # 无限值
        })
        
        indicators = [
            ('MACD', MCSIAdapter.create_mcsi_macd_unit),
            ('MMT', MCSIAdapter.create_mcsi_mmt_unit),
            ('RSI', MCSIAdapter.create_mcsi_rsi_unit),
            ('TTM', MCSIAdapter.create_mcsi_ttm_unit)
        ]
        
        for name, create_func in indicators:
            try:
                unit = create_func()
                result = unit.calculate_score(extreme_df)
                
                # 检查结果是否合理
                if np.isfinite(result.score) and -100 <= result.score <= 100:
                    print(f"✅ {name}: 极端数据处理正常 (score={result.score:.2f})")
                else:
                    print(f"⚠️  {name}: 极端数据处理异常 (score={result.score})")
                    
            except Exception as e:
                print(f"⚠️  {name}: 计算异常被捕获 - {type(e).__name__}: {str(e)[:100]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 计算错误测试失败: {e}")
        return False

def test_logging_functionality():
    """测试日志功能"""
    print("\n=== 测试日志功能 ===")
    
    try:
        import logging
        
        # 获取MCSI相关的logger
        mcsi_logger = logging.getLogger('core.scoring_units.mcsi_macd_scoring')
        adapter_logger = logging.getLogger('core.scoring_units.mcsi_adapter')
        
        # 测试不同级别的日志
        mcsi_logger.info("测试MCSI MACD日志信息")
        mcsi_logger.warning("测试MCSI MACD警告信息")
        adapter_logger.info("测试适配器日志信息")
        
        # 测试异常日志
        try:
            raise ValueError("测试异常日志")
        except ValueError as e:
            mcsi_logger.error(f"捕获测试异常: {e}")
        
        print("✅ 日志功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 日志功能测试失败: {e}")
        return False

def test_resource_cleanup():
    """测试资源清理"""
    print("\n=== 测试资源清理 ===")
    
    try:
        from core.scoring_units.mcsi_adapter import MCSIAdapter
        
        # 创建大量单元实例测试内存管理
        units = []
        for i in range(10):
            units.extend([
                MCSIAdapter.create_mcsi_macd_unit(),
                MCSIAdapter.create_mcsi_mmt_unit(),
                MCSIAdapter.create_mcsi_rsi_unit(),
                MCSIAdapter.create_mcsi_ttm_unit()
            ])
        
        # 测试计算
        test_df = pd.DataFrame({
            'date': pd.date_range('2023-01-01', periods=100),
            'open': [100 + i for i in range(100)],
            'high': [105 + i for i in range(100)],
            'low': [95 + i for i in range(100)],
            'close': [102 + i for i in range(100)],
            'volume': [1000 + i*10 for i in range(100)]
        })
        
        results = []
        for unit in units[:4]:  # 只测试前4个
            result = unit.calculate_score(test_df)
            results.append(result.score)
        
        # 清理引用
        del units
        del test_df
        
        print(f"✅ 资源清理测试完成，处理了 {len(results)} 个结果")
        return True
        
    except Exception as e:
        print(f"❌ 资源清理测试失败: {e}")
        return False

def test_concurrent_access():
    """测试并发访问"""
    print("\n=== 测试并发访问 ===")
    
    try:
        import threading
        from core.scoring_units.mcsi_adapter import MCSIAdapter
        
        # 准备共享测试数据
        test_df = pd.DataFrame({
            'date': pd.date_range('2023-01-01', periods=150),
            'open': [100 + i for i in range(150)],
            'high': [105 + i for i in range(150)],
            'low': [95 + i for i in range(150)],
            'close': [102 + i for i in range(150)],
            'volume': [1000 + i*10 for i in range(150)]
        })
        
        results = []
        errors = []
        
        def worker_thread(thread_id):
            try:
                unit = MCSIAdapter.create_mcsi_macd_unit()
                result = unit.calculate_score(test_df)
                results.append((thread_id, result.score))
            except Exception as e:
                errors.append((thread_id, str(e)))
        
        # 创建5个并发线程
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker_thread, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        print(f"✅ 并发访问测试完成: {len(results)} 成功, {len(errors)} 错误")
        
        # 检查结果一致性
        if len(results) > 1:
            scores = [score for _, score in results]
            if all(abs(score - scores[0]) < 0.01 for score in scores):
                print("✅ 并发计算结果一致")
            else:
                print(f"⚠️  并发计算结果不一致: {scores}")
        
        return len(errors) == 0
        
    except Exception as e:
        print(f"❌ 并发访问测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== Step 2.5: 异常处理和日志记录测试 ===")
    
    # 设置日志
    setup_test_logging()
    
    test_results = []
    
    # 1. 空数据处理
    empty_ok = test_empty_data_handling()
    test_results.append(('空数据处理', empty_ok))
    
    # 2. 无效数据处理
    invalid_ok = test_invalid_data_handling()
    test_results.append(('无效数据处理', invalid_ok))
    
    # 3. 计算错误处理
    calc_ok = test_calculation_errors()
    test_results.append(('计算错误处理', calc_ok))
    
    # 4. 日志功能
    log_ok = test_logging_functionality()
    test_results.append(('日志功能', log_ok))
    
    # 5. 资源清理
    cleanup_ok = test_resource_cleanup()
    test_results.append(('资源清理', cleanup_ok))
    
    # 6. 并发访问
    concurrent_ok = test_concurrent_access()
    test_results.append(('并发访问', concurrent_ok))
    
    # 输出总结
    print("\n=== 异常处理测试总结 ===")
    success_count = 0
    total_count = len(test_results)
    
    for test_name, success in test_results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n总体成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count >= total_count * 0.8:  # 80%通过即算成功
        print("🎉 异常处理测试基本通过！系统具有良好的健壮性。")
    else:
        print("⚠️  异常处理需要改进")

if __name__ == "__main__":
    main()