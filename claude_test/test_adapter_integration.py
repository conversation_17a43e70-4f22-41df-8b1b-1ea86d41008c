#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试适配器集成新的统一接口
Step 2.1 验证
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))

try:
    from core.scoring_units.mcsi_adapter import MCSIAdapter
except ImportError as e:
    print(f"导入适配器失败: {e}")
    sys.exit(1)

def test_adapter_integration():
    """测试适配器是否正确集成统一接口"""
    print("=== 测试适配器集成统一接口 ===")
    
    # 加载测试数据
    csv_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'stock_data', 'cnindex_000001_上证指数.csv')
    df = pd.read_csv(csv_path).tail(150)  # 使用150行数据测试
    
    print(f"测试数据: {len(df)} 行")
    
    # 1. 测试MCSI MACD适配器
    print("\n=== 测试MCSI MACD适配器 ===")
    try:
        macd_unit = MCSIAdapter.create_mcsi_macd_unit()
        result = macd_unit.calculate_score(df)
        
        print(f"MACD分数: {result.score}")
        print(f"信号: {result.signal}")
        print(f"置信度: {result.confidence}")
        print(f"使用单元类型: {result.metadata.get('unit_type', 'unknown')}")
        
        if result.metadata.get('unit_type') == 'standard':
            print("✅ MACD适配器正确使用统一接口")
        else:
            print("⚠️  MACD适配器未使用统一接口")
            
    except Exception as e:
        print(f"❌ MACD适配器测试失败: {e}")
    
    # 2. 测试MCSI MMT适配器
    print("\n=== 测试MCSI MMT适配器 ===")
    try:
        mmt_unit = MCSIAdapter.create_mcsi_mmt_unit()
        result = mmt_unit.calculate_score(df)
        
        print(f"MMT分数: {result.score}")
        print(f"信号: {result.signal}")
        print(f"置信度: {result.confidence}")
        print(f"使用单元类型: {result.metadata.get('unit_type', 'unknown')}")
        
        if result.metadata.get('unit_type') == 'standard':
            print("✅ MMT适配器正确使用统一接口")
        else:
            print("⚠️  MMT适配器未使用统一接口")
            
    except Exception as e:
        print(f"❌ MMT适配器测试失败: {e}")
    
    # 3. 测试MCSI RSI适配器
    print("\n=== 测试MCSI RSI适配器 ===")
    try:
        rsi_unit = MCSIAdapter.create_mcsi_rsi_unit()
        result = rsi_unit.calculate_score(df)
        
        print(f"RSI分数: {result.score}")
        print(f"信号: {result.signal}")
        print(f"置信度: {result.confidence}")
        print(f"使用单元类型: {result.metadata.get('unit_type', 'unknown')}")
        
        if result.metadata.get('unit_type') == 'standard':
            print("✅ RSI适配器正确使用统一接口")
        else:
            print("⚠️  RSI适配器未使用统一接口")
            
    except Exception as e:
        print(f"❌ RSI适配器测试失败: {e}")
    
    # 4. 测试MCSI TTM适配器
    print("\n=== 测试MCSI TTM适配器 ===")
    try:
        ttm_unit = MCSIAdapter.create_mcsi_ttm_unit()
        result = ttm_unit.calculate_score(df)
        
        print(f"TTM分数: {result.score}")
        print(f"信号: {result.signal}")
        print(f"置信度: {result.confidence}")
        print(f"使用单元类型: {result.metadata.get('unit_type', 'unknown')}")
        
        if result.metadata.get('unit_type') == 'standard':
            print("✅ TTM适配器正确使用统一接口")
        else:
            print("⚠️  TTM适配器未使用统一接口")
            
    except Exception as e:
        print(f"❌ TTM适配器测试失败: {e}")
    
    print("\n=== 适配器集成测试完成 ===")

if __name__ == "__main__":
    test_adapter_integration()