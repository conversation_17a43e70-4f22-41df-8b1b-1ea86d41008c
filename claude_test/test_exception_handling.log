[2025-08-20 09:08:09,806] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 09:08:09,807] INFO - core.scoring_units.base_scoring_unit.mcsi_macd - ✅ 成功加载MACD源代码实现
[2025-08-20 09:08:09,807] WARNING - core.scoring_units.base_scoring_unit.mcsi_macd - 数据长度不足: 需要49行，实际5行
[2025-08-20 09:08:09,807] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 09:08:09,807] INFO - core.scoring_units.base_scoring_unit.mcsi_macd - ✅ 成功加载MACD源代码实现
[2025-08-20 09:08:09,810] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 09:08:09,810] INFO - core.scoring_units.base_scoring_unit.mcsi_macd - ✅ 成功加载MACD源代码实现
[2025-08-20 09:08:09,810] WARNING - core.scoring_units.base_scoring_unit.mcsi_macd - 收盘价包含非正值
[2025-08-20 09:08:09,810] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MMT混合输入包装器
[2025-08-20 09:08:09,811] INFO - core.scoring_units.base_scoring_unit.mcsi_mmt - ✅ 成功加载MMT源代码实现
[2025-08-20 09:08:09,811] WARNING - core.scoring_units.base_scoring_unit.mcsi_mmt - close包含非正值
[2025-08-20 09:08:09,811] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI RSI混合输入包装器
[2025-08-20 09:08:09,811] INFO - core.scoring_units.base_scoring_unit.mcsi_rsi - ✅ 成功加载RSI源代码实现
[2025-08-20 09:08:09,812] WARNING - core.scoring_units.base_scoring_unit.mcsi_rsi - 收盘价包含非正值
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI TTM混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.base_scoring_unit.mcsi_ttm - ✅ 成功加载TTM源代码实现
[2025-08-20 09:08:09,812] WARNING - core.scoring_units.base_scoring_unit.mcsi_ttm - 收盘价包含非正值
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_macd_scoring - 测试MCSI MACD日志信息
[2025-08-20 09:08:09,812] WARNING - core.scoring_units.mcsi_macd_scoring - 测试MCSI MACD警告信息
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - 测试适配器日志信息
[2025-08-20 09:08:09,812] ERROR - core.scoring_units.mcsi_macd_scoring - 捕获测试异常: 测试异常日志
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MMT混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI RSI混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI TTM混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MMT混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI RSI混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI TTM混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MMT混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI RSI混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI TTM混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MMT混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI RSI混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI TTM混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MMT混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI RSI混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI TTM混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MMT混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI RSI混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI TTM混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MMT混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI RSI混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI TTM混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MMT混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI RSI混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI TTM混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MMT混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI RSI混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI TTM混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MMT混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI RSI混合输入包装器
[2025-08-20 09:08:09,812] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI TTM混合输入包装器
[2025-08-20 09:08:09,813] INFO - core.scoring_units.base_scoring_unit.mcsi_macd - ✅ 成功加载MACD源代码实现
[2025-08-20 09:08:09,814] INFO - core.scoring_units.base_scoring_unit.mcsi_mmt - ✅ 成功加载MMT源代码实现
[2025-08-20 09:08:09,819] INFO - core.scoring_units.base_scoring_unit.mcsi_rsi - ✅ 成功加载RSI源代码实现
[2025-08-20 09:08:09,820] INFO - core.scoring_units.base_scoring_unit.mcsi_ttm - ✅ 成功加载TTM源代码实现
[2025-08-20 09:08:09,821] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 09:08:09,821] INFO - core.scoring_units.base_scoring_unit.mcsi_macd - ✅ 成功加载MACD源代码实现
[2025-08-20 09:08:09,821] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 09:08:09,823] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 09:08:09,823] INFO - core.scoring_units.base_scoring_unit.mcsi_macd - ✅ 成功加载MACD源代码实现
[2025-08-20 09:08:09,823] INFO - core.scoring_units.base_scoring_unit.mcsi_macd - ✅ 成功加载MACD源代码实现
[2025-08-20 09:08:09,828] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 09:08:09,833] INFO - core.scoring_units.mcsi_adapter - ✅ 创建MCSI MACD混合输入包装器
[2025-08-20 09:08:09,833] INFO - core.scoring_units.base_scoring_unit.mcsi_macd - ✅ 成功加载MACD源代码实现
[2025-08-20 09:08:09,833] INFO - core.scoring_units.base_scoring_unit.mcsi_macd - ✅ 成功加载MACD源代码实现
