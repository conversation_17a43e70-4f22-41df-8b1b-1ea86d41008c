#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试MCSI MMT不一致问题
"""

import sys
import os
import json
import numpy as np
import pandas as pd

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'TV-code', 'py-code'))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))

from mcsi_mmt import MCSIMMTIndicator
from core.scoring_units.mcsi_mmt_scoring import MCSIMMTScoringUnit

def debug_mmt_inconsistency():
    print("=== 调试MCSI MMT不一致问题 ===")
    
    # 加载数据
    csv_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'stock_data', 'cnindex_000001_上证指数.csv')
    df = pd.read_csv(csv_path).tail(200)
    
    # 加载基准分数
    with open('baseline_scores.json', 'r') as f:
        baseline_data = json.load(f)
    baseline_mmt = baseline_data['scores']['mcsi_mmt']['values']
    
    print(f"数据行数: {len(df)}")
    print(f"基准分数: {len(baseline_mmt)} 个")
    
    # 1. 权威版本（基准生成方式）
    print("\n=== 权威版本计算 ===")
    mmt_indicator = MCSIMMTIndicator()
    
    close_prices = df['close'].values
    high_prices = df['high'].values  
    low_prices = df['low'].values
    
    authority_scores = []
    window_size = 100
    
    for i in range(window_size, len(close_prices)):
        window_close = close_prices[max(0, i-window_size):i+1]
        window_high = high_prices[max(0, i-window_size):i+1]
        window_low = low_prices[max(0, i-window_size):i+1]
        result = mmt_indicator.calculate(window_close, window_high, window_low)
        score = result.get('mmt_score', 0)[-1] if hasattr(result.get('mmt_score', 0), '__getitem__') else result.get('mmt_score', 0)
        authority_scores.append(float(score) if not np.isnan(score) else 0.0)
    
    print(f"权威版本分数: {len(authority_scores)} 个")
    print(f"前10个分数: {authority_scores[:10]}")
    print(f"基准前10个: {baseline_mmt[:10]}")
    
    # 2. 统一接口版本
    print("\n=== 统一接口计算 ===")
    mmt_unit = MCSIMMTScoringUnit()
    
    unified_scores = []
    for i in range(window_size, len(df)):
        window_data = df.iloc[max(0, i-window_size):i+1]
        result = mmt_unit.calculate_score(window_data)
        unified_scores.append(result.score)
    
    print(f"统一接口分数: {len(unified_scores)} 个")
    print(f"前10个分数: {unified_scores[:10]}")
    
    # 3. 比较差异
    print("\n=== 差异分析 ===")
    if len(baseline_mmt) == len(unified_scores):
        differences = np.array(baseline_mmt) - np.array(unified_scores)
        max_diff = np.max(np.abs(differences))
        print(f"最大差异: {max_diff}")
        print(f"平均差异: {np.mean(np.abs(differences))}")
        
        # 找出差异最大的几个点
        diff_indices = np.argsort(np.abs(differences))[-5:]
        print(f"\n差异最大的5个点:")
        for idx in diff_indices:
            print(f"  点{idx}: 基准={baseline_mmt[idx]}, 统一接口={unified_scores[idx]}, 差异={differences[idx]}")
    
    # 4. 检查参数是否一致
    print(f"\n=== 参数检查 ===")
    print(f"权威版本参数: leveling={mmt_indicator.leveling}, cyclic_memory={mmt_indicator.cyclic_memory}")
    print(f"统一接口参数: leveling={mmt_unit.leveling}, cyclic_memory={mmt_unit.cyclic_memory}")

if __name__ == "__main__":
    debug_mmt_inconsistency()