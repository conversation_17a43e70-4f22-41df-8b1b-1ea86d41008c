#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版中国指数数据库检查脚本
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import pandas as pd

# 数据库连接配置
DB_CONFIG = {
    'host': '***********',
    'port': 5433,
    'database': 'fintech_db',
    'user': 'postgres',
    'password': 'robot2025'
}

def main():
    print("🔍 中国指数数据库检查")
    print("=" * 50)
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # 1. 查找相关表名
        print("\n📊 查找中国指数相关表...")
        cursor.execute("""
            SELECT table_name, table_schema
            FROM information_schema.tables 
            WHERE table_type = 'BASE TABLE' 
            AND table_schema = 'public'
            AND (
                table_name ILIKE '%cnindex%' OR 
                table_name ILIKE '%中国%' OR 
                table_name ILIKE '%上证%' OR 
                table_name ILIKE '%深证%' OR
                table_name ILIKE '%index%'
            )
            ORDER BY table_name;
        """)
        
        tables = cursor.fetchall()
        
        print(f"\n找到 {len(tables)} 个相关表:")
        for i, table in enumerate(tables, 1):
            print(f"{i}. {table['table_schema']}.{table['table_name']}")
        
        # 2. 分析每个表
        for table in tables:
            table_name = table['table_name']
            table_schema = table['table_schema']
            full_name = f'"{table_schema}"."{table_name}"'
            
            print(f"\n{'='*60}")
            print(f"📈 表: {table_name}")
            print(f"{'='*60}")
            
            # 获取表结构
            cursor.execute(f"""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns 
                WHERE table_schema = %s AND table_name = %s
                ORDER BY ordinal_position;
            """, (table_schema, table_name))
            
            columns = cursor.fetchall()
            print(f"\n🏗️ 表结构 ({len(columns)} 个字段):")
            for col in columns:
                nullable = "可空" if col['is_nullable'] == 'YES' else "必填"
                print(f"  - {col['column_name']}: {col['data_type']} ({nullable})")
            
            # 获取数据量
            try:
                cursor.execute(f"SELECT COUNT(*) as count FROM {full_name}")
                count_result = cursor.fetchone()
                row_count = count_result['count'] if count_result else 0
                print(f"\n📊 数据量: {row_count:,} 行")
                
                # 如果有数据，显示示例
                if row_count > 0:
                    cursor.execute(f"SELECT * FROM {full_name} LIMIT 3")
                    sample_data = cursor.fetchall()
                    
                    print(f"\n📋 示例数据 (前3行):")
                    if sample_data:
                        df = pd.DataFrame(sample_data)
                        pd.set_option('display.max_columns', None)
                        pd.set_option('display.width', None)
                        pd.set_option('display.max_colwidth', 20)
                        print(df.to_string(index=False))
                
            except Exception as e:
                print(f"⚠️ 查询数据时出错: {e}")
        
        cursor.close()
        conn.close()
        
        print(f"\n✅ 检查完成!")
        
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()