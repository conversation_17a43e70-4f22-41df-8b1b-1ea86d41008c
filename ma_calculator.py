#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动平均线计算器
实现5日、20日、50日、200日移动平均线计算功能
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, Optional
from config import MA_PERIODS

class MACalculator:
    """移动平均线计算器"""
    
    def __init__(self):
        self.periods = MA_PERIODS
        self.logger = logging.getLogger(__name__)
    
    def calculate_ma(self, prices: pd.Series, period: int) -> pd.Series:
        """计算简单移动平均线"""
        try:
            return prices.rolling(window=period, min_periods=period).mean()
        except Exception as e:
            self.logger.error(f"计算 {period} 日移动平均线失败: {str(e)}")
            return pd.Series(dtype=float)
    
    def calculate_all_ma(self, df: pd.DataFrame) -> Dict[str, float]:
        """计算所有移动平均线的最新值"""
        if df.empty or 'close' not in df.columns:
            self.logger.warning("数据为空或缺少收盘价列")
            return {}
        
        # 确保数据按时间排序
        if 'timestamp' in df.columns:
            df = df.sort_values('timestamp')
        
        close_prices = df['close']
        
        # 计算各期移动平均线
        ma_results = {}
        
        try:
            # 5日线 (周线)
            ma5 = self.calculate_ma(close_prices, self.periods['short'])
            ma_results['ma5'] = ma5.iloc[-1] if not ma5.empty and not pd.isna(ma5.iloc[-1]) else None
            
            # 20日线 (月线)
            ma20 = self.calculate_ma(close_prices, self.periods['medium'])
            ma_results['ma20'] = ma20.iloc[-1] if not ma20.empty and not pd.isna(ma20.iloc[-1]) else None
            
            # 50日线 (季度线)
            ma50 = self.calculate_ma(close_prices, self.periods['long'])
            ma_results['ma50'] = ma50.iloc[-1] if not ma50.empty and not pd.isna(ma50.iloc[-1]) else None
            
            # 200日线 (年线)
            ma200 = self.calculate_ma(close_prices, self.periods['ultra_long'])
            ma_results['ma200'] = ma200.iloc[-1] if not ma200.empty and not pd.isna(ma200.iloc[-1]) else None
            
            # 当前价格
            ma_results['current_price'] = close_prices.iloc[-1] if not close_prices.empty else None
            
            # 数据日期
            if 'timestamp' in df.columns:
                ma_results['date'] = df['timestamp'].iloc[-1]
            
            self.logger.debug(f"计算移动平均线完成: {ma_results}")
            return ma_results
            
        except Exception as e:
            self.logger.error(f"计算移动平均线失败: {str(e)}")
            return {}
    
    def get_ma_trend_info(self, ma_data: Dict[str, float]) -> Dict[str, str]:
        """获取移动平均线趋势信息"""
        if not ma_data:
            return {}
        
        trend_info = {}
        
        try:
            current_price = ma_data.get('current_price')
            ma5 = ma_data.get('ma5')
            ma20 = ma_data.get('ma20')
            ma50 = ma_data.get('ma50')
            ma200 = ma_data.get('ma200')
            
            # 价格与各均线的关系
            if current_price and ma5:
                trend_info['price_vs_ma5'] = "上方" if current_price > ma5 else "下方"
            
            if current_price and ma20:
                trend_info['price_vs_ma20'] = "上方" if current_price > ma20 else "下方"
            
            if current_price and ma50:
                trend_info['price_vs_ma50'] = "上方" if current_price > ma50 else "下方"
            
            if current_price and ma200:
                trend_info['price_vs_ma200'] = "上方" if current_price > ma200 else "下方"
            
            # 均线之间的关系
            if ma5 and ma20:
                trend_info['ma5_vs_ma20'] = "上方" if ma5 > ma20 else "下方"
            
            if ma20 and ma50:
                trend_info['ma20_vs_ma50'] = "上方" if ma20 > ma50 else "下方"
            
            if ma50 and ma200:
                trend_info['ma50_vs_ma200'] = "上方" if ma50 > ma200 else "下方"
            
            # 整体趋势判断
            if ma5 and ma20 and ma50 and ma200:
                if ma5 > ma20 > ma50 > ma200:
                    trend_info['overall_trend'] = "强势上升"
                elif ma5 > ma20 > ma50:
                    trend_info['overall_trend'] = "上升趋势"
                elif ma5 > ma20:
                    trend_info['overall_trend'] = "短期上升"
                elif ma5 < ma20 < ma50 < ma200:
                    trend_info['overall_trend'] = "强势下降"
                elif ma5 < ma20 < ma50:
                    trend_info['overall_trend'] = "下降趋势"
                elif ma5 < ma20:
                    trend_info['overall_trend'] = "短期下降"
                else:
                    trend_info['overall_trend'] = "震荡整理"
            
            return trend_info
            
        except Exception as e:
            self.logger.error(f"获取趋势信息失败: {str(e)}")
            return {}
    
    def validate_ma_data(self, ma_data: Dict[str, float]) -> bool:
        """验证移动平均线数据的有效性"""
        required_fields = ['ma5', 'ma20', 'ma50', 'ma200', 'current_price']
        
        for field in required_fields:
            if field not in ma_data or ma_data[field] is None:
                self.logger.warning(f"移动平均线数据缺少字段: {field}")
                return False
        
        return True
    
    def get_ma_summary(self, ma_data: Dict[str, float]) -> str:
        """获取移动平均线摘要信息"""
        if not self.validate_ma_data(ma_data):
            return "数据不完整"
        
        try:
            current = ma_data['current_price']
            ma5 = ma_data['ma5']
            ma20 = ma_data['ma20']
            ma50 = ma_data['ma50']
            ma200 = ma_data['ma200']
            
            summary = f"当前价格: {current:.2f}, "
            summary += f"MA5: {ma5:.2f}, "
            summary += f"MA20: {ma20:.2f}, "
            summary += f"MA50: {ma50:.2f}, "
            summary += f"MA200: {ma200:.2f}"
            
            return summary
            
        except Exception as e:
            self.logger.error(f"生成摘要失败: {str(e)}")
            return "摘要生成失败"
