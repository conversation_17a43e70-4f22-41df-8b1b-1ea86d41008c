#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库样本数据收集器
用于获取数据库结构和样本数据，供后续开发参考
"""

import psycopg2
import pandas as pd
import json
from datetime import datetime
import sys
import os

# 添加core目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))

# 使用现有的数据库配置
DB_CONFIG = {
    'host': '***********',
    'port': 5433,
    'database': 'fintech_db',
    'user': 'postgres',
    'password': 'robot2025'
}

class DatabaseSampleCollector:
    """数据库样本数据收集器"""
    
    def __init__(self):
        self.conn = None
        self.samples = {}
        
    def connect(self):
        """连接数据库"""
        try:
            self.conn = psycopg2.connect(**DB_CONFIG)
            print(f"✓ 成功连接到数据库: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")
            return True
        except Exception as e:
            print(f"✗ 数据库连接失败: {e}")
            return False
    
    def get_database_schema(self):
        """获取数据库结构信息"""
        if not self.conn:
            return None
            
        try:
            cursor = self.conn.cursor()
            
            # 获取所有表信息
            cursor.execute("""
                SELECT 
                    t.table_name,
                    t.table_type,
                    obj_description(c.oid) as table_comment
                FROM information_schema.tables t
                LEFT JOIN pg_class c ON c.relname = t.table_name
                WHERE t.table_schema = 'public'
                AND t.table_type = 'BASE TABLE'
                ORDER BY t.table_name;
            """)
            
            tables = cursor.fetchall()
            
            schema_info = {
                'database_name': DB_CONFIG['database'],
                'total_tables': len(tables),
                'tables': {}
            }
            
            for table_name, table_type, table_comment in tables:
                # 获取每个表的列信息
                cursor.execute("""
                    SELECT 
                        column_name,
                        data_type,
                        is_nullable,
                        column_default,
                        character_maximum_length
                    FROM information_schema.columns
                    WHERE table_name = %s
                    AND table_schema = 'public'
                    ORDER BY ordinal_position;
                """, (table_name,))
                
                columns = cursor.fetchall()
                
                # 获取表的记录数
                try:
                    cursor.execute(f'SELECT COUNT(*) FROM "{table_name}";')
                    row_count = cursor.fetchone()[0]
                except:
                    row_count = 0
                
                schema_info['tables'][table_name] = {
                    'type': table_type,
                    'comment': table_comment,
                    'row_count': row_count,
                    'columns': [
                        {
                            'name': col[0],
                            'type': col[1],
                            'nullable': col[2],
                            'default': col[3],
                            'max_length': col[4]
                        } for col in columns
                    ]
                }
            
            cursor.close()
            return schema_info
            
        except Exception as e:
            print(f"获取数据库结构失败: {e}")
            return None
    
    def get_sample_data(self, table_name, limit=5):
        """获取表的样本数据"""
        if not self.conn:
            return None
            
        try:
            cursor = self.conn.cursor()
            
            # 获取样本数据
            cursor.execute(f'SELECT * FROM "{table_name}" ORDER BY RANDOM() LIMIT %s;', (limit,))
            rows = cursor.fetchall()
            
            # 获取列名
            cursor.execute("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = %s
                AND table_schema = 'public'
                ORDER BY ordinal_position;
            """, (table_name,))
            
            columns = [row[0] for row in cursor.fetchall()]
            
            cursor.close()
            
            # 转换为字典格式
            sample_data = []
            for row in rows:
                sample_data.append(dict(zip(columns, [str(val) if val is not None else None for val in row])))
            
            return {
                'table_name': table_name,
                'columns': columns,
                'sample_count': len(sample_data),
                'data': sample_data
            }
            
        except Exception as e:
            print(f"获取表 {table_name} 样本数据失败: {e}")
            return None
    
    def collect_all_samples(self):
        """收集所有表的样本数据"""
        if not self.connect():
            return False
        
        print("正在收集数据库结构信息...")
        schema = self.get_database_schema()
        
        if not schema:
            print("无法获取数据库结构")
            return False
        
        print(f"发现 {schema['total_tables']} 个数据表")
        
        self.samples = {
            'collection_time': datetime.now().isoformat(),
            'database_info': {
                'host': DB_CONFIG['host'],
                'port': DB_CONFIG['port'],
                'database': DB_CONFIG['database']
            },
            'schema': schema,
            'sample_data': {}
        }
        
        # 收集每个表的样本数据
        for table_name, table_info in schema['tables'].items():
            print(f"正在收集表 {table_name} 的样本数据 (共{table_info['row_count']}条记录)...")
            
            sample = self.get_sample_data(table_name, limit=3)
            if sample:
                self.samples['sample_data'][table_name] = sample
                print(f"  ✓ 获取到 {sample['sample_count']} 条样本数据")
            else:
                print(f"  ✗ 无法获取样本数据")
        
        return True
    
    def save_samples(self, output_file='database_samples.json'):
        """保存样本数据到文件"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.samples, f, indent=2, ensure_ascii=False, default=str)
            print(f"✓ 样本数据已保存到: {output_file}")
            return True
        except Exception as e:
            print(f"✗ 保存样本数据失败: {e}")
            return False
    
    def generate_summary_report(self):
        """生成数据库摘要报告"""
        if not self.samples:
            return "没有可用的样本数据"
        
        schema = self.samples['schema']
        report = []
        
        report.append("=" * 60)
        report.append(f"数据库样本数据收集报告")
        report.append("=" * 60)
        report.append(f"收集时间: {self.samples['collection_time']}")
        report.append(f"数据库: {schema['database_name']}")
        report.append(f"表总数: {schema['total_tables']}")
        report.append("")
        
        report.append("表结构概览:")
        report.append("-" * 40)
        
        for table_name, table_info in schema['tables'].items():
            report.append(f"表名: {table_name}")
            report.append(f"  记录数: {table_info['row_count']:,}")
            report.append(f"  字段数: {len(table_info['columns'])}")
            report.append(f"  字段: {', '.join([col['name'] for col in table_info['columns'][:5]])}")
            if len(table_info['columns']) > 5:
                report.append(f"       ...等共{len(table_info['columns'])}个字段")
            report.append("")
        
        return "\n".join(report)
    
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            print("数据库连接已关闭")

def main():
    """主函数"""
    collector = DatabaseSampleCollector()
    
    try:
        print("开始收集数据库样本数据...")
        print("=" * 50)
        
        if collector.collect_all_samples():
            # 保存到claude_test目录
            output_file = os.path.join(os.path.dirname(__file__), 'database_samples.json')
            collector.save_samples(output_file)
            
            # 生成摘要报告
            summary_file = os.path.join(os.path.dirname(__file__), 'database_summary.txt')
            summary = collector.generate_summary_report()
            
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write(summary)
            print(f"✓ 摘要报告已保存到: {summary_file}")
            
            print("\n" + summary)
            
        else:
            print("样本数据收集失败")
            
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        collector.close()

if __name__ == "__main__":
    main()