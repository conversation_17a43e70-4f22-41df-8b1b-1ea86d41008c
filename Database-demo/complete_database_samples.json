{"database_info": {"database_name": "fintech_db", "port": 5433, "total_tables": 88, "data_tables": 87, "management_tables": 1, "analysis_date": "2025-08-19", "earliest_data": "1990-12-19", "latest_data": "2025-08-19"}, "market_categories": {"ashares": {"description": "A股股票数据", "count": 48, "representative_stocks": ["ashares_600519_贵州茅台", "ashares_300750_宁德时代", "ashares_002594_比亚迪", "ashares_688981_中芯国际", "ashares_600036_招商银行"]}, "crypto": {"description": "加密货币数据", "count": 19, "major_cryptocurrencies": ["crypto_btc_usdt_比特币", "crypto_eth_usdt_eth", "crypto_bnb_usdt_bnb", "crypto_sol_usdt_sol", "crypto_xrp_usdt_xrp"]}, "other": {"description": "其他资产(申万行业指数, 美股指数)", "count": 9, "subcategories": {"shenwan_industries": ["other_801010_si_申万农林牧渔", "other_801030_si_申万基础化工", "other_801150_si_申万医药生物"], "us_indices": ["other__dji_道琼斯工业平均指数", "other__spx_s_p_500指数", "other__ixic_美国100现金cfd"]}}, "usstocks": {"description": "美股个股数据", "count": 4, "stocks": ["usstocks_aihs_森淼科技", "usstocks_crcl_circle_internet_group_inc_a"]}, "commodity": {"description": "大宗商品数据", "count": 3, "commodities": ["commodity_xauusd_现货黄金", "commodity_xagusd_现货白银", "commodity_clusd_原油"]}, "cnindex": {"description": "中国指数数据", "count": 2, "indices": ["cnindex_000001_上证指数", "cnindex_399006_创业板指"]}, "etf": {"description": "ETF基金数据", "count": 1, "funds": ["etf_sz159993_证券etf龙头"]}, "hkstocks": {"description": "港股数据", "count": 1, "stocks": ["hkstocks_01129_中国水业集团"]}}, "table_structure": {"standard_ohlcv_schema": {"id": {"type": "integer", "constraints": "PRIMARY KEY, AUTO_INCREMENT", "description": "唯一标识符"}, "timestamp": {"type": "timestamp without time zone", "constraints": "NOT NULL", "description": "交易日期时间戳"}, "period": {"type": "character varying", "constraints": "NOT NULL, DEFAULT 'daily'", "description": "时间周期(daily/weekly/monthly)"}, "open": {"type": "numeric", "constraints": "NULLABLE", "description": "开盘价"}, "high": {"type": "numeric", "constraints": "NULLABLE", "description": "最高价"}, "low": {"type": "numeric", "constraints": "NULLABLE", "description": "最低价"}, "close": {"type": "numeric", "constraints": "NULLABLE", "description": "收盘价"}, "volume": {"type": "numeric", "constraints": "DEFAULT 0", "description": "成交量"}, "created_at": {"type": "timestamp without time zone", "constraints": "DEFAULT CURRENT_TIMESTAMP", "description": "记录创建时间"}}}, "multi_timeframe_analysis": {"cnindex_000001_上证指数": {"daily": {"count": 8465, "date_range": "1990-12-19 到 2025-08-19", "years_covered": 34.7}, "weekly": {"count": 1760, "date_range": "1990-12-21 到 2025-08-19", "usage": "MCSI-RSI真实周线数据分析"}, "monthly": {"count": 415, "date_range": "1990-12-31 到 2025-06-30", "usage": "长期趋势分析"}}}, "data_quality_metrics": {"price_correlation": {"close_correlation": 0.57456106, "high_correlation": 0.5838671, "low_correlation": 0.5642538, "open_correlation": 0.5739459}, "data_uniqueness": {"close_distinct_ratio": 0.78410697, "high_distinct_ratio": 0.7839187, "low_distinct_ratio": 0.78448355, "volume_distinct_ratio": 0.99764615}, "temporal_characteristics": {"timestamp_correlation": 0.6652178, "period_correlation": 0.9920689, "volume_correlation": 0.80482656}}, "sample_data_records": {"cnindex_000001_上证指数_recent_daily": [{"timestamp": "2025-08-19T00:00:00.000Z", "period": "daily", "open": "3728.490", "high": "3746.670", "low": "3718.150", "close": "3727.290", "volume": "696099750.00000000"}, {"timestamp": "2025-08-18T00:00:00.000Z", "period": "daily", "open": "3712.500", "high": "3745.940", "low": "3702.380", "close": "3728.030", "volume": "776016669.00000000"}]}, "management_tables": {"gap_attempt_records": {"purpose": "数据完整性管理和缺失数据补充记录", "schema": {"id": "integer PRIMARY KEY", "symbol": "varchar NOT NULL - 资产代码", "category": "varchar NOT NULL - 资产分类", "gap_start_date": "date NOT NULL - 缺失开始日期", "gap_end_date": "date NOT NULL - 缺失结束日期", "data_source": "varchar NOT NULL - 数据来源", "attempt_result": "varchar NOT NULL - 补充尝试结果", "attempt_timestamp": "timestamp - 尝试时间", "error_message": "text - 错误信息", "records_found": "integer - 找到记录数", "metadata": "jsonb - JSON格式元数据"}}}, "mcsi_system_integration": {"data_provider_support": {"period_filtering": "支持按时间周期查询 (daily/weekly/monthly)", "caching_mechanism": "LRU缓存提升查询性能", "fallback_strategy": "数据库优先, CSV文件备用"}, "real_applications": {"mcsi_rsi_weekly": "使用真实周线数据(period='weekly')提升RSI指标准确性", "professional_charts": "日线数据用于专业图表显示", "trend_analysis": "月线数据支持长期趋势分析"}}, "database_naming_convention": {"pattern": "{market}_{code}_{chinese_name}", "examples": {"A股": "ashares_600519_贵州茅台", "美股": "usstocks_aihs_森淼科技", "加密货币": "crypto_btc_usdt_比特币", "指数": "cnindex_000001_上证指数", "商品": "commodity_xauusd_现货黄金"}}, "technical_features": {"precision": "NUMERIC类型确保金融数据精度", "timezone": "统一使用UTC时间, TIMESTAMP WITHOUT TIME ZONE", "indexing": "主键自动索引, timestamp字段适合时序查询", "unicode_support": "完整支持中文资产名称", "extensibility": "支持新市场、新资产类别、新时间周期扩展"}, "optimization_recommendations": {"data_completeness": ["补充加密货币历史数据(部分表数据稀少)", "统一数据更新频率和数据质量监控"], "performance": ["添加复合索引 (timestamp, period) 优化多时间框架查询", "考虑大表数据分区策略", "实施数据压缩减少存储开销"], "functionality": ["添加实时数据更新机制", "实施数据版本管理和血缘追踪", "增加预计算技术指标字段"]}}