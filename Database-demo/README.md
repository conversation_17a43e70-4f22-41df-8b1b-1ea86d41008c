# 金融数据库架构演示与分析

这是AI股票技术分析系统的数据库架构完整演示目录，包含详细的数据库结构分析、样本数据收集和框架说明文档。

## 数据库基本信息

- **数据库**: PostgreSQL fintech_db (端口5433)
- **总表数**: 88个表 (87个金融数据表 + 1个管理表)
- **数据覆盖**: 1990年12月 - 2025年8月 (34.7年历史数据)
- **多时间框架**: 支持daily/weekly/monthly三种时间周期
- **资产覆盖**: A股、美股、港股、加密货币、大宗商品、ETF、指数等全市场资产

## 文件说明

### 📊 核心分析文档
- **`database_framework_analysis.md`**: 数据库架构详细分析报告 (完整无省略)
  - 数据表分类统计 (A股48表, 加密货币19表, 其他20表)
  - 标准OHLCV表结构设计
  - 多时间框架数据分布分析
  - 数据质量指标和统计特征
  - MCSI系统集成说明
  - 优化建议和扩展方向

- **`complete_database_samples.json`**: 完整数据库样本和结构信息
  - 88个表的完整分类和命名规范
  - 标准化表结构schema定义
  - 数据质量指标 (相关性、唯一性、时序特征)
  - 实际样本数据记录
  - MCSI系统集成配置
  - 技术特性和优化建议

### 📈 历史文档 (保留)
- `database_sample_collector.py`: 原始数据库采样收集脚本
- `database_samples.json`: 原始收集的数据库样本数据  
- `database_summary.txt`: 原始数据库概要信息

## 数据库架构亮点

### 🎯 多时间框架设计
```sql
-- 所有表支持三种时间周期
period VARCHAR NOT NULL DEFAULT 'daily'
-- daily: 日线数据 (主要)
-- weekly: 周线数据 (MCSI-RSI真实周线分析)  
-- monthly: 月线数据 (长期趋势)
```

### 🏢 全市场资产覆盖
- **A股股票** (48表): 涵盖科技、医药、新能源、金融、消费等主要行业龙头
- **加密货币** (19表): BTC、ETH等主流币种 + 平台币和新兴币种
- **其他资产** (17表): 申万行业指数、美股指数、大宗商品、ETF等

### 🔧 标准化设计
- **统一OHLCV格式**: 所有表采用相同的开高低收量结构
- **命名规范**: `{market}_{code}_{chinese_name}` 格式
- **精度保证**: NUMERIC类型确保金融数据计算精度
- **时区统一**: UTC时间戳避免时区问题

## MCSI系统集成

### 📊 真实周线数据支持
```python
# MCSI-RSI使用真实周线数据而非重采样
config = {
    'db_conn': '************************************************/fintech_db',
    'symbol': 'cnindex_000001_上证指数', 
    'period': 'weekly'  # 使用数据库真实周线数据
}
```

### 💾 DataProvider智能切换
- **数据库优先**: 高性能PostgreSQL查询
- **CSV备用**: 自动回退到stock_data/目录
- **LRU缓存**: 提升重复查询性能
- **参数化查询**: 防SQL注入攻击

## 使用方法

### 查看完整架构分析
```bash
# 阅读详细架构报告 (无省略, 完整描述)
cat Database-demo/database_framework_analysis.md
```

### 查看结构化数据样本
```bash 
# 查看JSON格式完整样本数据
cat Database-demo/complete_database_samples.json
```

### 运行原始采样脚本 (可选)
```bash
cd Database-demo
python database_sample_collector.py
```

### 使用MCP数据库查询 (推荐)
```python
# 在Claude Code中使用MCP服务直接查询
mcp__fintech-postgres__query("SELECT * FROM cnindex_000001_上证指数 WHERE period = 'weekly' LIMIT 5;")
```

## 技术特色

### 🚀 高性能查询
- 主键索引: 自动优化
- 时序索引: timestamp字段高相关性 (0.67)
- 复合索引潜力: (timestamp, period) 组合查询

### 📈 数据质量保证
- **价格相关性**: 0.57-0.58 (OHLC价格数据高度相关)
- **数据唯一性**: 78-99% (价格和成交量数据差异化明显)
- **时序连续性**: 34.7年连续历史数据 (上证指数)

### 🔄 扩展性设计
- **水平扩展**: 支持新市场、新资产类别
- **垂直扩展**: 支持技术指标预计算、基本面数据
- **时间扩展**: 支持新时间周期 (4H, 1H等)

## 详细统计数据

### 📊 表分类统计
| 市场类别 | 表数量 | 代表性资产 |
|---------|--------|-----------|
| A股股票 | 48 | 贵州茅台, 宁德时代, 中芯国际 |
| 加密货币 | 19 | BTC, ETH, SOL, BNB |
| 申万指数 | 6 | 医药生物, 基础化工, 电子 |
| 美股指数 | 3 | 道琼斯, 标普500, 纳斯达克 |
| 美股个股 | 4 | 森淼科技, Circle |
| 大宗商品 | 3 | 黄金, 白银, 原油 |
| 中国指数 | 2 | 上证指数, 创业板指 |
| ETF基金 | 1 | 证券ETF龙头 |
| 港股 | 1 | 中国水业集团 |

### 📈 数据时间跨度 (上证指数示例)
- **日线数据**: 8,465条 (1990-12-19 至 2025-08-19)
- **周线数据**: 1,760条 (1990-12-21 至 2025-08-19) 
- **月线数据**: 415条 (1990-12-31 至 2025-06-30)

### 🎯 MCSI系统应用场景
- **MCSI-RSI**: 使用真实周线数据提升指标准确性
- **专业图表**: 日线数据用于K线图表显示  
- **长期分析**: 月线数据支持宏观趋势研究

---

**🎉 完整无省略**: 本目录提供金融数据库的完整架构分析，包含所有88个表的详细信息、数据质量指标、MCSI系统集成说明，以及实际样本数据。是理解AI股票分析系统数据基础的完整参考资料。

**更新时间**: 2025-08-19  
**MCP服务**: fintech-postgres  
**数据库版本**: PostgreSQL 13+