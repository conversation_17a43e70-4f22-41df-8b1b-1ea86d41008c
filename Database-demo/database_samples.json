{"collection_time": "2025-08-05T15:07:57.655116", "database_info": {"host": "***********", "port": 5433, "database": "fintech_db"}, "schema": {"database_name": "fintech_db", "total_tables": 45, "tables": {"ashares_300502_新易盛": {"type": "BASE TABLE", "comment": null, "row_count": 2885, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"ashares_300502_新易盛_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "ashares_300584_海辰药业": {"type": "BASE TABLE", "comment": null, "row_count": 2619, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"ashares_300584_海辰药业_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "ashares_600519_贵州茅台": {"type": "BASE TABLE", "comment": null, "row_count": 7227, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"ashares_600519_贵州茅台_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "ashares_871634_新威凌": {"type": "BASE TABLE", "comment": null, "row_count": 0, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"ashares_871634_新威凌_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "ashares_sz300502_新易盛": {"type": "BASE TABLE", "comment": null, "row_count": 2885, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"ashares_sz300502_新易盛_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "cnindex_000001_上证指数": {"type": "BASE TABLE", "comment": null, "row_count": 10628, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"cnindex_000001_上证指数_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "cnindex_399006_创业板指": {"type": "BASE TABLE", "comment": null, "row_count": 4646, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"cnindex_399006_创业板指_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "commodity_clusd_原油": {"type": "BASE TABLE", "comment": null, "row_count": 9616, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"commodity_clusd_原油_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "commodity_xagusd_现货白银": {"type": "BASE TABLE", "comment": null, "row_count": 3243, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"commodity_xagusd_现货白银_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "commodity_xauusd_现货黄金": {"type": "BASE TABLE", "comment": null, "row_count": 3243, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"commodity_xauusd_现货黄金_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "crypto_bnb_usdt_bnb": {"type": "BASE TABLE", "comment": null, "row_count": 88069, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('crypto_bnb_usdt_bnb_id_seq'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "crypto_btc_usdt_btc": {"type": "BASE TABLE", "comment": null, "row_count": 90596, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('crypto_btc_usdt_btc_id_seq'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "crypto_btc_usdt_比特币": {"type": "BASE TABLE", "comment": null, "row_count": 1, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"crypto_btc_usdt_比特币_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "crypto_doge_usdt_doge": {"type": "BASE TABLE", "comment": null, "row_count": 69254, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('crypto_doge_usdt_doge_id_seq'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "crypto_dydx_usdt_dydx": {"type": "BASE TABLE", "comment": null, "row_count": 44463, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('crypto_dydx_usdt_dydx_id_seq'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "crypto_ena_usdt_ena": {"type": "BASE TABLE", "comment": null, "row_count": 15277, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('crypto_ena_usdt_ena_id_seq'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "crypto_eth_usdt_eth": {"type": "BASE TABLE", "comment": null, "row_count": 90584, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('crypto_eth_usdt_eth_id_seq'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "crypto_ldo_usdt_ldo": {"type": "BASE TABLE", "comment": null, "row_count": 35996, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('crypto_ldo_usdt_ldo_id_seq'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "crypto_link_usdt_link": {"type": "BASE TABLE", "comment": null, "row_count": 74538, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('crypto_link_usdt_link_id_seq'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "crypto_ltc_usdt_ltc": {"type": "BASE TABLE", "comment": null, "row_count": 86913, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('crypto_ltc_usdt_ltc_id_seq'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "crypto_near_usdt_near": {"type": "BASE TABLE", "comment": null, "row_count": 54062, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('crypto_near_usdt_near_id_seq'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "crypto_neo_usdt_neo": {"type": "BASE TABLE", "comment": null, "row_count": 87630, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('crypto_neo_usdt_neo_id_seq'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "crypto_ondo_usdt_ondo": {"type": "BASE TABLE", "comment": null, "row_count": 3611, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('crypto_ondo_usdt_ondo_id_seq'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "crypto_sol_usdt_sol": {"type": "BASE TABLE", "comment": null, "row_count": 56724, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('crypto_sol_usdt_sol_id_seq'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "crypto_sui_usdt_sui": {"type": "BASE TABLE", "comment": null, "row_count": 25716, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('crypto_sui_usdt_sui_id_seq'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "crypto_trx_usdt_trx": {"type": "BASE TABLE", "comment": null, "row_count": 80646, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('crypto_trx_usdt_trx_id_seq'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "crypto_uni_usdt_uni": {"type": "BASE TABLE", "comment": null, "row_count": 55575, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('crypto_uni_usdt_uni_id_seq'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "crypto_xlm_usdt_xlm": {"type": "BASE TABLE", "comment": null, "row_count": 80816, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('crypto_xlm_usdt_xlm_id_seq'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "crypto_xrp_usdt_xrp": {"type": "BASE TABLE", "comment": null, "row_count": 82520, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('crypto_xrp_usdt_xrp_id_seq'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "etf_sz159993_证券etf龙头": {"type": "BASE TABLE", "comment": null, "row_count": 1683, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"etf_sz159993_证券etf龙头_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "gap_attempt_records": {"type": "BASE TABLE", "comment": null, "row_count": 0, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('gap_attempt_records_id_seq'::regclass)", "max_length": null}, {"name": "symbol", "type": "character varying", "nullable": "NO", "default": null, "max_length": 50}, {"name": "category", "type": "character varying", "nullable": "NO", "default": null, "max_length": 100}, {"name": "gap_start_date", "type": "date", "nullable": "NO", "default": null, "max_length": null}, {"name": "gap_end_date", "type": "date", "nullable": "NO", "default": null, "max_length": null}, {"name": "data_source", "type": "character varying", "nullable": "NO", "default": null, "max_length": 100}, {"name": "attempt_result", "type": "character varying", "nullable": "NO", "default": null, "max_length": 20}, {"name": "attempt_timestamp", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}, {"name": "error_message", "type": "text", "nullable": "YES", "default": null, "max_length": null}, {"name": "records_found", "type": "integer", "nullable": "YES", "default": "0", "max_length": null}, {"name": "metadata", "type": "jsonb", "nullable": "YES", "default": null, "max_length": null}]}, "hkstocks_01129_中国水业集团": {"type": "BASE TABLE", "comment": null, "row_count": 7089, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"hkstocks_01129_中国水业集团_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "other_801010_si_申万农林牧渔": {"type": "BASE TABLE", "comment": null, "row_count": 2018, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"other_801010_si_申万农林牧渔_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "other_801030_si_申万基础化工": {"type": "BASE TABLE", "comment": null, "row_count": 7787, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"other_801030_si_申万基础化工_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "other_801040_si_申万钢铁": {"type": "BASE TABLE", "comment": null, "row_count": 7787, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"other_801040_si_申万钢铁_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "other_801080_si_申万电子": {"type": "BASE TABLE", "comment": null, "row_count": 7787, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"other_801080_si_申万电子_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "other_801140_si_申万轻工制造": {"type": "BASE TABLE", "comment": null, "row_count": 7787, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"other_801140_si_申万轻工制造_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "other_801150_si_申万医药生物": {"type": "BASE TABLE", "comment": null, "row_count": 7787, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"other_801150_si_申万医药生物_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "other__dji_道琼斯工业平均指数": {"type": "BASE TABLE", "comment": null, "row_count": 6819, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"other__dji_道琼斯工业平均指数_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "other__ixic_美国100现金cfd": {"type": "BASE TABLE", "comment": null, "row_count": 6817, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"other__ixic_美国100现金cfd_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "other__spx_s_p_500指数": {"type": "BASE TABLE", "comment": null, "row_count": 6820, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"other__spx_s_p_500指数_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "usstocks_aihs_森淼科技": {"type": "BASE TABLE", "comment": null, "row_count": 2329, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('\"usstocks_aihs_森淼科技_id_seq\"'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "usstocks_crcl_circle_internet_group_inc_a": {"type": "BASE TABLE", "comment": null, "row_count": 52, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('usstocks_crcl_circle_internet_group_inc_a_id_seq'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "usstocks_cycu_cycurion_inc": {"type": "BASE TABLE", "comment": null, "row_count": 0, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('usstocks_cycu_cycurion_inc_id_seq'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}, "usstocks_limnw_liminatus_pharma_inc_wt": {"type": "BASE TABLE", "comment": null, "row_count": 0, "columns": [{"name": "id", "type": "integer", "nullable": "NO", "default": "nextval('usstocks_limnw_liminatus_pharma_inc_wt_id_seq'::regclass)", "max_length": null}, {"name": "timestamp", "type": "timestamp without time zone", "nullable": "NO", "default": null, "max_length": null}, {"name": "period", "type": "character varying", "nullable": "NO", "default": "'daily'::character varying", "max_length": 20}, {"name": "open", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "high", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "low", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "close", "type": "numeric", "nullable": "YES", "default": null, "max_length": null}, {"name": "volume", "type": "numeric", "nullable": "YES", "default": "0", "max_length": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": "YES", "default": "CURRENT_TIMESTAMP", "max_length": null}]}}}, "sample_data": {"ashares_300502_新易盛": {"table_name": "ashares_300502_新易盛", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "1619", "timestamp": "2022-10-31 00:00:00", "period": "daily", "open": "12.080", "high": "12.820", "low": "12.000", "close": "12.700", "volume": "250076.00000000", "created_at": "2025-07-29 12:47:57.257662"}, {"id": "928", "timestamp": "2019-12-20 00:00:00", "period": "daily", "open": "10.410", "high": "10.510", "low": "10.060", "close": "10.140", "volume": "100153.00000000", "created_at": "2025-07-29 12:47:57.257662"}, {"id": "945", "timestamp": "2020-01-15 00:00:00", "period": "daily", "open": "10.180", "high": "10.450", "low": "10.060", "close": "10.380", "volume": "64025.00000000", "created_at": "2025-07-29 12:47:57.257662"}]}, "ashares_300584_海辰药业": {"table_name": "ashares_300584_海辰药业", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "1549", "timestamp": "2023-05-30 00:00:00", "period": "daily", "open": "25.150", "high": "25.550", "low": "24.880", "close": "25.550", "volume": "22152.00000000", "created_at": "2025-07-28 08:47:40.869289"}, {"id": "383", "timestamp": "2018-08-07 00:00:00", "period": "daily", "open": "30.540", "high": "31.680", "low": "30.280", "close": "31.640", "volume": "9935.00000000", "created_at": "2025-07-28 08:47:40.869289"}, {"id": "1127", "timestamp": "2021-08-27 00:00:00", "period": "daily", "open": "16.780", "high": "16.880", "low": "16.630", "close": "16.690", "volume": "4904.00000000", "created_at": "2025-07-28 08:47:40.869289"}]}, "ashares_600519_贵州茅台": {"table_name": "ashares_600519_贵州茅台", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "5364", "timestamp": "2024-01-22 00:00:00", "period": "daily", "open": "1541.160", "high": "1554.410", "low": "1531.590", "close": "1553.460", "volume": "4694589.00000000", "created_at": "2025-07-28 08:47:54.254265"}, {"id": "4264", "timestamp": "2019-07-15 00:00:00", "period": "daily", "open": "852.790", "high": "863.630", "low": "838.100", "close": "859.950", "volume": "5987040.00000000", "created_at": "2025-07-28 08:47:54.254265"}, {"id": "4280", "timestamp": "2019-08-06 00:00:00", "period": "daily", "open": "820.360", "high": "835.340", "low": "814.020", "close": "833.840", "volume": "4399116.00000000", "created_at": "2025-07-28 08:47:54.254265"}]}, "ashares_871634_新威凌": {"table_name": "ashares_871634_新威凌", "columns": ["id", "timestamp", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 0, "data": []}, "ashares_sz300502_新易盛": {"table_name": "ashares_sz300502_新易盛", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "478", "timestamp": "2018-02-12 00:00:00", "period": "daily", "open": "5.170", "high": "5.330", "low": "5.120", "close": "5.290", "volume": "35716.00000000", "created_at": "2025-07-29 12:49:45.577145"}, {"id": "102", "timestamp": "2016-08-01 00:00:00", "period": "daily", "open": "8.090", "high": "8.160", "low": "7.450", "close": "7.970", "volume": "19383.00000000", "created_at": "2025-07-29 12:49:45.577145"}, {"id": "2372", "timestamp": "2017-11-10 00:00:00", "period": "weekly", "open": "8.090", "high": "8.600", "low": "7.600", "close": "8.600", "volume": "256970.00000000", "created_at": "2025-07-29 12:49:48.113910"}]}, "cnindex_000001_上证指数": {"table_name": "cnindex_000001_上证指数", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "7584", "timestamp": "2021-12-29 00:00:00", "period": "daily", "open": "3630.920", "high": "3630.920", "low": "3596.320", "close": "3597.000", "volume": "305131766.00000000", "created_at": "2025-07-28 08:54:03.742785"}, {"id": "3455", "timestamp": "2005-01-04 00:00:00", "period": "daily", "open": "1260.780", "high": "1260.780", "low": "1238.180", "close": "1242.770", "volume": "8713300.00000000", "created_at": "2025-07-28 08:54:03.742785"}, {"id": "711", "timestamp": "1993-09-30 00:00:00", "period": "daily", "open": "887.130", "high": "896.580", "low": "883.550", "close": "890.270", "volume": "524829.00000000", "created_at": "2025-07-28 08:54:03.742785"}]}, "cnindex_399006_创业板指": {"table_name": "cnindex_399006_创业板指", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "3726", "timestamp": "2011-04-08 00:00:00", "period": "weekly", "open": "1011.680", "high": "1019.140", "low": "992.310", "close": "1019.140", "volume": "4884873.00000000", "created_at": "2025-07-28 08:54:08.599510"}, {"id": "2560", "timestamp": "2020-12-10 00:00:00", "period": "daily", "open": "2689.210", "high": "2734.640", "low": "2675.460", "close": "2718.550", "volume": "85881188.00000000", "created_at": "2025-07-28 08:54:07.398280"}, {"id": "2002", "timestamp": "2018-08-22 00:00:00", "period": "daily", "open": "1454.710", "high": "1457.790", "low": "1437.260", "close": "1439.550", "volume": "38469603.00000000", "created_at": "2025-07-28 08:54:07.398280"}]}, "commodity_clusd_原油": {"table_name": "commodity_clusd_原油", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "1831", "timestamp": "2002-12-09 00:00:00", "period": "daily", "open": "27.050", "high": "27.430", "low": "26.980", "close": "27.200", "volume": "0E-8", "created_at": "2025-07-28 08:45:43.059158"}, {"id": "2940", "timestamp": "2007-04-25 00:00:00", "period": "daily", "open": "64.540", "high": "65.920", "low": "64.530", "close": "65.840", "volume": "0E-8", "created_at": "2025-07-28 08:45:43.059158"}, {"id": "3467", "timestamp": "2009-05-01 00:00:00", "period": "daily", "open": "50.950", "high": "53.650", "low": "50.430", "close": "52.610", "volume": "0E-8", "created_at": "2025-07-28 08:45:43.059158"}]}, "commodity_xagusd_现货白银": {"table_name": "commodity_xagusd_现货白银", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "356", "timestamp": "2016-12-06 00:00:00", "period": "daily", "open": "16.780", "high": "16.950", "low": "16.740", "close": "16.780", "volume": "0E-8", "created_at": "2025-07-28 08:45:40.480543"}, {"id": "1333", "timestamp": "2020-09-08 00:00:00", "period": "daily", "open": "27.050", "high": "27.385", "low": "25.985", "close": "26.830", "volume": "0E-8", "created_at": "2025-07-28 08:45:40.480543"}, {"id": "1231", "timestamp": "2020-04-17 00:00:00", "period": "daily", "open": "15.750", "high": "15.800", "low": "15.195", "close": "15.345", "volume": "0E-8", "created_at": "2025-07-28 08:45:40.480543"}]}, "commodity_xauusd_现货黄金": {"table_name": "commodity_xauusd_现货黄金", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "2955", "timestamp": "2022-07-01 00:00:00", "period": "weekly", "open": "1839.600", "high": "1842.800", "low": "1783.400", "close": "1811.800", "volume": "0E-8", "created_at": "2025-07-28 08:45:39.581288"}, {"id": "3099", "timestamp": "2025-04-04 00:00:00", "period": "weekly", "open": "3117.700", "high": "3201.600", "low": "3032.700", "close": "3056.100", "volume": "0E-8", "created_at": "2025-07-28 08:45:39.581288"}, {"id": "262", "timestamp": "2016-07-27 00:00:00", "period": "daily", "open": "1319.900", "high": "1342.300", "low": "1315.600", "close": "1339.900", "volume": "0E-8", "created_at": "2025-07-28 08:45:38.235875"}]}, "crypto_bnb_usdt_bnb": {"table_name": "crypto_bnb_usdt_bnb", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "55737", "timestamp": "2024-03-21 13:00:00", "period": "hourly", "open": "559.2000000000", "high": "565.4000000000", "low": "554.3000000000", "close": "559.2000000000", "volume": "53114.82900000", "created_at": "2025-07-28 09:05:29.811542"}, {"id": "33339", "timestamp": "2021-08-31 04:00:00", "period": "hourly", "open": "461.4000000000", "high": "465.2000000000", "low": "460.4000000000", "close": "460.9000000000", "volume": "47599.92200000", "created_at": "2025-07-28 09:05:29.811542"}, {"id": "68824", "timestamp": "2018-06-02 08:00:00", "period": "4hourly", "open": "14.3000000000", "high": "14.6526000000", "low": "14.2600000000", "close": "14.5000000000", "volume": "481725.01000000", "created_at": "2025-07-28 09:05:54.015725"}]}, "crypto_btc_usdt_btc": {"table_name": "crypto_btc_usdt_btc", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "55900", "timestamp": "2024-01-07 15:00:00", "period": "hourly", "open": "44402.0000000000", "high": "44431.1000000000", "low": "43945.1500000000", "close": "44047.5700000000", "volume": "1672.94945000", "created_at": "2025-07-29 02:46:51.690501"}, {"id": "35430", "timestamp": "2021-09-06 14:00:00", "period": "hourly", "open": "51476.8300000000", "high": "51721.9400000000", "low": "51433.0000000000", "close": "51702.3900000000", "volume": "2247.95000000", "created_at": "2025-07-29 02:46:51.690501"}, {"id": "23584", "timestamp": "2020-04-30 04:00:00", "period": "hourly", "open": "9289.9700000000", "high": "9289.9800000000", "low": "9011.5400000000", "close": "9154.3000000000", "volume": "10001.46402300", "created_at": "2025-07-29 02:46:51.690501"}]}, "crypto_btc_usdt_比特币": {"table_name": "crypto_btc_usdt_比特币", "columns": ["id", "timestamp", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 1, "data": [{"id": "1", "timestamp": "2025-07-28 10:00:00", "open": "118930.8800000000", "high": "118935.9400000000", "low": "118771.5900000000", "close": "118771.5900000000", "volume": "41.00000000", "created_at": "2025-07-28 10:14:51.005437"}]}, "crypto_doge_usdt_doge": {"table_name": "crypto_doge_usdt_doge", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "12668", "timestamp": "2020-12-15 08:00:00", "period": "hourly", "open": "0.0031985000", "high": "0.0032064000", "low": "0.0031805000", "close": "0.0031903000", "volume": "6637642.00000000", "created_at": "2025-07-28 09:17:36.092025"}, {"id": "41321", "timestamp": "2024-03-24 00:00:00", "period": "hourly", "open": "0.1619100000", "high": "0.1672500000", "low": "0.1609700000", "close": "0.1663300000", "volume": "207133800.00000000", "created_at": "2025-07-28 09:17:36.092025"}, {"id": "32781", "timestamp": "2023-04-03 04:00:00", "period": "hourly", "open": "0.0773000000", "high": "0.0776500000", "low": "0.0772000000", "close": "0.0776200000", "volume": "30975053.00000000", "created_at": "2025-07-28 09:17:36.092025"}]}, "crypto_dydx_usdt_dydx": {"table_name": "crypto_dydx_usdt_dydx", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "22340", "timestamp": "2024-03-28 00:00:00", "period": "hourly", "open": "3.4410000000", "high": "3.4620000000", "low": "3.4250000000", "close": "3.4420000000", "volume": "223483.93000000", "created_at": "2025-07-28 09:20:15.530771"}, {"id": "30087", "timestamp": "2025-02-13 19:00:00", "period": "hourly", "open": "0.7735000000", "high": "0.7812000000", "low": "0.7670000000", "close": "0.7714000000", "volume": "287282.83000000", "created_at": "2025-07-28 09:20:15.530771"}, {"id": "12236", "timestamp": "2023-01-31 23:00:00", "period": "hourly", "open": "3.2610000000", "high": "3.3070000000", "low": "3.1690000000", "close": "3.2230000000", "volume": "3440798.49000000", "created_at": "2025-07-28 09:20:15.530771"}]}, "crypto_ena_usdt_ena": {"table_name": "crypto_ena_usdt_ena", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "12686", "timestamp": "2024-10-06 16:00:00", "period": "4hourly", "open": "0.2924000000", "high": "0.3000000000", "low": "0.2884000000", "close": "0.2888000000", "volume": "24257476.69000000", "created_at": "2025-07-28 09:22:01.714023"}, {"id": "7569", "timestamp": "2025-02-11 16:00:00", "period": "hourly", "open": "0.4597000000", "high": "0.4663000000", "low": "0.4543000000", "close": "0.4588000000", "volume": "9593842.67000000", "created_at": "2025-07-28 09:21:56.668048"}, {"id": "5818", "timestamp": "2024-11-30 17:00:00", "period": "hourly", "open": "0.7839000000", "high": "0.7996000000", "low": "0.7762000000", "close": "0.7993000000", "volume": "3250962.97000000", "created_at": "2025-07-28 09:21:56.668048"}]}, "crypto_eth_usdt_eth": {"table_name": "crypto_eth_usdt_eth", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "82355", "timestamp": "2023-06-29 08:00:00", "period": "4hourly", "open": "1847.4500000000", "high": "1872.8400000000", "low": "1846.7400000000", "close": "1869.6000000000", "volume": "63671.98420000", "created_at": "2025-07-28 09:04:14.995660"}, {"id": "31227", "timestamp": "2021-03-15 02:00:00", "period": "hourly", "open": "1884.9900000000", "high": "1891.4400000000", "low": "1868.3000000000", "close": "1882.3300000000", "volume": "20838.90068000", "created_at": "2025-07-28 09:03:44.040233"}, {"id": "55865", "timestamp": "2024-01-06 04:00:00", "period": "hourly", "open": "2253.0000000000", "high": "2255.6600000000", "low": "2245.4200000000", "close": "2246.7900000000", "volume": "5548.78860000", "created_at": "2025-07-28 09:03:44.040233"}]}, "crypto_ldo_usdt_ldo": {"table_name": "crypto_ldo_usdt_ldo", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "30749", "timestamp": "2023-12-05 12:00:00", "period": "4hourly", "open": "2.3270000000", "high": "2.3640000000", "low": "2.3130000000", "close": "2.3590000000", "volume": "1190319.08000000", "created_at": "2025-07-28 09:21:39.617011"}, {"id": "8622", "timestamp": "2023-05-03 17:00:00", "period": "hourly", "open": "1.8770000000", "high": "1.9040000000", "low": "1.8560000000", "close": "1.8820000000", "volume": "875539.12000000", "created_at": "2025-07-28 09:21:27.009948"}, {"id": "9693", "timestamp": "2023-06-18 02:00:00", "period": "hourly", "open": "1.7820000000", "high": "1.7830000000", "low": "1.7740000000", "close": "1.7760000000", "volume": "19621.59000000", "created_at": "2025-07-28 09:21:27.009948"}]}, "crypto_link_usdt_link": {"table_name": "crypto_link_usdt_link", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "26791", "timestamp": "2022-02-08 03:00:00", "period": "hourly", "open": "18.8600000000", "high": "19.0300000000", "low": "18.7900000000", "close": "19.0100000000", "volume": "152825.42000000", "created_at": "2025-07-28 09:15:52.633367"}, {"id": "4275", "timestamp": "2019-07-14 04:00:00", "period": "hourly", "open": "3.1337000000", "high": "3.1921000000", "low": "3.1251000000", "close": "3.1270000000", "volume": "159716.80000000", "created_at": "2025-07-28 09:15:52.633367"}, {"id": "39265", "timestamp": "2023-07-12 22:00:00", "period": "hourly", "open": "6.2300000000", "high": "6.2330000000", "low": "6.2180000000", "close": "6.2310000000", "volume": "21694.12000000", "created_at": "2025-07-28 09:15:52.633367"}]}, "crypto_ltc_usdt_ltc": {"table_name": "crypto_ltc_usdt_ltc", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "67355", "timestamp": "2018-04-03 12:00:00", "period": "4hourly", "open": "122.8900000000", "high": "127.2200000000", "low": "122.5300000000", "close": "126.6000000000", "volume": "14724.90595000", "created_at": "2025-07-28 09:14:38.852244"}, {"id": "29393", "timestamp": "2021-04-25 14:00:00", "period": "hourly", "open": "229.1200000000", "high": "234.4400000000", "low": "227.9900000000", "close": "233.2400000000", "volume": "57251.62677000", "created_at": "2025-07-28 09:13:59.633324"}, {"id": "82671", "timestamp": "2025-03-31 16:00:00", "period": "4hourly", "open": "83.2300000000", "high": "84.3000000000", "low": "82.5800000000", "close": "83.3300000000", "volume": "61285.53600000", "created_at": "2025-07-28 09:14:38.852244"}]}, "crypto_near_usdt_near": {"table_name": "crypto_near_usdt_near", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "32364", "timestamp": "2024-07-22 08:00:00", "period": "hourly", "open": "6.1560000000", "high": "6.1750000000", "low": "6.0920000000", "close": "6.0970000000", "volume": "321278.70000000", "created_at": "2025-07-28 09:18:59.314669"}, {"id": "18302", "timestamp": "2022-12-11 08:00:00", "period": "hourly", "open": "1.7120000000", "high": "1.7130000000", "low": "1.7020000000", "close": "1.7110000000", "volume": "85831.10000000", "created_at": "2025-07-28 09:18:55.192822"}, {"id": "118", "timestamp": "2020-10-19 02:00:00", "period": "hourly", "open": "0.8600000000", "high": "0.8619000000", "low": "0.8320000000", "close": "0.8410000000", "volume": "272982.78000000", "created_at": "2025-07-28 09:18:55.192822"}]}, "crypto_neo_usdt_neo": {"table_name": "crypto_neo_usdt_neo", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "56790", "timestamp": "2024-05-18 10:00:00", "period": "hourly", "open": "15.7900000000", "high": "15.8100000000", "low": "15.7200000000", "close": "15.7700000000", "volume": "8805.52000000", "created_at": "2025-07-28 09:14:50.252220"}, {"id": "84923", "timestamp": "2020-03-20 00:00:00", "period": "daily", "open": "6.4620000000", "high": "7.2000000000", "low": "5.7010000000", "close": "6.2730000000", "volume": "1871897.66000000", "created_at": "2025-07-28 09:15:28.256531"}, {"id": "68599", "timestamp": "2018-07-05 08:00:00", "period": "4hourly", "open": "41.6410000000", "high": "42.8800000000", "low": "40.6560000000", "close": "42.1780000000", "volume": "101509.05500000", "created_at": "2025-07-28 09:15:20.724179"}]}, "crypto_ondo_usdt_ondo": {"table_name": "crypto_ondo_usdt_ondo", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "1102", "timestamp": "2025-05-27 11:00:00", "period": "hourly", "open": "0.9497000000", "high": "0.9508000000", "low": "0.9430000000", "close": "0.9497000000", "volume": "534936.00000000", "created_at": "2025-07-28 09:26:09.437692"}, {"id": "1960", "timestamp": "2025-07-02 05:00:00", "period": "hourly", "open": "0.7550000000", "high": "0.7607000000", "low": "0.7522000000", "close": "0.7602000000", "volume": "363408.40000000", "created_at": "2025-07-28 09:26:09.437692"}, {"id": "2236", "timestamp": "2025-07-13 17:00:00", "period": "hourly", "open": "0.9044000000", "high": "0.9126000000", "low": "0.9008000000", "close": "0.9099000000", "volume": "422585.60000000", "created_at": "2025-07-28 09:26:09.437692"}]}, "crypto_sol_usdt_sol": {"table_name": "crypto_sol_usdt_sol", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "30205", "timestamp": "2024-01-22 14:00:00", "period": "hourly", "open": "88.6600000000", "high": "88.7500000000", "low": "86.5500000000", "close": "87.4000000000", "volume": "334934.54000000", "created_at": "2025-07-28 09:24:09.594111"}, {"id": "14730", "timestamp": "2022-04-17 18:00:00", "period": "hourly", "open": "103.8100000000", "high": "103.9000000000", "low": "102.7600000000", "close": "103.0600000000", "volume": "119778.61000000", "created_at": "2025-07-28 09:24:09.594111"}, {"id": "52798", "timestamp": "2024-11-13 20:00:00", "period": "4hourly", "open": "210.8700000000", "high": "215.5000000000", "low": "205.9300000000", "close": "215.1300000000", "volume": "1332948.77600000", "created_at": "2025-07-28 09:24:29.574192"}]}, "crypto_sui_usdt_sui": {"table_name": "crypto_sui_usdt_sui", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "15082", "timestamp": "2025-01-20 21:00:00", "period": "hourly", "open": "4.4238000000", "high": "4.4600000000", "low": "4.3682000000", "close": "4.3733000000", "volume": "2051647.70000000", "created_at": "2025-07-28 09:25:16.662563"}, {"id": "17200", "timestamp": "2025-04-19 03:00:00", "period": "hourly", "open": "2.1410000000", "high": "2.1510000000", "low": "2.1349000000", "close": "2.1411000000", "volume": "2339514.10000000", "created_at": "2025-07-28 09:25:16.662563"}, {"id": "22767", "timestamp": "2024-10-12 16:00:00", "period": "4hourly", "open": "2.2069000000", "high": "2.2446000000", "low": "2.1919000000", "close": "2.2270000000", "volume": "17228706.10000000", "created_at": "2025-07-28 09:25:25.515412"}]}, "crypto_trx_usdt_trx": {"table_name": "crypto_trx_usdt_trx", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "55583", "timestamp": "2024-10-17 01:00:00", "period": "hourly", "open": "0.1600000000", "high": "0.1602000000", "low": "0.1598000000", "close": "0.1600000000", "volume": "5645333.30000000", "created_at": "2025-07-28 09:20:58.086361"}, {"id": "1287", "timestamp": "2018-08-04 19:00:00", "period": "hourly", "open": "0.0287000000", "high": "0.0287500000", "low": "0.0285600000", "close": "0.0285600000", "volume": "26503563.30000000", "created_at": "2025-07-28 09:20:58.086361"}, {"id": "58245", "timestamp": "2025-02-04 23:00:00", "period": "hourly", "open": "0.2231000000", "high": "0.2248000000", "low": "0.2230000000", "close": "0.2245000000", "volume": "39607477.20000000", "created_at": "2025-07-28 09:20:58.086361"}]}, "crypto_uni_usdt_uni": {"table_name": "crypto_uni_usdt_uni", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "51833", "timestamp": "2024-12-07 08:00:00", "period": "4hourly", "open": "18.2430000000", "high": "18.2900000000", "low": "17.3580000000", "close": "17.4360000000", "volume": "1828225.68000000", "created_at": "2025-07-28 09:22:52.849992"}, {"id": "36492", "timestamp": "2024-11-16 10:00:00", "period": "hourly", "open": "8.8400000000", "high": "8.8710000000", "low": "8.7950000000", "close": "8.8280000000", "volume": "182097.76000000", "created_at": "2025-07-28 09:22:29.862736"}, {"id": "32039", "timestamp": "2024-05-14 21:00:00", "period": "hourly", "open": "6.8200000000", "high": "6.8330000000", "low": "6.7940000000", "close": "6.8290000000", "volume": "11674.11000000", "created_at": "2025-07-28 09:22:29.862736"}]}, "crypto_xlm_usdt_xlm": {"table_name": "crypto_xlm_usdt_xlm", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "6694", "timestamp": "2019-03-07 10:00:00", "period": "hourly", "open": "0.0841600000", "high": "0.0843900000", "low": "0.0836800000", "close": "0.0843500000", "volume": "1165418.00000000", "created_at": "2025-07-28 09:18:26.492634"}, {"id": "23529", "timestamp": "2021-02-07 19:00:00", "period": "hourly", "open": "0.3788200000", "high": "0.3862900000", "low": "0.3752700000", "close": "0.3771600000", "volume": "26506369.50000000", "created_at": "2025-07-28 09:18:26.492634"}, {"id": "31866", "timestamp": "2022-01-21 17:00:00", "period": "hourly", "open": "0.2213000000", "high": "0.2217000000", "low": "0.2180000000", "close": "0.2187000000", "volume": "3154143.00000000", "created_at": "2025-07-28 09:18:26.492634"}]}, "crypto_xrp_usdt_xrp": {"table_name": "crypto_xrp_usdt_xrp", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "1010", "timestamp": "2018-06-15 09:00:00", "period": "hourly", "open": "0.5500000000", "high": "0.5504400000", "low": "0.5437300000", "close": "0.5473400000", "volume": "996895.50000000", "created_at": "2025-07-28 09:16:43.794215"}, {"id": "40738", "timestamp": "2022-12-30 08:00:00", "period": "hourly", "open": "0.3368000000", "high": "0.3386000000", "low": "0.3363000000", "close": "0.3382000000", "volume": "9186290.00000000", "created_at": "2025-07-28 09:16:43.794215"}, {"id": "30324", "timestamp": "2021-10-22 10:00:00", "period": "hourly", "open": "1.1028000000", "high": "1.1040000000", "low": "1.0962000000", "close": "1.1023000000", "volume": "8651352.00000000", "created_at": "2025-07-28 09:16:43.794215"}]}, "etf_sz159993_证券etf龙头": {"table_name": "etf_sz159993_证券etf龙头", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "175", "timestamp": "2020-10-26 00:00:00", "period": "daily", "open": "1.216", "high": "1.227", "low": "1.180", "close": "1.188", "volume": "652937.00000000", "created_at": "2025-07-29 09:14:18.642520"}, {"id": "861", "timestamp": "2023-08-18 00:00:00", "period": "daily", "open": "1.088", "high": "1.111", "low": "1.078", "close": "1.078", "volume": "552375.00000000", "created_at": "2025-07-29 09:14:18.642520"}, {"id": "162", "timestamp": "2020-09-29 00:00:00", "period": "daily", "open": "1.232", "high": "1.239", "low": "1.226", "close": "1.227", "volume": "370519.00000000", "created_at": "2025-07-29 09:14:18.642520"}]}, "gap_attempt_records": {"table_name": "gap_attempt_records", "columns": ["id", "symbol", "category", "gap_start_date", "gap_end_date", "data_source", "attempt_result", "attempt_timestamp", "error_message", "records_found", "metadata"], "sample_count": 0, "data": []}, "hkstocks_01129_中国水业集团": {"table_name": "hkstocks_01129_中国水业集团", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "2812", "timestamp": "2013-08-07 00:00:00", "period": "daily", "open": "4.888", "high": "5.021", "low": "4.855", "close": "4.888", "volume": "118400.00000000", "created_at": "2025-07-29 09:14:10.569016"}, {"id": "1501", "timestamp": "2008-02-14 00:00:00", "period": "daily", "open": "12.955", "high": "13.121", "low": "12.455", "close": "12.955", "volume": "12008000.00000000", "created_at": "2025-07-29 09:14:10.569016"}, {"id": "3616", "timestamp": "2016-11-14 00:00:00", "period": "daily", "open": "4.688", "high": "4.755", "low": "4.688", "close": "4.755", "volume": "774800.00000000", "created_at": "2025-07-29 09:14:10.569016"}]}, "other_801010_si_申万农林牧渔": {"table_name": "other_801010_si_申万农林牧渔", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "1603", "timestamp": "2019-02-22 00:00:00", "period": "weekly", "open": "2485.170", "high": "2629.160", "low": "2485.170", "close": "2613.820", "volume": "120814958.00000000", "created_at": "2025-07-29 02:28:36.331063"}, {"id": "1193", "timestamp": "2023-11-29 00:00:00", "period": "daily", "open": "3937.110", "high": "3937.550", "low": "3880.680", "close": "3895.050", "volume": "72605693.00000000", "created_at": "2025-07-29 02:28:35.488672"}, {"id": "115", "timestamp": "2019-06-24 00:00:00", "period": "daily", "open": "2534.620", "high": "2542.920", "low": "2521.470", "close": "2542.860", "volume": "19096133.00000000", "created_at": "2025-07-29 02:28:35.488672"}]}, "other_801030_si_申万基础化工": {"table_name": "other_801030_si_申万基础化工", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "19", "timestamp": "2000-01-27 00:00:00", "period": "daily", "open": "1066.830", "high": "1089.190", "low": "1042.740", "close": "1066.380", "volume": "1.91888360", "created_at": "2025-07-28 08:55:45.313417"}, {"id": "5832", "timestamp": "2024-02-22 00:00:00", "period": "daily", "open": "3038.770", "high": "3076.650", "low": "3035.270", "close": "3076.650", "volume": "35.40892308", "created_at": "2025-07-28 08:55:45.313417"}, {"id": "3640", "timestamp": "2015-01-20 00:00:00", "period": "daily", "open": "2333.540", "high": "2398.110", "low": "2333.180", "close": "2398.110", "volume": "27.28570429", "created_at": "2025-07-28 08:55:45.313417"}]}, "other_801040_si_申万钢铁": {"table_name": "other_801040_si_申万钢铁", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "7532", "timestamp": "2004-11-30 00:00:00", "period": "monthly", "open": "1359.740", "high": "1426.170", "low": "1329.230", "close": "1391.890", "volume": "28.35531418", "created_at": "2025-07-28 08:56:20.395923"}, {"id": "6552", "timestamp": "2007-07-20 00:00:00", "period": "weekly", "open": "4352.690", "high": "4352.900", "low": "4139.860", "close": "4299.420", "volume": "25.71600378", "created_at": "2025-07-28 08:56:15.365361"}, {"id": "4588", "timestamp": "2018-12-18 00:00:00", "period": "daily", "open": "2289.010", "high": "2305.730", "low": "2270.530", "close": "2280.190", "volume": "5.41378888", "created_at": "2025-07-28 08:56:09.745725"}]}, "other_801080_si_申万电子": {"table_name": "other_801080_si_申万电子", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "5059", "timestamp": "2020-11-27 00:00:00", "period": "daily", "open": "4628.890", "high": "4663.790", "low": "4595.760", "close": "4662.030", "volume": "38.94871925", "created_at": "2025-07-28 08:56:33.008619"}, {"id": "7132", "timestamp": "2018-11-23 00:00:00", "period": "weekly", "open": "2291.090", "high": "2299.690", "low": "2110.350", "close": "2117.540", "volume": "150.50026502", "created_at": "2025-07-28 08:56:39.940166"}, {"id": "3107", "timestamp": "2012-11-07 00:00:00", "period": "daily", "open": "1210.260", "high": "1217.430", "low": "1204.590", "close": "1211.030", "volume": "5.32629924", "created_at": "2025-07-28 08:56:33.008619"}]}, "other_801140_si_申万轻工制造": {"table_name": "other_801140_si_申万轻工制造", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "5698", "timestamp": "2023-07-31 00:00:00", "period": "daily", "open": "2394.290", "high": "2415.950", "low": "2394.290", "close": "2411.570", "volume": "12.51183834", "created_at": "2025-07-28 08:56:58.141832"}, {"id": "3994", "timestamp": "2016-07-11 00:00:00", "period": "daily", "open": "3307.590", "high": "3321.590", "low": "3287.620", "close": "3290.090", "volume": "11.73761928", "created_at": "2025-07-28 08:56:58.141832"}, {"id": "3901", "timestamp": "2016-02-22 00:00:00", "period": "daily", "open": "3144.460", "high": "3183.240", "low": "3135.390", "close": "3183.240", "volume": "12.84287509", "created_at": "2025-07-28 08:56:58.141832"}]}, "other_801150_si_申万医药生物": {"table_name": "other_801150_si_申万医药生物", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "1314", "timestamp": "2005-06-27 00:00:00", "period": "daily", "open": "680.090", "high": "693.650", "low": "676.530", "close": "691.430", "volume": "1.95174388", "created_at": "2025-07-28 08:57:21.985171"}, {"id": "7750", "timestamp": "2023-01-31 00:00:00", "period": "monthly", "open": "9109.480", "high": "9888.420", "low": "9100.170", "close": "9566.210", "volume": "604.23019259", "created_at": "2025-07-28 08:57:32.952480"}, {"id": "249", "timestamp": "2001-01-12 00:00:00", "period": "daily", "open": "1773.090", "high": "1789.280", "low": "1740.610", "close": "1757.780", "volume": "0.76346326", "created_at": "2025-07-28 08:57:21.985171"}]}, "other__dji_道琼斯工业平均指数": {"table_name": "other__dji_道琼斯工业平均指数", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "5210", "timestamp": "2024-09-11 00:00:00", "period": "daily", "open": "40638.762", "high": "40903.680", "low": "39993.070", "close": "40861.711", "volume": "356047581.00000000", "created_at": "2025-07-29 11:04:51.966061"}, {"id": "6245", "timestamp": "2019-08-23 00:00:00", "period": "weekly", "open": "26020.061", "high": "26388.779", "low": "25507.180", "close": "25628.900", "volume": "1286614110.00000000", "created_at": "2025-07-29 11:04:55.054770"}, {"id": "4361", "timestamp": "2021-04-27 00:00:00", "period": "daily", "open": "33932.129", "high": "34043.981", "low": "33870.461", "close": "33984.930", "volume": "294947064.00000000", "created_at": "2025-07-29 11:04:51.966061"}]}, "other__ixic_美国100现金cfd": {"table_name": "other__ixic_美国100现金cfd", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "131", "timestamp": "2004-07-12 00:00:00", "period": "daily", "open": "1936.170", "high": "1941.240", "low": "1921.400", "close": "1936.920", "volume": "1504000000.00000000", "created_at": "2025-07-29 11:04:58.057810"}, {"id": "3691", "timestamp": "2018-08-29 00:00:00", "period": "daily", "open": "8044.340", "high": "8113.560", "low": "8042.100", "close": "8109.690", "volume": "1720198860.00000000", "created_at": "2025-07-29 11:04:58.057810"}, {"id": "3453", "timestamp": "2017-09-19 00:00:00", "period": "daily", "open": "6465.570", "high": "6467.790", "low": "6446.750", "close": "6461.320", "volume": "1570782481.00000000", "created_at": "2025-07-29 11:04:58.057810"}]}, "other__spx_s_p_500指数": {"table_name": "other__spx_s_p_500指数", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "778", "timestamp": "2007-02-05 00:00:00", "period": "daily", "open": "1448.330", "high": "1449.380", "low": "1443.850", "close": "1446.990", "volume": "2439429888.00000000", "created_at": "2025-07-29 11:04:45.745129"}, {"id": "1387", "timestamp": "2009-07-07 00:00:00", "period": "daily", "open": "898.600", "high": "898.600", "low": "879.930", "close": "881.030", "volume": "4673299968.00000000", "created_at": "2025-07-29 11:04:45.745129"}, {"id": "1282", "timestamp": "2009-02-04 00:00:00", "period": "daily", "open": "837.770", "high": "851.850", "low": "829.180", "close": "832.230", "volume": "6420449792.00000000", "created_at": "2025-07-29 11:04:45.745129"}]}, "usstocks_aihs_森淼科技": {"table_name": "usstocks_aihs_森淼科技", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "1908", "timestamp": "2019-04-26 00:00:00", "period": "weekly", "open": "47.100", "high": "52.900", "low": "43.600", "close": "50.100", "volume": "827608.00000000", "created_at": "2025-07-29 10:22:47.680442"}, {"id": "1805", "timestamp": "2025-05-21 00:00:00", "period": "daily", "open": "0.950", "high": "0.958", "low": "0.920", "close": "0.920", "volume": "10052.00000000", "created_at": "2025-07-29 10:22:46.499796"}, {"id": "123", "timestamp": "2018-09-10 00:00:00", "period": "daily", "open": "53.000", "high": "56.300", "low": "51.000", "close": "51.700", "volume": "19704.00000000", "created_at": "2025-07-29 10:22:46.499796"}]}, "usstocks_crcl_circle_internet_group_inc_a": {"table_name": "usstocks_crcl_circle_internet_group_inc_a", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 3, "data": [{"id": "13", "timestamp": "2025-06-24 00:00:00", "period": "daily", "open": "250.420", "high": "265.000", "low": "217.579", "close": "222.650", "volume": "68222554.00000000", "created_at": "2025-07-29 10:22:05.872834"}, {"id": "8", "timestamp": "2025-06-16 00:00:00", "period": "daily", "open": "147.540", "high": "165.600", "low": "145.880", "close": "151.060", "volume": "43781546.00000000", "created_at": "2025-07-29 10:22:05.872834"}, {"id": "63", "timestamp": "2025-08-04 00:00:00", "period": "daily", "open": "172.100", "high": "172.100", "low": "154.500", "close": "164.820", "volume": "11700418.00000000", "created_at": "2025-08-05 08:23:24.214275"}]}, "usstocks_cycu_cycurion_inc": {"table_name": "usstocks_cycu_cycurion_inc", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 0, "data": []}, "usstocks_limnw_liminatus_pharma_inc_wt": {"table_name": "usstocks_limnw_liminatus_pharma_inc_wt", "columns": ["id", "timestamp", "period", "open", "high", "low", "close", "volume", "created_at"], "sample_count": 0, "data": []}}}