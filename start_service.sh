#!/bin/bash

# AI市场分析系统启动脚本
# Author: AI Assistant
# Date: 2025-07-06

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 配置
SERVICE_NAME="AI市场分析系统"
PYTHON_SCRIPT="dev.py"
PORT=50505
HOST="***********"
PID_FILE="service.pid"
LOG_FILE="service.log"

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # 端口被占用
    else
        return 1  # 端口空闲
    fi
}

# 函数：获取进程PID
get_service_pid() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p $pid > /dev/null 2>&1; then
            echo $pid
        else
            rm -f "$PID_FILE"
            echo ""
        fi
    else
        echo ""
    fi
}

# 函数：停止服务
stop_service() {
    print_message $YELLOW "正在停止 $SERVICE_NAME..."
    
    local pid=$(get_service_pid)
    if [ -n "$pid" ]; then
        kill $pid
        sleep 2
        if ps -p $pid > /dev/null 2>&1; then
            print_message $YELLOW "强制停止进程..."
            kill -9 $pid
        fi
        rm -f "$PID_FILE"
        print_message $GREEN "✅ 服务已停止"
    else
        print_message $YELLOW "⚠️  服务未运行"
    fi
    
    # 检查并清理端口占用
    if check_port $PORT; then
        print_message $YELLOW "清理端口 $PORT 占用..."
        local port_pid=$(lsof -ti:$PORT)
        if [ -n "$port_pid" ]; then
            kill -9 $port_pid 2>/dev/null
            sleep 1
        fi
    fi
}

# 函数：启动服务
start_service() {
    print_message $BLUE "🚀 启动 $SERVICE_NAME..."
    
    # 检查Python脚本是否存在
    if [ ! -f "$PYTHON_SCRIPT" ]; then
        print_message $RED "❌ 错误: $PYTHON_SCRIPT 文件不存在"
        exit 1
    fi
    
    # 检查端口是否被占用
    if check_port $PORT; then
        print_message $RED "❌ 错误: 端口 $PORT 已被占用"
        print_message $YELLOW "正在尝试清理..."
        stop_service
        sleep 2
    fi
    
    # 启动服务
    print_message $BLUE "启动服务在端口 $PORT..."
    nohup python3 "$PYTHON_SCRIPT" > "$LOG_FILE" 2>&1 &
    local pid=$!
    echo $pid > "$PID_FILE"
    
    # 等待服务启动（调试模式需要更长时间）
    sleep 8
    
    # 检查服务是否成功启动
    if ps -p $pid > /dev/null 2>&1; then
        if check_port $PORT; then
            print_message $GREEN "✅ $SERVICE_NAME 启动成功!"
            print_message $GREEN "📱 局域网访问: http://***********:$PORT"
            print_message $GREEN "🌐 网络访问: http://***********:$PORT"
            print_message $BLUE "📋 进程ID: $pid"
            print_message $BLUE "📄 日志文件: $LOG_FILE"
        else
            print_message $RED "❌ 服务启动失败: 端口未监听"
            stop_service
            exit 1
        fi
    else
        print_message $RED "❌ 服务启动失败"
        rm -f "$PID_FILE"
        exit 1
    fi
}

# 函数：检查服务状态
check_status() {
    local pid=$(get_service_pid)
    if [ -n "$pid" ]; then
        if check_port $PORT; then
            print_message $GREEN "✅ $SERVICE_NAME 正在运行"
            print_message $BLUE "📋 进程ID: $pid"
            print_message $BLUE "🌐 访问地址: http://***********:$PORT"
            print_message $BLUE "🌐 网络地址: http://***********:$PORT"
        else
            print_message $YELLOW "⚠️  进程存在但端口未监听"
        fi
    else
        print_message $RED "❌ $SERVICE_NAME 未运行"
    fi
}

# 函数：重启服务
restart_service() {
    print_message $YELLOW "🔄 重启 $SERVICE_NAME..."
    stop_service
    sleep 2
    start_service
}

# 函数：查看日志
view_logs() {
    if [ -f "$LOG_FILE" ]; then
        print_message $BLUE "📄 查看服务日志 (按 Ctrl+C 退出):"
        tail -f "$LOG_FILE"
    else
        print_message $YELLOW "⚠️  日志文件不存在"
    fi
}

# 函数：网络诊断
network_diagnosis() {
    print_message $BLUE "🔍 网络诊断..."
    
    local local_ip=$(hostname -I | awk '{print $1}')
    print_message $BLUE "本机IP地址: $local_ip"
    
    # 检查防火墙状态
    if command -v ufw >/dev/null 2>&1; then
        print_message $BLUE "防火墙状态:"
        sudo ufw status
    elif command -v firewall-cmd >/dev/null 2>&1; then
        print_message $BLUE "防火墙状态:"
        sudo firewall-cmd --state
        sudo firewall-cmd --list-ports
    fi
    
    # 检查端口监听
    print_message $BLUE "端口监听状态:"
    netstat -tlnp | grep ":$PORT " || echo "端口 $PORT 未监听"
    
    # 测试局域网连接
    print_message $BLUE "测试局域网连接:"
    curl -s -o /dev/null -w "HTTP状态码: %{http_code}\n" "http://***********:$PORT" || echo "局域网连接失败"

    # 测试网络连接
    print_message $BLUE "测试网络连接:"
    curl -s -o /dev/null -w "HTTP状态码: %{http_code}\n" "http://***********:$PORT" || echo "网络连接失败"
}

# 函数：显示帮助
show_help() {
    echo "用法: $0 {start|stop|restart|status|logs|diagnosis|help}"
    echo ""
    echo "命令:"
    echo "  start      启动服务"
    echo "  stop       停止服务"
    echo "  restart    重启服务"
    echo "  status     查看服务状态"
    echo "  logs       查看服务日志"
    echo "  diagnosis  网络诊断"
    echo "  help       显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start     # 启动服务"
    echo "  $0 status    # 查看状态"
    echo "  $0 logs      # 查看日志"
}

# 主逻辑
case "$1" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        check_status
        ;;
    logs)
        view_logs
        ;;
    diagnosis)
        network_diagnosis
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_message $RED "❌ 无效的命令: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
