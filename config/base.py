#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础配置文件
所有环境的通用配置
"""

import os
from typing import Dict, Any
try:
    from pydantic_settings import BaseSettings
    from pydantic import validator
except ImportError:
    # 如果pydantic-settings不可用，使用简化配置
    BaseSettings = object
    def validator(*args, **kwargs):
        def decorator(func):
            return func
        return decorator


class BaseConfig(BaseSettings):
    """基础配置类"""
    
    # 应用基础配置
    APP_NAME: str = "趋势分析系统"
    APP_VERSION: str = "2.0.0"
    APP_DESCRIPTION: str = "基于移动平均线的股票趋势流分析系统"
    
    # 服务配置
    HOST: str = "0.0.0.0"
    PORT: int = 5008
    DEBUG: bool = False
    TESTING: bool = False
    
    # 数据库配置
    DB_HOST: str = "127.0.0.1"
    DB_PORT: int = 5433
    DB_NAME: str = "fintech_db"
    DB_USER: str = "postgres"
    DB_PASSWORD: str = "robot2025"
    DB_ECHO: bool = False
    
    # Redis配置
    REDIS_HOST: str = "127.0.0.1"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: str = ""
    
    # JWT配置
    JWT_SECRET_KEY: str = "your-secret-key-change-in-production"
    JWT_ACCESS_TOKEN_EXPIRES: int = 3600  # 1小时
    JWT_REFRESH_TOKEN_EXPIRES: int = 86400 * 30  # 30天
    
    # 缓存配置
    CACHE_TYPE: str = "redis"
    CACHE_DEFAULT_TIMEOUT: int = 300  # 5分钟
    CACHE_KEY_PREFIX: str = "trend_analysis:"
    
    # 分页配置
    DEFAULT_PAGE_SIZE: int = 20
    MAX_PAGE_SIZE: int = 100
    
    # 文件上传配置
    MAX_CONTENT_LENGTH: int = 16 * 1024 * 1024  # 16MB
    UPLOAD_FOLDER: str = "uploads"
    ALLOWED_EXTENSIONS: set = {"csv", "xlsx", "json"}
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "[%(asctime)s] %(levelname)s - %(name)s - %(message)s"
    LOG_DATE_FORMAT: str = "%Y-%m-%d %H:%M:%S"
    LOG_FILE: str = "logs/app.log"
    LOG_MAX_BYTES: int = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT: int = 5
    
    # Celery配置
    CELERY_BROKER_URL: str = "redis://127.0.0.1:6379/1"
    CELERY_RESULT_BACKEND: str = "redis://127.0.0.1:6379/1"
    CELERY_TASK_SERIALIZER: str = "json"
    CELERY_RESULT_SERIALIZER: str = "json"
    CELERY_ACCEPT_CONTENT: list = ["json"]
    CELERY_TIMEZONE: str = "Asia/Shanghai"
    CELERY_ENABLE_UTC: bool = True
    
    # 移动平均线配置
    MA_PERIODS: Dict[str, int] = {
        "short": 5,      # 5日线 (周线)
        "medium": 20,    # 20日线 (月线)
        "long": 50,      # 50日线 (季度线)
        "ultra_long": 200  # 200日线 (年线)
    }
    
    # 趋势评分配置
    TREND_SCORING: Dict[str, Any] = {
        "base_score": 0,
        "max_score": 6,
        "min_score": -6,
        "weights": {
            "ma5_vs_ma20": 1,
            "ma5_vs_ma50": 1,
            "ma5_vs_ma200": 1,
            "ma20_vs_ma50": 1,
            "ma20_vs_ma200": 1,
            "ma50_vs_ma200": 1
        }
    }
    
    # 分析配置
    MIN_DATA_POINTS: int = 200  # 最少数据点数
    DEFAULT_DATA_LIMIT: int = 250  # 默认数据量
    DATA_LIMIT_OPTIONS: list = [100, 250, 500, 1000, 2000]  # 可选数据量
    MAX_DATA_LIMIT: int = 2000  # 最大数据量限制
    ANALYSIS_BATCH_SIZE: int = 50  # 批量分析大小
    ANALYSIS_TIMEOUT: int = 3600  # 分析超时时间(秒)
    
    # API配置
    API_PREFIX: str = "/api"
    API_VERSION: str = "v1"
    CORS_ORIGINS: list = ["*"]
    CORS_METHODS: list = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    CORS_HEADERS: list = ["*"]
    
    # 限流配置
    RATELIMIT_STORAGE_URL: str = "redis://127.0.0.1:6379/2"
    RATELIMIT_DEFAULT: str = "100 per hour"
    RATELIMIT_HEADERS_ENABLED: bool = True
    
    # 监控配置
    METRICS_ENABLED: bool = True
    METRICS_PORT: int = 9090
    HEALTH_CHECK_ENABLED: bool = True
    
    @validator("DB_PASSWORD", pre=True)
    def validate_db_password(cls, v):
        if not v:
            raise ValueError("数据库密码不能为空")
        return v
    
    @validator("JWT_SECRET_KEY", pre=True)
    def validate_jwt_secret(cls, v):
        if v == "your-secret-key-change-in-production":
            raise ValueError("生产环境必须修改JWT密钥")
        return v
    
    @property
    def database_url(self) -> str:
        """构建数据库连接URL"""
        return f"postgresql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
    
    @property
    def redis_url(self) -> str:
        """构建Redis连接URL"""
        if self.REDIS_PASSWORD:
            return f"redis://:{self.REDIS_PASSWORD}@{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
        return f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
    
    @property
    def api_base_url(self) -> str:
        """API基础URL"""
        return f"{self.API_PREFIX}/{self.API_VERSION}"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 创建配置实例
config = BaseConfig()
