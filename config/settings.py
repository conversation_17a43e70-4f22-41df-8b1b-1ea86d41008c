"""
系统配置管理
"""

import os
import json
from pathlib import Path
from typing import Dict, Any

class Settings:
    """统一配置管理类"""
    
    def __init__(self):
        self.config_dir = Path(__file__).parent
        self._configs = {}
        self._load_all_configs()
    
    def _load_all_configs(self):
        """加载所有配置文件"""
        config_files = [
            'scoring_unit_config.json',
            'group_config.json', 
            'composite_config.json',
            'indicator_config.json',
            'user_config.json',
            'weight_config.json'
        ]
        
        for config_file in config_files:
            config_path = self.config_dir / config_file
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_name = config_file.replace('.json', '')
                    self._configs[config_name] = json.load(f)
    
    def get(self, config_name: str, default: Any = None) -> Any:
        """获取配置"""
        return self._configs.get(config_name, default)
    
    def get_scoring_units(self) -> Dict[str, Any]:
        """获取评分单元配置"""
        return self.get('scoring_unit_config', {})
    
    def get_groups(self) -> Dict[str, Any]:
        """获取分组配置"""
        return self.get('group_config', {})
    
    def get_composite(self) -> Dict[str, Any]:
        """获取综合评分配置"""
        return self.get('composite_config', {})
    
    def get_indicators(self) -> Dict[str, Any]:
        """获取指标配置"""
        return self.get('indicator_config', {})
    
    def get_weights(self) -> Dict[str, Any]:
        """获取权重配置"""
        return self.get('weight_config', {})
    
    def get_user_config(self) -> Dict[str, Any]:
        """获取用户配置"""
        return self.get('user_config', {})

# 全局配置实例
settings = Settings()
