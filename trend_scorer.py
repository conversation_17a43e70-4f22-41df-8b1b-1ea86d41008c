#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
趋势评分算法
基于多层级移动平均线比较实现趋势评分逻辑
"""

import logging
from typing import Dict, Optional
from config import TREND_SCORING

class TrendScorer:
    """趋势评分器"""

    def __init__(self):
        self.scoring_config = TREND_SCORING
        self.logger = logging.getLogger(__name__)
    
    def calculate_trend_score(self, ma_data: Dict[str, float]) -> Dict[str, any]:
        """
        计算趋势评分
        
        评分规则：
        - 5日线高于20日线：+1分
        - 5日线高于50日线：+1分  
        - 5日线高于200日线：+1分
        - 20日线高于50日线：+1分
        - 20日线高于200日线：+1分
        - 50日线高于200日线：+1分
        
        反之则减分
        """
        if not ma_data:
            return self._create_empty_result()
        
        try:
            ma5 = ma_data.get('ma5')
            ma20 = ma_data.get('ma20')
            ma50 = ma_data.get('ma50')
            ma200 = ma_data.get('ma200')
            current_price = ma_data.get('current_price')
            
            # 检查数据完整性
            if None in [ma5, ma20, ma50, ma200]:
                self.logger.warning("移动平均线数据不完整，无法计算评分")
                return self._create_empty_result()
            
            score = 0
            score_details = {}
            signals = []
            
            # 1. 5日线 vs 20日线
            if ma5 > ma20:
                score += 1
                score_details['ma5_vs_ma20'] = '+1 (5日线上穿20日线)'
                signals.append('短期上升趋势')
            else:
                score -= 1
                score_details['ma5_vs_ma20'] = '-1 (5日线下穿20日线)'
                signals.append('短期下降趋势')
            
            # 2. 5日线 vs 50日线
            if ma5 > ma50:
                score += 1
                score_details['ma5_vs_ma50'] = '+1 (5日线上穿50日线)'
            else:
                score -= 1
                score_details['ma5_vs_ma50'] = '-1 (5日线下穿50日线)'
            
            # 3. 5日线 vs 200日线
            if ma5 > ma200:
                score += 1
                score_details['ma5_vs_ma200'] = '+1 (5日线上穿200日线)'
            else:
                score -= 1
                score_details['ma5_vs_ma200'] = '-1 (5日线下穿200日线)'
            
            # 4. 20日线 vs 50日线
            if ma20 > ma50:
                score += 1
                score_details['ma20_vs_ma50'] = '+1 (20日线上穿50日线)'
                signals.append('中期上升趋势')
            else:
                score -= 1
                score_details['ma20_vs_ma50'] = '-1 (20日线下穿50日线)'
                signals.append('中期下降趋势')
            
            # 5. 20日线 vs 200日线
            if ma20 > ma200:
                score += 1
                score_details['ma20_vs_ma200'] = '+1 (20日线上穿200日线)'
            else:
                score -= 1
                score_details['ma20_vs_ma200'] = '-1 (20日线下穿200日线)'
            
            # 6. 50日线 vs 200日线
            if ma50 > ma200:
                score += 1
                score_details['ma50_vs_ma200'] = '+1 (50日线上穿200日线)'
                signals.append('长期上升趋势')
            else:
                score -= 1
                score_details['ma50_vs_ma200'] = '-1 (50日线下穿200日线)'
                signals.append('长期下降趋势')
            
            # 生成趋势等级
            trend_grade = self._get_trend_grade(score)
            
            # 生成趋势描述
            trend_description = self._get_trend_description(score, ma5, ma20, ma50, ma200)
            
            result = {
                'score': score,
                'max_score': 6,
                'min_score': -6,
                'score_percentage': round((score + 6) / 12 * 100, 1),  # 转换为百分比
                'trend_grade': trend_grade,
                'trend_description': trend_description,
                'score_details': score_details,
                'signals': signals,
                'ma_values': {
                    'current_price': current_price,
                    'ma5': ma5,
                    'ma20': ma20,
                    'ma50': ma50,
                    'ma200': ma200
                }
            }
            
            self.logger.debug(f"趋势评分计算完成: 得分 {score}/6")
            return result
            
        except Exception as e:
            self.logger.error(f"计算趋势评分失败: {str(e)}")
            return self._create_empty_result()
    
    def _get_trend_grade(self, score: int) -> str:
        """根据评分获取趋势等级"""
        if score >= 5:
            return "A+"
        elif score >= 3:
            return "A"
        elif score >= 1:
            return "B+"
        elif score >= -1:
            return "B"
        elif score >= -3:
            return "C+"
        elif score >= -5:
            return "C"
        else:
            return "D"
    
    def _get_trend_description(self, score: int, ma5: float, ma20: float, ma50: float, ma200: float) -> str:
        """根据评分和均线位置生成趋势描述"""
        if score >= 4:
            if ma5 > ma20 > ma50 > ma200:
                return "强势多头排列，趋势向上"
            else:
                return "强势上升趋势"
        elif score >= 2:
            return "上升趋势"
        elif score >= 0:
            return "弱势上升或震荡"
        elif score >= -2:
            return "弱势下降或震荡"
        elif score >= -4:
            return "下降趋势"
        else:
            if ma5 < ma20 < ma50 < ma200:
                return "强势空头排列，趋势向下"
            else:
                return "强势下降趋势"
    
    def _create_empty_result(self) -> Dict[str, any]:
        """创建空的评分结果"""
        return {
            'score': 0,
            'max_score': 6,
            'min_score': -6,
            'score_percentage': 50.0,
            'trend_grade': 'N/A',
            'trend_description': '数据不足',
            'score_details': {},
            'signals': [],
            'ma_values': {}
        }
    
    def get_trend_strength(self, score: int) -> str:
        """获取趋势强度描述"""
        abs_score = abs(score)
        if abs_score >= 5:
            return "极强"
        elif abs_score >= 3:
            return "强"
        elif abs_score >= 1:
            return "中等"
        else:
            return "弱"
    
    def is_bullish_trend(self, score: int) -> bool:
        """判断是否为看涨趋势"""
        return score > 0
    
    def is_bearish_trend(self, score: int) -> bool:
        """判断是否为看跌趋势"""
        return score < 0
    
    def get_trading_signal(self, score: int) -> str:
        """获取交易信号"""
        if score >= 4:
            return "强烈买入"
        elif score >= 2:
            return "买入"
        elif score >= 0:
            return "持有/观望"
        elif score >= -2:
            return "观望/减仓"
        elif score >= -4:
            return "卖出"
        else:
            return "强烈卖出"
