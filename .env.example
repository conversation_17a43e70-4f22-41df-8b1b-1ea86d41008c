# 环境配置示例文件
# 复制此文件为 .env 并修改相应配置

# 应用环境 (development, production, testing, docker)
FLASK_ENV=development

# 数据库配置
DB_HOST=127.0.0.1
DB_PORT=5433
DB_NAME=fintech_db
DB_USER=postgres
DB_PASSWORD=robot2025

# Redis配置
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# JWT配置
JWT_SECRET_KEY=your-secret-key-change-in-production

# 服务配置
HOST=0.0.0.0
PORT=5008

# 日志配置
LOG_LEVEL=INFO

# Celery配置
CELERY_BROKER_URL=redis://127.0.0.1:6379/1
CELERY_RESULT_BACKEND=redis://127.0.0.1:6379/1
