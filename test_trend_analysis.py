#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
趋势分析功能测试脚本
"""

import sys
import os
import logging

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from data_loader import DataLoader
from ma_calculator import MACalculator
from trend_scorer import TrendScorer
from trend_analyzer import TrendAnalyzer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def test_data_loader():
    """测试数据加载器"""
    print("\n🔍 测试数据加载器...")
    
    loader = DataLoader()
    
    # 测试数据库连接
    if loader.connect_db():
        print("✅ 数据库连接成功")
    else:
        print("❌ 数据库连接失败")
        return False
    
    # 测试配置加载
    config = loader.load_market_config()
    if config:
        print(f"✅ 配置加载成功，包含 {len(config)} 个分组")
    else:
        print("❌ 配置加载失败")
        return False
    
    # 测试股票列表获取
    stocks = loader.get_stock_list()
    if stocks:
        print(f"✅ 获取股票列表成功，共 {len(stocks)} 只股票")
        print(f"   示例股票: {stocks[0]['symbol']}({stocks[0]['name']})")
    else:
        print("❌ 获取股票列表失败")
        return False
    
    loader.close_db()
    return True

def test_ma_calculator():
    """测试移动平均线计算器"""
    print("\n📊 测试移动平均线计算器...")
    
    import pandas as pd
    import numpy as np
    
    # 创建测试数据
    dates = pd.date_range('2023-01-01', periods=250, freq='D')
    prices = 100 + np.cumsum(np.random.randn(250) * 0.5)  # 模拟价格走势
    
    test_df = pd.DataFrame({
        'timestamp': dates,
        'close': prices,
        'open': prices * 0.99,
        'high': prices * 1.01,
        'low': prices * 0.98,
        'volume': np.random.randint(1000, 10000, 250)
    })
    
    calculator = MACalculator()
    ma_data = calculator.calculate_all_ma(test_df)
    
    if ma_data and all(key in ma_data for key in ['ma5', 'ma20', 'ma50', 'ma200']):
        print("✅ 移动平均线计算成功")
        print(f"   MA5: {ma_data['ma5']:.2f}")
        print(f"   MA20: {ma_data['ma20']:.2f}")
        print(f"   MA50: {ma_data['ma50']:.2f}")
        print(f"   MA200: {ma_data['ma200']:.2f}")
        return ma_data
    else:
        print("❌ 移动平均线计算失败")
        return None

def test_trend_scorer(ma_data):
    """测试趋势评分器"""
    print("\n🎯 测试趋势评分器...")
    
    if not ma_data:
        print("❌ 没有移动平均线数据")
        return False
    
    scorer = TrendScorer()
    trend_result = scorer.calculate_trend_score(ma_data)
    
    if trend_result and 'score' in trend_result:
        print("✅ 趋势评分计算成功")
        print(f"   评分: {trend_result['score']}/6")
        print(f"   等级: {trend_result['trend_grade']}")
        print(f"   描述: {trend_result['trend_description']}")
        print(f"   百分比: {trend_result['score_percentage']}%")
        return True
    else:
        print("❌ 趋势评分计算失败")
        return False

def test_single_stock_analysis():
    """测试单只股票分析"""
    print("\n📈 测试单只股票分析...")
    
    analyzer = TrendAnalyzer()
    
    # 获取一只股票进行测试
    stocks = analyzer.data_loader.get_stock_list()
    if not stocks:
        print("❌ 没有找到股票")
        return False
    
    # 找一只有数据的股票
    test_stock = None
    for stock in stocks[:10]:  # 测试前10只股票
        if analyzer.data_loader.check_table_exists(stock['symbol'], stock['name'], stock['category']):
            test_stock = stock
            break
    
    if not test_stock:
        print("❌ 没有找到有数据的股票")
        return False
    
    print(f"   测试股票: {test_stock['symbol']}({test_stock['name']})")
    
    result = analyzer.analyze_single_stock(
        test_stock['symbol'],
        test_stock['name'],
        test_stock['category']
    )
    
    if result:
        print("✅ 单只股票分析成功")
        print(f"   数据量: {result['data_count']} 条")
        print(f"   趋势评分: {result['trend_analysis']['score']}/6")
        print(f"   趋势等级: {result['trend_analysis']['trend_grade']}")
        print(f"   趋势描述: {result['trend_analysis']['trend_description']}")
        return True
    else:
        print("❌ 单只股票分析失败")
        return False

def main():
    """主测试函数"""
    print("🚀 开始趋势分析功能测试")
    
    # 测试各个组件
    tests = [
        ("数据加载器", test_data_loader),
        ("移动平均线计算器", test_ma_calculator),
        ("单只股票分析", test_single_stock_analysis)
    ]
    
    results = []
    ma_data = None
    
    for test_name, test_func in tests:
        try:
            if test_name == "移动平均线计算器":
                result = test_func()
                if result:
                    ma_data = result
                    results.append((test_name, True))
                    # 测试趋势评分器
                    scorer_result = test_trend_scorer(ma_data)
                    results.append(("趋势评分器", scorer_result))
                else:
                    results.append((test_name, False))
            else:
                result = test_func()
                results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试出错: {str(e)}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n📋 测试结果汇总:")
    success_count = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n🎯 测试完成: {success_count}/{len(results)} 项通过")
    
    if success_count == len(results):
        print("🎉 所有测试通过！趋势分析功能正常")
        print("\n🚀 可以启动服务:")
        print("   cd trend_analysis")
        print("   python trend_service.py")
        print("   或者运行: ./start_trend_service.sh")
    else:
        print("⚠️  部分测试失败，请检查配置和数据")

if __name__ == '__main__':
    main()
