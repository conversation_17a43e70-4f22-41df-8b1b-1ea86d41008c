#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据获取模块
从market_config读取股票配置，获取数据库中的股票数据
"""

import psycopg2
import pandas as pd
import logging
from typing import Dict, List, Optional
# 数据库配置
DB_CONFIG = {
    'host': '***********',
    'port': 5433,
    'database': 'fintech_db',
    'user': 'postgres',
    'password': 'robot2025'
}

class DataLoader:
    """数据加载器"""
    
    def __init__(self):
        self.db_config = DB_CONFIG
        self.conn = None
        self.logger = logging.getLogger(__name__)
        
    def connect_db(self):
        """连接数据库"""
        try:
            self.conn = psycopg2.connect(**self.db_config)
            self.logger.info("数据库连接成功")
            return True
        except Exception as e:
            self.logger.error(f"数据库连接失败: {str(e)}")
            return False
    
    def close_db(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            self.logger.info("数据库连接已关闭")
    
    def get_all_data_tables(self) -> List[Dict]:
        """直接从数据库查询所有可用的数据表"""
        if not self.conn:
            if not self.connect_db():
                return []

        try:
            cursor = self.conn.cursor()
            # 查询所有包含OHLCV数据的表
            cursor.execute("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_type = 'BASE TABLE'
                AND table_name NOT LIKE '%_backup%'
                AND table_name NOT LIKE '%_temp%'
                ORDER BY table_name;
            """)

            tables = cursor.fetchall()
            cursor.close()

            # 验证表是否包含必要的列
            valid_tables = []
            for (table_name,) in tables:
                if self._validate_table_structure(table_name):
                    # 解析表名获取股票信息
                    stock_info = self._parse_table_name(table_name)
                    if stock_info:
                        valid_tables.append(stock_info)

            self.logger.info(f"从数据库发现 {len(valid_tables)} 个有效的数据表")
            return valid_tables

        except Exception as e:
            self.logger.error(f"查询数据库表失败: {str(e)}")
            return []
    
    def get_stock_list(self) -> List[Dict]:
        """获取所有可用的股票列表（直接从数据库查询）"""
        return self.get_all_data_tables()

    def _validate_table_structure(self, table_name: str) -> bool:
        """验证表是否包含必要的OHLCV列"""
        if not self.conn:
            return False

        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = %s
                AND table_schema = 'public'
            """, (table_name,))

            columns = [row[0] for row in cursor.fetchall()]
            cursor.close()

            # 检查必要的列是否存在
            required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            return all(col in columns for col in required_columns)

        except Exception as e:
            self.logger.debug(f"验证表 {table_name} 结构失败: {str(e)}")
            return False

    def _parse_table_name(self, table_name: str) -> Optional[Dict]:
        """从表名解析股票信息"""
        try:
            # 表名格式通常为: prefix_symbol_name
            parts = table_name.split('_', 2)
            if len(parts) < 3:
                return None

            prefix = parts[0]
            symbol = parts[1]
            name = parts[2]

            # 根据前缀推断分类
            category_mapping = {
                'ashares': '股票/A股',
                'usstocks': '股票/美股',
                'hkstocks': '股票/港股',
                'cnindex': '指数/中国股票指数',
                'crypto': '加密货币/现货',
                'etf': '基金/ETF基金',
                'commodity': '商品',
                'other': '其他'
            }

            category = category_mapping.get(prefix, '其他')

            # 清理symbol和name
            clean_symbol = symbol.upper()
            clean_name = name.replace('_', ' ').strip()

            return {
                'symbol': clean_symbol,
                'name': clean_name,
                'category': category,
                'table_name': table_name,
                'source': 'database',
                'enabled': True
            }

        except Exception as e:
            self.logger.debug(f"解析表名 {table_name} 失败: {str(e)}")
            return None
    

    
    def get_stock_data(self, table_name: str, limit: int = 250) -> Optional[pd.DataFrame]:
        """获取股票的历史数据（直接使用表名）"""
        if not self.conn:
            if not self.connect_db():
                return None

        try:
            # 查询最近的数据，按时间倒序
            query = f"""
                SELECT timestamp, open, high, low, close, volume
                FROM {table_name}
                ORDER BY timestamp DESC
                LIMIT %s
            """

            df = pd.read_sql_query(query, self.conn, params=[limit])

            if df.empty:
                self.logger.warning(f"表 {table_name} 没有数据")
                return None

            # 按时间正序排列（计算移动平均线需要）
            df = df.sort_values('timestamp').reset_index(drop=True)
            df['timestamp'] = pd.to_datetime(df['timestamp'])

            self.logger.debug(f"从表 {table_name} 获取到 {len(df)} 条数据")
            return df

        except Exception as e:
            self.logger.error(f"获取表 {table_name} 数据失败: {str(e)}")
            return None
    
    def check_table_exists(self, table_name: str) -> bool:
        """检查数据表是否存在"""
        if not self.conn:
            if not self.connect_db():
                return False

        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name = %s
                );
            """, (table_name,))

            exists = cursor.fetchone()[0]
            cursor.close()
            return exists

        except Exception as e:
            self.logger.error(f"检查表 {table_name} 是否存在失败: {str(e)}")
            return False
