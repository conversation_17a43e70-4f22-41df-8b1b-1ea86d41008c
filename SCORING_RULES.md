# 📊 技术分析评价规则文档

> **版本**: v1.0  
> **创建日期**: 2025-06-25  
> **适用范围**: 股票技术分析评分系统  
> **维护说明**: 本文档将持续更新，所有技术分析功能都应严格遵循此规则

---

## 🎯 总体设计原则

### 核心理念
- **客观量化**: 所有评分规则基于数学计算，避免主观判断
- **分层评价**: 不同技术分析方法独立评分，最终综合
- **动态权重**: 根据市场环境和数据质量调整权重
- **可扩展性**: 规则设计支持后续添加新的技术指标

### 评分体系架构
```
技术分析总评分
├── 趋势流分析 (40% 权重)
│   ├── 移动平均线分析
│   └── 趋势强度评估
├── 波段流分析 (30% 权重) [待实现]
│   ├── 震荡指标分析
│   └── 超买超卖判断
├── 成交量分析 (20% 权重) [待实现]
│   ├── 量价关系
│   └── 资金流向
└── 风险评估 (10% 权重) [待实现]
    ├── 波动率分析
    └── 回撤风险
```

---

## 📈 趋势流分析规则 (已实现)

### 1. 移动平均线定义

| 均线类型 | 周期 | 代表意义 | 权重 |
|----------|------|----------|------|
| MA5 | 5日 | 短期趋势 (周线) | 高 |
| MA20 | 20日 | 中期趋势 (月线) | 高 |
| MA50 | 50日 | 长期趋势 (季度线) | 中 |
| MA200 | 200日 | 超长期趋势 (年线) | 中 |

### 2. 趋势评分算法

#### 基础评分规则
**总分范围**: -6分 到 +6分

| 比较项目 | 条件 | 得分 | 说明 |
|----------|------|------|------|
| MA5 vs MA20 | MA5 > MA20 | +1分 | 短期上升趋势 |
| MA5 vs MA20 | MA5 < MA20 | -1分 | 短期下降趋势 |
| MA5 vs MA50 | MA5 > MA50 | +1分 | 中短期强势 |
| MA5 vs MA50 | MA5 < MA50 | -1分 | 中短期弱势 |
| MA5 vs MA200 | MA5 > MA200 | +1分 | 整体趋势向上 |
| MA5 vs MA200 | MA5 < MA200 | -1分 | 整体趋势向下 |
| MA20 vs MA50 | MA20 > MA50 | +1分 | 中期上升趋势 |
| MA20 vs MA50 | MA20 < MA50 | -1分 | 中期下降趋势 |
| MA20 vs MA200 | MA20 > MA200 | +1分 | 中长期强势 |
| MA20 vs MA200 | MA20 < MA200 | -1分 | 中长期弱势 |
| MA50 vs MA200 | MA50 > MA200 | +1分 | 长期上升趋势 |
| MA50 vs MA200 | MA50 < MA200 | -1分 | 长期下降趋势 |

#### 特殊情况处理
- **数据不足**: 如果历史数据少于200个交易日，该股票不参与评分
- **均线重合**: 如果两条均线差值小于0.1%，视为平等，得分为0
- **异常数据**: 如果价格出现异常波动(单日涨跌幅>20%)，需要数据验证

### 3. 趋势等级划分

| 评分范围 | 等级 | 颜色标识 | 趋势描述 | 操作建议 |
|----------|------|----------|----------|----------|
| 6分 | A+ | 深绿色 | 完美多头排列 | 强烈买入 |
| 4-5分 | A | 绿色 | 强势上升趋势 | 买入 |
| 2-3分 | B+ | 浅绿色 | 上升趋势 | 适量买入 |
| 0-1分 | B | 灰色 | 弱势上升或震荡 | 持有观望 |
| -1-0分 | B | 灰色 | 弱势下降或震荡 | 谨慎持有 |
| -3--2分 | C+ | 浅红色 | 下降趋势 | 适量减仓 |
| -5--4分 | C | 红色 | 强势下降趋势 | 卖出 |
| -6分 | D | 深红色 | 完美空头排列 | 强烈卖出 |

### 4. 趋势强度判断

#### 多头排列强度
```
完美多头: MA5 > MA20 > MA50 > MA200 (6分)
强势多头: 5个比较项为正 (4-5分)
一般多头: 3-4个比较项为正 (2-3分)
```

#### 空头排列强度
```
完美空头: MA5 < MA20 < MA50 < MA200 (-6分)
强势空头: 5个比较项为负 (-5--4分)
一般空头: 3-4个比较项为负 (-3--2分)
```

#### 震荡状态
```
平衡震荡: 正负比较项基本相等 (-1到1分)
```

---

## 🌊 波段流分析规则 (待实现)

### 1. 技术指标定义

| 指标名称 | 参数 | 用途 | 权重 |
|----------|------|------|------|
| RSI | 14日 | 超买超卖判断 | 25% |
| KDJ | K=9, D=3, J=3 | 短期买卖信号 | 25% |
| MACD | 12,26,9 | 趋势转换信号 | 30% |
| 布林带 | 20日,2倍标准差 | 价格通道分析 | 20% |

### 2. 波段评分算法 (规划)

#### RSI评分规则
```
RSI >= 80: -2分 (严重超买)
RSI 70-80: -1分 (超买)
RSI 50-70: +1分 (强势)
RSI 30-50: 0分 (正常)
RSI 20-30: +1分 (超卖，买入机会)
RSI <= 20: +2分 (严重超卖，强烈买入)
```

#### KDJ评分规则
```
K线与D线金叉且J<20: +2分 (强烈买入信号)
K线与D线金叉: +1分 (买入信号)
K>80且D>80: -1分 (超买信号)
K<20且D<20: +1分 (超卖信号)
K线与D线死叉: -1分 (卖出信号)
```

#### MACD评分规则
```
MACD金叉且柱状图转正: +2分 (强烈买入)
MACD金叉: +1分 (买入信号)
MACD死叉且柱状图转负: -2分 (强烈卖出)
MACD死叉: -1分 (卖出信号)
```

---

## 📊 成交量分析规则 (待实现)

### 1. 量价关系评分

| 价格走势 | 成交量变化 | 得分 | 说明 |
|----------|------------|------|------|
| 上涨 | 放量 | +2分 | 健康上涨 |
| 上涨 | 缩量 | +1分 | 惯性上涨 |
| 下跌 | 放量 | -2分 | 恐慌性下跌 |
| 下跌 | 缩量 | -1分 | 技术性回调 |

### 2. 成交量指标

| 指标 | 计算方法 | 评分规则 |
|------|----------|----------|
| 量比 | 当日成交量/近5日平均量 | >2倍:+1分, <0.5倍:-1分 |
| OBV | 累积成交量指标 | 上升:+1分, 下降:-1分 |

---

## ⚠️ 风险评估规则 (待实现)

### 1. 波动率评分

| 波动率水平 | 评分调整 | 说明 |
|------------|----------|------|
| 极高波动(>5%) | -1分 | 风险过高 |
| 高波动(3-5%) | -0.5分 | 风险较高 |
| 正常波动(1-3%) | 0分 | 正常风险 |
| 低波动(<1%) | +0.5分 | 风险较低 |

### 2. 回撤风险

| 最大回撤 | 评分调整 | 说明 |
|----------|----------|------|
| >20% | -2分 | 高风险 |
| 10-20% | -1分 | 中等风险 |
| 5-10% | 0分 | 正常风险 |
| <5% | +1分 | 低风险 |

---

## 🔧 实现技术规范

### 1. 数据要求
- **最小数据量**: 200个交易日
- **数据完整性**: 缺失数据不超过5%
- **数据质量**: 价格数据通过异常值检测

### 2. 计算精度
- **价格精度**: 保留3位小数
- **评分精度**: 整数评分
- **百分比精度**: 保留1位小数

### 3. 更新频率
- **实时更新**: 收盘后30分钟内完成计算
- **历史回测**: 支持任意时间点的历史评分计算

### 4. 性能要求
- **计算速度**: 单只股票<1秒
- **批量处理**: 1000只股票<5分钟
- **内存占用**: 单只股票<10MB

---

## 📝 扩展规划

### 短期扩展 (1-2周)
- [ ] 波段流分析 (RSI, KDJ, MACD)
- [ ] 成交量分析
- [ ] 综合评分算法

### 中期扩展 (1个月)
- [ ] 行业对比分析
- [ ] 历史回测功能
- [ ] 预警系统

### 长期扩展 (3个月)
- [ ] 机器学习优化
- [ ] 多市场对比
- [ ] 投资组合建议

---

## 🔄 版本更新记录

| 版本 | 日期 | 更新内容 | 负责人 |
|------|------|----------|--------|
| v1.0 | 2025-06-25 | 初始版本，趋势流分析规则 | AI Assistant |

---

**注意**: 本文档为技术分析评分系统的核心规则文档，所有后续开发都应严格遵循此规则。如需修改规则，请更新此文档并记录版本变更。
