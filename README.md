# 🔍 趋势分析系统

基于移动平均线的股票趋势流分析功能

## ✨ 功能特性

### 📊 **趋势评分算法**
- **5日线 vs 20日线**: 高于+1分，低于-1分
- **5日线 vs 50日线**: 高于+1分，低于-1分  
- **5日线 vs 200日线**: 高于+1分，低于-1分
- **20日线 vs 50日线**: 高于+1分，低于-1分
- **20日线 vs 200日线**: 高于+1分，低于-1分
- **50日线 vs 200日线**: 高于+1分，低于-1分

**总分范围**: -6分 到 +6分

### 🎯 **评分等级**
- **A+ (5-6分)**: 强势多头排列
- **A (3-4分)**: 上升趋势
- **B+ (1-2分)**: 弱势上升
- **B (-1-0分)**: 震荡整理
- **C+ (-3--2分)**: 弱势下降
- **C (-5--4分)**: 下降趋势
- **D (-6分)**: 强势空头排列

### 🌐 **Web界面功能**
- 实时分析进度显示
- 股票列表按评分排序
- 趋势等级和描述
- 统计信息查看
- 响应式设计

## 🚀 快速开始

### 1. 启动服务
```bash
cd trend_analysis
python trend_service.py
```

### 2. 访问界面
打开浏览器访问: `http://localhost:5008`

### 3. 开始分析
点击"开始分析"按钮，系统将自动分析所有配置的股票

## 📁 文件结构

```
trend_analysis/
├── __init__.py              # 模块初始化
├── config.py                # 配置文件
├── data_loader.py           # 数据获取模块
├── ma_calculator.py         # 移动平均线计算器
├── trend_scorer.py          # 趋势评分算法
├── trend_analyzer.py        # 趋势分析引擎
├── trend_service.py         # Web服务 (端口5008)
└── README.md               # 说明文档
```

## ⚙️ 配置说明

### 数据库配置
```python
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 5433,
    'database': 'fintech_db',
    'user': 'postgres',
    'password': 'robot2025'
}
```

### 移动平均线周期
```python
MA_PERIODS = {
    'short': 5,      # 5日线 (周线)
    'medium': 20,    # 20日线 (月线)
    'long': 50,      # 50日线 (季度线)
    'ultra_long': 200  # 200日线 (年线)
}
```

## 🔧 API接口

### 启动分析
```http
POST /api/analyze
```

### 获取分析状态
```http
GET /api/status
```

### 获取分析结果
```http
GET /api/results
```

### 获取统计信息
```http
GET /api/statistics
```

### 获取单只股票详情
```http
GET /api/stock/<symbol>
```

## 📊 使用示例

### 1. 启动分析
访问主页，点击"开始分析"按钮

### 2. 查看进度
系统会实时显示分析进度和当前处理的股票

### 3. 查看结果
分析完成后，股票列表按趋势评分从高到低排序

### 4. 理解评分
- **正分**: 上升趋势，分数越高趋势越强
- **负分**: 下降趋势，分数越低趋势越弱
- **零分**: 震荡整理

## 🎯 技术原理

### 移动平均线
- **5日线**: 短期趋势，反映一周的价格走势
- **20日线**: 中期趋势，反映一个月的价格走势
- **50日线**: 长期趋势，反映一个季度的价格走势
- **200日线**: 超长期趋势，反映一年的价格走势

### 趋势判断
通过比较不同周期的移动平均线位置关系，判断股票的趋势强度：
- 短期均线在长期均线上方 → 上升趋势
- 短期均线在长期均线下方 → 下降趋势
- 多头排列 (5>20>50>200) → 强势上升
- 空头排列 (5<20<50<200) → 强势下降

## 📝 注意事项

1. **数据要求**: 股票需要至少200个交易日的数据才能计算200日均线
2. **更新频率**: 基于现有数据库中的最新数据进行分析
3. **分析时间**: 根据股票数量，完整分析可能需要几分钟时间
4. **服务端口**: 默认使用5008端口，确保端口未被占用

## 🔄 后续扩展

本系统为趋势流分析的基础版本，后续可扩展：
- 波段流分析
- 更多技术指标
- 历史回测功能
- 预警系统
- 数据导出功能
