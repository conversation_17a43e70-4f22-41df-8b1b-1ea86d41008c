//@version=4
study("K线形态合集 by 比力鱼", shorttitle = "所有形态", overlay=true)

// 趋势判断
//{
C_DownTrend = true
C_UpTrend = true

trendRule1 = "SMA50"
trendRule2 = "SMA50, SMA200"
trendRule3 = "不检测"
trendRule = input(trendRule1, "基于何种均线检测趋势", options=[trendRule1, trendRule2, trendRule3])

if trendRule == trendRule1  // SMA50
    priceAvg = sma(close, 50)
    C_DownTrend := close < priceAvg
    C_UpTrend := close > priceAvg

if trendRule == trendRule2  // SMA50, SMA200
    sma200 = sma(close, 200)
    sma50 = sma(close, 50)
    C_DownTrend := close < sma50 and sma50 < sma200
    C_UpTrend := close > sma50 and sma50 > sma200
//}

// 全局配置和输入设置
//{
// K线特征参数
var C_Len = 14         // EMA深度用于bodyAvg
var C_ShadowPercent = 5.0      // 影线大小阈值
var C_ShadowEqualsPercent = 100.0
var C_DojiBodyPercent = 5.0
var C_Factor = 2.0     // 影线相对于实体的倍数

// 基础颜色和透明度设置
var float OPACITY_1_CANDLE = 20.0  // 单根K线透明度
var float OPACITY_2_CANDLE = 40.0  // 双根K线透明度
var float OPACITY_3_CANDLE = 60.0  // 三根K线透明度
var float OPACITY_5_CANDLE = 80.0  // 五根K线透明度

var color BULLISH_COLOR = #4CAF50  // 看涨绿色
var color BEARISH_COLOR = #FF5252  // 看跌红色
var color NEUTRAL_COLOR = #787B86  // 中性灰色

// 根据K线数量自动计算标签颜色
label_color_1_candle_bullish = color.new(BULLISH_COLOR, OPACITY_1_CANDLE)
label_color_1_candle_bearish = color.new(BEARISH_COLOR, OPACITY_1_CANDLE)
label_color_1_candle_neutral = color.new(NEUTRAL_COLOR, OPACITY_1_CANDLE)

label_color_2_candle_bullish = color.new(BULLISH_COLOR, OPACITY_2_CANDLE)
label_color_2_candle_bearish = color.new(BEARISH_COLOR, OPACITY_2_CANDLE)

label_color_3_candle_bullish = color.new(BULLISH_COLOR, OPACITY_3_CANDLE)
label_color_3_candle_bearish = color.new(BEARISH_COLOR, OPACITY_3_CANDLE)

label_color_5_candle_bullish = color.new(BULLISH_COLOR, OPACITY_5_CANDLE)
label_color_5_candle_bearish = color.new(BEARISH_COLOR, OPACITY_5_CANDLE)

// 通用颜色引用
label_color_bullish = label_color_2_candle_bullish
label_color_bearish = label_color_2_candle_bearish
label_color_neutral = label_color_1_candle_neutral

// 形态类型选择
candleType1 = "看涨"
candleType2 = "看跌"
candleType3 = "两者"
CandleType = input(candleType3, "显示形态类型", options=[candleType1, candleType2, candleType3])

// 单根K线形态 (1根K线)
HammerInput = input(title = "锤子形态(1)", defval=true) 
HangingManInput = input(title = "上吊线(1)", defval=true) 
InvertedHammerInput = input(title = "倒锤子(1)", defval=false) 
ShootingStarInput = input(title = "流星线(1)", defval=false) 
DojiInput = input(title = "十字星(1)", defval=false) 
DragonflyDojiInput = input(title = "蜻蜓十字(1)", defval=false) 
GravestoneDojiInput = input(title = "墓碑十字(1)", defval=false) 
LongLowerShadowInput = input(title = "长下影线(1)", defval=false) 
LongUpperShadowInput = input(title = "长上影线(1)", defval=false) 
MarubozuBlackInput = input(title = "黑色光头光脚(1)", defval=false) 
MarubozuWhiteInput = input(title = "白色光头光脚(1)", defval=false) 
SpinningTopBlackInput = input(title = "黑色纺锤(1)", defval=false) 
SpinningTopWhiteInput = input(title = "白色纺锤(1)", defval=false) 

// 双根K线形态 (2根K线)
EngulfingInput = input(title = "吞没形态(2)", defval=true) 
OnNeckInput = input(title = "颈线形态(2)", defval=false) 
PiercingInput = input(title = "刺透形态(2)", defval=false) 
DarkCloudCoverInput = input(title = "乌云盖顶(2)", defval=false) 
HaramiInput = input(title = "孕线形态(2)", defval=false) 
HaramiCrossInput = input(title = "孕线十字(2)", defval=false) 
KickingInput = input(title = "踢线形态(2)", defval=false) 
RisingWindowInput = input(title = "上升跳空(2)", defval=false) 
FallingWindowInput = input(title = "下降跳空(2)", defval=false) 
TweezerBottomInput = input(title = "双底形态(2)", defval=false) 
TweezerTopInput = input(title = "双顶形态(2)", defval=false) 

// 三根K线形态 (3根K线)
AbandonedBabyInput = input(title = "遗弃婴儿(3)", defval=true) 
DojiStarInput = input(title = "十字星形态(3)", defval=false) 
DownsideTasukiGapInput = input(title = "下行Tasuki缺口(3)", defval=false) 
EveningDojiStarInput = input(title = "晚星十字(3)", defval=true) 
EveningStarInput = input(title = "晚星形态(3)", defval=true) 
MorningDojiStarInput = input(title = "晨星十字(3)", defval=true) 
MorningStarInput = input(title = "晨星形态(3)", defval=true) 
ThreeBlackCrowsInput = input(title = "三只乌鸦(3)", defval=false) 
ThreeWhiteSoldiersInput = input(title = "三白兵(3)", defval=false) 
TriStarInput = input(title = "三星形态(3)", defval=false) 
UpsideTasukiGapInput = input(title = "上行Tasuki缺口(3)", defval=false) 

// 五根K线形态 (5根K线)
FallingThreeMethodsInput = input(title = "下跌回调形态(5)", defval=false) 
RisingThreeMethodsInput = input(title = "上涨回调形态(5)", defval=false)
//}

// 蜡烛图特征计算
C_BodyHi = max(close, open)
C_BodyLo = min(close, open)
C_Body = C_BodyHi - C_BodyLo
C_BodyAvg = ema(C_Body, C_Len)
C_SmallBody = C_Body < C_BodyAvg
C_LongBody = C_Body > C_BodyAvg
C_UpShadow = high - C_BodyHi
C_DnShadow = C_BodyLo - low
C_HasUpShadow = C_UpShadow > C_ShadowPercent / 100 * C_Body
C_HasDnShadow = C_DnShadow > C_ShadowPercent / 100 * C_Body
C_WhiteBody = open < close
C_BlackBody = open > close
C_Range = high - low
C_IsInsideBar = C_BodyHi[1] > C_BodyHi and C_BodyLo[1] < C_BodyLo
C_BodyMiddle = C_Body / 2 + C_BodyLo
C_ShadowEquals = C_UpShadow == C_DnShadow or (abs(C_UpShadow - C_DnShadow) / C_DnShadow * 100) < C_ShadowEqualsPercent and (abs(C_DnShadow - C_UpShadow) / C_UpShadow * 100) < C_ShadowEqualsPercent
C_IsDojiBody = C_Range > 0 and C_Body <= C_Range * C_DojiBodyPercent / 100
C_Doji = C_IsDojiBody and C_ShadowEquals

patternLabelPosLow = low - (atr(30) * 0.6)
patternLabelPosHigh = high + (atr(30) * 0.6)

// 形态检测
C_OnNeckBearishNumberOfCandles = 2
C_OnNeckBearish = false
if C_DownTrend and C_BlackBody[1] and C_LongBody[1] and C_WhiteBody and open < close[1] and C_SmallBody and C_Range!=0 and abs(close-low[1])<=C_BodyAvg*0.05
    C_OnNeckBearish := true
alertcondition(C_OnNeckBearish, title = "颈线 - 看跌", message = "检测到新的颈线 - 看跌形态")
if C_OnNeckBearish and OnNeckInput and (("看跌" == CandleType) or CandleType == "两者")

    var ttBearishOnNeck = "颈线\n颈线是一个两根蜡烛组成的延续形态，出现在下跌趋势中。第一根蜡烛是长阴线，第二根蜡烛是短阳线，且其收盘价接近或等于第一根蜡烛的最低点。这个形态暗示下跌趋势可能继续，突破第二根阳线的低点时，通常被认为是确认信号。"
    label.new(bar_index, patternLabelPosHigh, text="颈线", style=label.style_label_down, color = label_color_2_candle_bearish, textcolor=color.white, tooltip = ttBearishOnNeck)

C_RisingWindowBullishNumberOfCandles = 2
C_RisingWindowBullish = false
if C_UpTrend[1] and (C_Range!=0 and C_Range[1]!=0) and low > high[1]
    C_RisingWindowBullish := true
alertcondition(C_RisingWindowBullish, title = "上升窗口 - 看涨", message = "检测到新的上升窗口 - 看涨形态")
if C_RisingWindowBullish and RisingWindowInput and (("看涨" == CandleType) or CandleType == "两者")

    var ttBullishRisingWindow = "上升窗口\n上升窗口是一个两根蜡烛组成的看涨延续形态，出现在上升趋势中。两根蜡烛的类型可以是任何类型，除了四价十字星。最重要的特征是两根蜡烛之间的价格缺口，代表对卖压的支撑。"
    label.new(bar_index, patternLabelPosLow, text="上升窗口", style=label.style_label_up, color = label_color_2_candle_bullish, textcolor=color.white, tooltip = ttBullishRisingWindow)

C_FallingWindowBearishNumberOfCandles = 2
C_FallingWindowBearish = false
if C_DownTrend[1] and (C_Range!=0 and C_Range[1]!=0) and high < low[1]
    C_FallingWindowBearish := true
alertcondition(C_FallingWindowBearish, title = "下跌窗口 - 看跌", message = "检测到新的下跌窗口 - 看跌形态")
if C_FallingWindowBearish and FallingWindowInput and (("看跌" == CandleType) or CandleType == "两者")

    var ttBearishFallingWindow = "下跌窗口\n下跌窗口是一个两根蜡烛组成的看跌延续形态，出现在下跌趋势中。两根蜡烛的类型可以是任何类型，除了四价十字星。最重要的特征是两根蜡烛之间的价格缺口，代表下跌趋势可能继续。"
    label.new(bar_index, patternLabelPosHigh, text="下跌窗口", style=label.style_label_down, color = label_color_2_candle_bearish, textcolor=color.white, tooltip = ttBearishFallingWindow)

C_FallingThreeMethodsBearishNumberOfCandles = 5
C_FallingThreeMethodsBearish = false
if C_DownTrend[4] and (C_LongBody[4] and C_BlackBody[4]) and (C_SmallBody[3] and C_WhiteBody[3] and open[3]>low[4] and close[3]<high[4]) and (C_SmallBody[2] and C_WhiteBody[2] and open[2]>low[4] and close[2]<high[4]) and (C_SmallBody[1] and C_WhiteBody[1] and open[1]>low[4] and close[1]<high[4]) and (C_LongBody and C_BlackBody and close<close[4])
    C_FallingThreeMethodsBearish := true
alertcondition(C_FallingThreeMethodsBearish, title = "下跌三法 - 看跌", message = "检测到新的下跌三法 - 看跌形态")
if C_FallingThreeMethodsBearish and FallingThreeMethodsInput and (("看跌" == CandleType) or CandleType == "两者")

    var ttBearishFallingThreeMethods = "下跌三法\n下跌三法是由五根蜡烛组成的看跌形态，暗示现有下跌趋势将继续。第一根蜡烛是长阴线，接下来是三根短阳线，蜡烛的实体在第一根蜡烛范围内。最后一根蜡烛是长阴线，且收盘价低于第一根蜡烛的收盘价。"
    label.new(bar_index, patternLabelPosHigh, text="下跌三法", style=label.style_label_down, color = label_color_5_candle_bearish, textcolor=color.white, tooltip = ttBearishFallingThreeMethods)
    
C_RisingThreeMethodsBullishNumberOfCandles = 5
C_RisingThreeMethodsBullish = false
if C_UpTrend[4] and (C_LongBody[4] and C_WhiteBody[4]) and (C_SmallBody[3] and C_BlackBody[3] and open[3]<high[4] and close[3]>low[4]) and (C_SmallBody[2] and C_BlackBody[2] and open[2]<high[4] and close[2]>low[4]) and (C_SmallBody[1] and C_BlackBody[1] and open[1]<high[4] and close[1]>low[4]) and (C_LongBody and C_WhiteBody and close>close[4])
    C_RisingThreeMethodsBullish := true
alertcondition(C_RisingThreeMethodsBullish, title = "上升三法 - 看涨", message = "检测到新的上升三法 - 看涨形态")
if C_RisingThreeMethodsBullish and RisingThreeMethodsInput and (("看涨" == CandleType) or CandleType == "两者")

    var ttBullishRisingThreeMethods = "上升三法\n上升三法是由五根蜡烛组成的看涨形态，暗示现有上升趋势将继续。第一根蜡烛是长阳线，接下来是三根短阴线，蜡烛的实体在第一根蜡烛范围内。最后一根蜡烛是长阳线，且收盘价高于第一根蜡烛的收盘价。"
    label.new(bar_index, patternLabelPosLow, text="上升三法", style=label.style_label_up, color = label_color_5_candle_bullish, textcolor=color.white, tooltip = ttBullishRisingThreeMethods)
    
// 更多形态检测可按上述格式进行定义。
C_TweezerTopBearishNumberOfCandles = 2
C_TweezerTopBearish = false
if C_UpTrend[1] and (not C_IsDojiBody or (C_HasUpShadow and C_HasDnShadow)) and abs(high-high[1]) <= C_BodyAvg*0.05 and C_WhiteBody[1] and C_BlackBody and C_LongBody[1]
    C_TweezerTopBearish := true
alertcondition(C_TweezerTopBearish, title = "镊子顶 - 看跌", message = "检测到新的镊子顶 - 看跌形态")
if C_TweezerTopBearish and TweezerTopInput and (("看跌" == CandleType) or CandleType == "两者")

    var ttBearishTweezerTop = "镊子顶\n镊子顶是两根蜡烛组成的形态，表示可能的看跌反转。该形态出现在上升趋势中。第一根蜡烛是长阳线，第二根蜡烛是阴线，且其最高价几乎与前一根蜡烛相同。相似的最高价与方向相反，暗示市场上空方力量正在增强。"
    label.new(bar_index, patternLabelPosHigh, text="镊子顶", style=label.style_label_down, color = label_color_2_candle_bearish, textcolor=color.white, tooltip = ttBearishTweezerTop)

C_TweezerBottomBullishNumberOfCandles = 2
C_TweezerBottomBullish = false
if C_DownTrend[1] and (not C_IsDojiBody or (C_HasUpShadow and C_HasDnShadow)) and abs(low-low[1]) <= C_BodyAvg*0.05 and C_BlackBody[1] and C_WhiteBody and C_LongBody[1]
    C_TweezerBottomBullish := true
alertcondition(C_TweezerBottomBullish, title = "镊子底 - 看涨", message = "检测到新的镊子底 - 看涨形态")
if C_TweezerBottomBullish and TweezerBottomInput and (("看涨" == CandleType) or CandleType == "两者")

    var ttBullishTweezerBottom = "镊子底\n镊子底是两根蜡烛组成的形态，表示可能的看涨反转。该形态出现在下跌趋势中。第一根蜡烛是长阴线，第二根蜡烛是阳线，且其最低价几乎与前一根蜡烛相同。相似的最低价与方向相反，暗示市场上多方力量正在增强。"
    label.new(bar_index, patternLabelPosLow, text="镊子底", style=label.style_label_up, color = label_color_2_candle_bullish, textcolor=color.white, tooltip = ttBullishTweezerBottom)

C_DarkCloudCoverBearishNumberOfCandles = 2
C_DarkCloudCoverBearish = false
if (C_UpTrend[1] and C_WhiteBody[1] and C_LongBody[1]) and (C_BlackBody and open >= high[1] and close < C_BodyMiddle[1] and close > open[1])
    C_DarkCloudCoverBearish := true
alertcondition(C_DarkCloudCoverBearish, title = "乌云盖顶 - 看跌", message = "检测到新的乌云盖顶 - 看跌形态")
if C_DarkCloudCoverBearish and DarkCloudCoverInput and (("看跌" == CandleType) or CandleType == "两者")

    var ttBearishDarkCloudCover = "乌云盖顶\n乌云盖顶是两根蜡烛组成的看跌反转蜡烛形态，出现在上升趋势中。第一根蜡烛是长阳线，第二根蜡烛是长阴线，开盘价高于前一根蜡烛的最高点，并且收盘价低于前一根蜡烛的中点。这种形态表明，市场趋势可能从上升转为下跌。"
    label.new(bar_index, patternLabelPosHigh, text="乌云盖顶", style=label.style_label_down, color = label_color_2_candle_bearish, textcolor=color.white, tooltip = ttBearishDarkCloudCover)

C_HammerBullishNumberOfCandles = 1
C_HammerBullish = false
if C_SmallBody and C_Body > 0 and C_BodyLo > hl2 and C_DnShadow >= C_Factor * C_Body and not C_HasUpShadow
    if C_DownTrend
        C_HammerBullish := true
alertcondition(C_HammerBullish, title = "锤子 - 看涨", message = "检测到新的锤子 - 看涨形态")
if C_HammerBullish and HammerInput and (("看涨" == CandleType) or CandleType == "两者")

    var ttBullishHammer = "锤子\n当市场在开盘后下跌，但随后大幅上涨并在收盘价接近日内高点时形成锤子形态。如果这种形态出现在下跌趋势中，它被称为锤子蜡烛，暗示可能的反转。"
    label.new(bar_index, patternLabelPosLow, text="锤子", style=label.style_label_up, color = label_color_1_candle_bullish, textcolor=color.white, tooltip = ttBullishHammer)

C_ShootingStarBearishNumberOfCandles = 1
C_ShootingStarBearish = false
if C_SmallBody and C_Body > 0 and C_BodyHi < hl2 and C_UpShadow >= C_Factor * C_Body and not C_HasDnShadow
    if C_UpTrend
        C_ShootingStarBearish := true
alertcondition(C_ShootingStarBearish, title = "流星 - 看跌", message = "检测到新的流星 - 看跌形态")
if C_ShootingStarBearish and ShootingStarInput and (("看跌" == CandleType) or CandleType == "两者")

    var ttBearishShootingStar = "流星\n流星形态出现在上升趋势中，通常由一根带有较长上影线的小实体蜡烛组成。开盘后价格上冲，但最终回落并接近开盘价收盘，暗示市场可能发生反转。"
    label.new(bar_index, patternLabelPosHigh, text="流星", style=label.style_label_down, color = label_color_1_candle_bearish, textcolor=color.white, tooltip = ttBearishShootingStar)

// 晨星形态（看涨）
C_MorningStarBullishNumberOfCandles = 3
C_MorningStarBullish = false
if C_LongBody[2] and C_SmallBody[1] and C_LongBody
    if C_DownTrend and C_BlackBody[2] and C_BodyHi[1] < C_BodyLo[2] and C_WhiteBody and C_BodyHi >= C_BodyMiddle[2] and C_BodyHi < C_BodyHi[2] and C_BodyHi[1] < C_BodyLo
        C_MorningStarBullish := true
alertcondition(C_MorningStarBullish, title = "晨星 – 看涨", message = "检测到新的晨星 – 看涨形态")
if C_MorningStarBullish and MorningStarInput and (("看涨" == CandleType) or CandleType == "两者")
    
    var ttBullishMorningStar = "晨星\n一个三日看涨反转形态，由三根K线组成：首先是延续当前下跌趋势的长阴线。接下来是一根短小的中间K线，其开盘价低于前一日。最后是一根长阳线，其开盘价高于前一日，收盘价高于第一天K线实体的中点。"
    label.new(bar_index, patternLabelPosLow, text="晨星", style=label.style_label_up, color=label_color_3_candle_bullish, textcolor=color.white, tooltip=ttBullishMorningStar)

// 黄昏星形态（看跌）
C_EveningStarBearishNumberOfCandles = 3
C_EveningStarBearish = false
if C_LongBody[2] and C_SmallBody[1] and C_LongBody
    if C_UpTrend and C_WhiteBody[2] and C_BodyLo[1] > C_BodyHi[2] and C_BlackBody and C_BodyLo <= C_BodyMiddle[2] and C_BodyLo > C_BodyLo[2] and C_BodyLo[1] > C_BodyHi
        C_EveningStarBearish := true
alertcondition(C_EveningStarBearish, title = "黄昏星 – 看跌", message = "检测到新的黄昏星 – 看跌形态")
if C_EveningStarBearish and EveningStarInput and (("看跌" == CandleType) or CandleType == "两者")
    var ttBearishEveningStar = "黄昏星\n这一K线形态在上升趋势中延续，第一根为长阳线，随后是带缺口的小实体K线，最后以阴线收盘，收盘价低于第一天K线实体的中点。"
    label.new(bar_index, patternLabelPosHigh, text="黄昏星", style=label.style_label_down, color=label_color_3_candle_bearish, textcolor=color.white, tooltip=ttBearishEveningStar)

// 白色光头光脚（看涨）
C_MarubozuWhiteBullishNumberOfCandles = 1
C_MarubozuShadowPercentWhite = 5.0
C_MarubozuWhiteBullish = C_WhiteBody and C_LongBody and C_UpShadow <= C_MarubozuShadowPercentWhite/100*C_Body and C_DnShadow <= C_MarubozuShadowPercentWhite/100*C_Body and C_WhiteBody
alertcondition(C_MarubozuWhiteBullish, title = "光头光脚白色 – 看涨", message = "检测到新的光头光脚白色 – 看涨形态")
if C_MarubozuWhiteBullish and MarubozuWhiteInput and (("看涨" == CandleType) or CandleType == "两者")
    var ttBullishMarubozuWhite = "光头光脚白色\n光头光脚白色K线是一种没有上下影线的实体K线。‘光头光脚’在日语中意为‘剃光’或‘光头’。其他地方也可能称其为‘光头’或‘剃头’K线。"
    label.new(bar_index, patternLabelPosLow, text="白光头", style=label.style_label_up, color=label_color_1_candle_bullish, textcolor=color.white, tooltip=ttBullishMarubozuWhite)

// 黑色光头光脚（看跌）
C_MarubozuBlackBearishNumberOfCandles = 1
C_MarubozuShadowPercentBearish = 5.0
C_MarubozuBlackBearish = C_BlackBody and C_LongBody and C_UpShadow <= C_MarubozuShadowPercentBearish/100*C_Body and C_DnShadow <= C_MarubozuShadowPercentBearish/100*C_Body and C_BlackBody
alertcondition(C_MarubozuBlackBearish, title = "光头光脚黑色 – 看跌", message = "检测到新的光头光脚黑色 – 看跌形态")
if C_MarubozuBlackBearish and MarubozuBlackInput and (("看跌" == CandleType) or CandleType == "两者")
    var ttBearishMarubozuBlack = "光头光脚黑色\n这是一种没有上下影线的K线，只有红色实体。在日语中，这个名字的意思是‘剃光’或‘光头’。这种K线也可以被称为‘光头’或‘剃头’。"
    label.new(bar_index, patternLabelPosHigh, text="黑光头", style=label.style_label_down, color=label_color_1_candle_bearish, textcolor=color.white, tooltip=ttBearishMarubozuBlack)

// 十字星形态
C_DojiNumberOfCandles = 1
C_DragonflyDoji = C_IsDojiBody and C_UpShadow <= C_Body
C_GravestoneDojiOne = C_IsDojiBody and C_DnShadow <= C_Body
alertcondition(C_Doji and not C_DragonflyDoji and not C_GravestoneDojiOne, title = "十字星", message = "检测到新的十字星形态")
if C_Doji and not C_DragonflyDoji and not C_GravestoneDojiOne and DojiInput
    var ttDoji = "十字星\n当一个证券的开盘和收盘价格几乎相等时，会形成十字星K线。上下影线的长度可能会变化，使得K线看起来像一个十字、倒十字或加号。十字星K线展示了买卖双方在一种拉锯战中的犹豫不决。随着价格在盘中到达开盘水平之上或之下，收盘价通常在开盘附近或开盘水平。"
    label.new(bar_index, patternLabelPosLow, text="十字", style=label.style_label_up, color=label_color_1_candle_neutral, textcolor=color.white, tooltip=ttDoji)

// 墓碑十字星（看跌）
C_GravestoneDojiBearishNumberOfCandles = 1
C_GravestoneDojiBearish = C_IsDojiBody and C_DnShadow <= C_Body
alertcondition(C_GravestoneDojiBearish, title = "墓碑十字星 – 看跌", message = "检测到新的墓碑十字星 – 看跌形态")
if C_GravestoneDojiBearish and GravestoneDojiInput and (("看跌" == CandleType) or CandleType == "两者")
    var ttBearishGravestoneDoji = "墓碑十字星\n当十字星出现在或接近平日的低点时，会形成墓碑十字星。"
    label.new(bar_index, patternLabelPosHigh, text="墓碑十字", style=label.style_label_down, color=label_color_1_candle_bearish, textcolor=color.white, tooltip=ttBearishGravestoneDoji)

// 蜻蜓十字星（看涨）
C_DragonflyDojiBullishNumberOfCandles = 1
C_DragonflyDojiBullish = C_IsDojiBody and C_UpShadow <= C_Body
alertcondition(C_DragonflyDojiBullish, title = "蜻蜓十字星 – 看涨", message = "检测到新的蜻蜓十字星 – 看涨形态")
if C_DragonflyDojiBullish and DragonflyDojiInput and (("看涨" == CandleType) or CandleType == "两者")
    var ttBullishDragonflyDoji = "蜻蜓十字星\n类似于其他十字星，这种特定的十字星也常在关键市场时刻出现。此十字星的开盘和收盘价格在当日的最高点。"
    label.new(bar_index, patternLabelPosLow, text="蜻蜓十字", style=label.style_label_up, color=label_color_1_candle_bullish, textcolor=color.white, tooltip=ttBullishDragonflyDoji)

// 十字孕线（看涨）
C_HaramiCrossBullishNumberOfCandles = 2
C_HaramiCrossBullish = C_LongBody[1] and C_BlackBody[1] and C_DownTrend[1] and C_IsDojiBody and high <= C_BodyHi[1] and low >= C_BodyLo[1]
alertcondition(C_HaramiCrossBullish, title = "十字孕线 – 看涨", message = "检测到新的十字孕线 – 看涨形态")
if C_HaramiCrossBullish and HaramiCrossInput and (("看涨" == CandleType) or CandleType == "两者")
    var ttBullishHaramiCross = "十字孕线\n这种K线形态是看涨孕线形态的变体。它出现在下跌趋势中。这一两日K线形态由一个完全包围在红色实体内的十字线组成。"
    label.new(bar_index, patternLabelPosLow, text="孕线十字", style=label.style_label_up, color=label_color_bullish, textcolor=color.white, tooltip=ttBullishHaramiCross)

// 十字孕线（看跌）
C_HaramiCrossBearishNumberOfCandles = 2
C_HaramiCrossBearish = C_LongBody[1] and C_WhiteBody[1] and C_UpTrend[1] and C_IsDojiBody and high <= C_BodyHi[1] and low >= C_BodyLo[1]
alertcondition(C_HaramiCrossBearish, title = "十字孕线 – 看跌", message = "检测到新的十字孕线 – 看跌形态")
if C_HaramiCrossBearish and HaramiCrossInput and (("看跌" == CandleType) or CandleType == "两者")
    var ttBearishHaramiCross = "十字孕线\n这种K线形态是看跌孕线形态的变体。它出现在上升趋势中。这一两日K线形态包含一个完全被绿色实体包围的十字线。十字线表明卖方出现了一些犹豫，并暗示趋势可能会反转。"
    label.new(bar_index, patternLabelPosHigh, text="孕线十字", style=label.style_label_down, color=label_color_bearish, textcolor=color.white, tooltip=ttBearishHaramiCross)


// 孕线形态（看涨）
C_HaramiBullishNumberOfCandles = 2
C_HaramiBullish = C_LongBody[1] and C_BlackBody[1] and C_DownTrend[1] and C_WhiteBody and C_SmallBody and high <= C_BodyHi[1] and low >= C_BodyLo[1]
alertcondition(C_HaramiBullish, title = "孕线 – 看涨", message = "检测到新的孕线 – 看涨形态")
if C_HaramiBullish and HaramiInput and (("看涨" == CandleType) or CandleType == "两者")
    var ttBullishHarami = "孕线\n这个两日K线形态由一个小阳线组成，其实体完全涵盖在之前的红色实体K线内。"
    label.new(bar_index, patternLabelPosLow, text="孕线", style=label.style_label_up, color=label_color_bullish, textcolor=color.white, tooltip=ttBullishHarami)

// 孕线形态（看跌）
C_HaramiBearishNumberOfCandles = 2
C_HaramiBearish = C_LongBody[1] and C_WhiteBody[1] and C_UpTrend[1] and C_BlackBody and C_SmallBody and high <= C_BodyHi[1] and low >= C_BodyLo[1]
alertcondition(C_HaramiBearish, title = "孕线 – 看跌", message = "检测到新的孕线 – 看跌形态")
if C_HaramiBearish and HaramiInput and (("看跌" == CandleType) or CandleType == "两者")
    var ttBearishHarami = "孕线\n这个两日K线形态由一个小阴线组成，其实体完全涵盖在之前的绿色实体K线内。"
    label.new(bar_index, patternLabelPosHigh, text="孕线", style=label.style_label_down, color=label_color_bearish, textcolor=color.white, tooltip=ttBearishHarami)

// 长下影线形态（看涨）
C_LongLowerShadowBullishNumberOfCandles = 1
C_LongLowerShadowPercent = 75.0
C_LongLowerShadowBullish = C_DnShadow > C_Range / 100 * C_LongLowerShadowPercent
alertcondition(C_LongLowerShadowBullish, title = "长下影线 – 看涨", message = "检测到新的长下影线 – 看涨形态")
if C_LongLowerShadowBullish and LongLowerShadowInput and (("看涨" == CandleType) or CandleType == "两者")
    var ttBullishLongLowerShadow = "长下影线\nK线呈现长下影线和短上影线，表示卖方在前期主宰市场，从而降低价格。"
    label.new(bar_index, patternLabelPosLow, text="长下影", style=label.style_label_up, color=label_color_1_candle_bullish, textcolor=color.white, tooltip=ttBullishLongLowerShadow)

// 长上影线形态（看跌）
C_LongUpperShadowBearishNumberOfCandles = 1
C_LongShadowPercent = 75.0
C_LongUpperShadowBearish = C_UpShadow > C_Range / 100 * C_LongShadowPercent
alertcondition(C_LongUpperShadowBearish, title = "长上影线 – 看跌", message = "检测到新的长上影线 – 看跌形态")
if C_LongUpperShadowBearish and LongUpperShadowInput and (("看跌" == CandleType) or CandleType == "两者")
    var ttBearishLongUpperShadow = "长上影线\nK线呈现长上影线和短下影线，表示买方在前期主宰市场，从而提升价格。"
    label.new(bar_index, patternLabelPosHigh, text="长上影", style=label.style_label_down, color=label_color_1_candle_bearish, textcolor=color.white, tooltip=ttBearishLongUpperShadow)

// 白色纺锤线
C_SpinningTopWhiteNumberOfCandles = 1
C_SpinningTopWhitePercent = 34.0
C_IsSpinningTopWhite = C_DnShadow >= C_Range / 100 * C_SpinningTopWhitePercent and C_UpShadow >= C_Range / 100 * C_SpinningTopWhitePercent and not C_IsDojiBody
C_SpinningTopWhite = C_IsSpinningTopWhite and C_WhiteBody
alertcondition(C_SpinningTopWhite, title = "白色纺锤线", message = "检测到新的白色纺锤线形态")
if C_SpinningTopWhite and SpinningTopWhiteInput
    var ttSpinningTopWhite = "白色纺锤线\n白色纺锤线是K线实体小、绿色并具有长上下影线的形态，通常表示买卖双方的犹豫不决。"
    label.new(bar_index, patternLabelPosLow, text="白纺锤", style=label.style_label_up, color=label_color_1_candle_neutral, textcolor=color.white, tooltip=ttSpinningTopWhite)

// 黑色纺锤线
C_SpinningTopBlackNumberOfCandles = 1
C_SpinningTopBlackPercent = 34.0
C_IsSpinningTop = C_DnShadow >= C_Range / 100 * C_SpinningTopBlackPercent and C_UpShadow >= C_Range / 100 * C_SpinningTopBlackPercent and not C_IsDojiBody
C_SpinningTopBlack = C_IsSpinningTop and C_BlackBody
alertcondition(C_SpinningTopBlack, title = "黑色纺锤线", message = "检测到新的黑色纺锤线形态")
if C_SpinningTopBlack and SpinningTopBlackInput
    var ttSpinningTopBlack = "黑色纺锤线\n黑色纺锤线是K线实体小、阴线并具有长上下影线的形态，通常表示犹豫不决。"
    label.new(bar_index, patternLabelPosLow, text="黑纺锤", style=label.style_label_up, color=label_color_1_candle_neutral, textcolor=color.white, tooltip=ttSpinningTopBlack)

// 三白兵（看涨）
C_ThreeWhiteSoldiersBullishNumberOfCandles = 3
C_3WSld_ShadowPercent = 5.0
C_3WSld_HaveNotUpShadow = C_Range * C_3WSld_ShadowPercent / 100 > C_UpShadow
C_ThreeWhiteSoldiersBullish = false
if C_LongBody and C_LongBody[1] and C_LongBody[2]
    if C_WhiteBody and C_WhiteBody[1] and C_WhiteBody[2]
        C_ThreeWhiteSoldiersBullish := close > close[1] and close[1] > close[2] and open < close[1] and open > open[1] and open[1] < close[2] and open[1] > open[2] and C_3WSld_HaveNotUpShadow and C_3WSld_HaveNotUpShadow[1] and C_3WSld_HaveNotUpShadow[2]
alertcondition(C_ThreeWhiteSoldiersBullish, title = "三白兵 – 看涨", message = "检测到新的三白兵 – 看涨形态")
if C_ThreeWhiteSoldiersBullish and ThreeWhiteSoldiersInput and (("看涨" == CandleType) or CandleType == "两者")
    var ttBullishThreeWhiteSoldiers = "三白兵\n这是一种由三根连续的长阳线组成的看涨反转形态。每一根K线的开盘价在前一根K线的实体内，并接近每日高点收盘。"
    label.new(bar_index, patternLabelPosLow, text="三白兵", style=label.style_label_up, color=label_color_3_candle_bullish, textcolor=color.white, tooltip=ttBullishThreeWhiteSoldiers)

// 三黑乌鸦（看跌）
C_ThreeBlackCrowsBearishNumberOfCandles = 3
C_3BCrw_ShadowPercent = 5.0
C_3BCrw_HaveNotDnShadow = C_Range * C_3BCrw_ShadowPercent / 100 > C_DnShadow
C_ThreeBlackCrowsBearish = false
if C_LongBody and C_LongBody[1] and C_LongBody[2]
    if C_BlackBody and C_BlackBody[1] and C_BlackBody[2]
        C_ThreeBlackCrowsBearish := close < close[1] and close[1] < close[2] and open > close[1] and open < open[1] and open[1] > close[2] and open[1] < open[2] and C_3BCrw_HaveNotDnShadow and C_3BCrw_HaveNotDnShadow[1] and C_3BCrw_HaveNotDnShadow[2]
alertcondition(C_ThreeBlackCrowsBearish, title = "三黑乌鸦 – 看跌", message = "检测到新的三黑乌鸦 – 看跌形态")
if C_ThreeBlackCrowsBearish and ThreeBlackCrowsInput and (("看跌" == CandleType) or CandleType == "两者")
    var ttBearishThreeBlackCrows = "三黑乌鸦\n这是一种由三根连续的长阴线组成的看跌反转形态。每一根K线的开盘价在前一根K线的实体内，并接近每日低点收盘。"
    label.new(bar_index, patternLabelPosHigh, text="三黑乌鸦", style=label.style_label_down, color=label_color_3_candle_bearish, textcolor=color.white, tooltip=ttBearishThreeBlackCrows)

// 吞没形态（看涨）
C_EngulfingBullishNumberOfCandles = 2
C_EngulfingBullish = C_DownTrend and C_WhiteBody and C_LongBody and C_BlackBody[1] and C_SmallBody[1] and close >= open[1] and open <= close[1] and (close > open[1] or open < close[1])
alertcondition(C_EngulfingBullish, title = "吞没 – 看涨", message = "检测到新的吞没 – 看涨形态")
if C_EngulfingBullish and EngulfingInput and (("看涨" == CandleType) or CandleType == "两者")
    var ttBullishEngulfing = "吞没\n在特定的下跌趋势末端，常出现反转形态。第一天使用小实体K线，第二天的K线实体全面超过前一天的实体，并在趋势的相反方向收盘。虽然类似于外部反转形态，但不需要完全超过价格区间（高低），只需要吞没开盘和收盘价即可。"
    label.new(bar_index, patternLabelPosLow, text="吞没", style=label.style_label_up, color=label_color_bullish, textcolor=color.white, tooltip=ttBullishEngulfing)

// 吞没形态（看跌）
C_EngulfingBearishNumberOfCandles = 2
C_EngulfingBearish = C_UpTrend and C_BlackBody and C_LongBody and C_WhiteBody[1] and C_SmallBody[1] and close <= open[1] and open >= close[1] and (close < open[1] or open > close[1])
alertcondition(C_EngulfingBearish, title = "吞没 – 看跌", message = "检测到新的吞没 – 看跌形态")
if C_EngulfingBearish and EngulfingInput and (("看跌" == CandleType) or CandleType == "两者")
    var ttBearishEngulfing = "吞没\n在特定的上升趋势末端，常出现反转形态。第一天使用小实体K线，第二天的K线实体全面超过前一天的实体，并在趋势的相反方向收盘。虽然类似于外部反转形态，但不需要完全超过价格区间（高低），只需要吞没开盘和收盘价即可。"
    label.new(bar_index, patternLabelPosHigh, text="吞没", style=label.style_label_down, color=label_color_bearish, textcolor=color.white, tooltip=ttBearishEngulfing)

// 弃婴形态（看涨）
C_AbandonedBabyBullishNumberOfCandles = 3
C_AbandonedBabyBullish = C_DownTrend[2] and C_BlackBody[2] and C_IsDojiBody[1] and low[2] > high[1] and C_WhiteBody and high[1] < low
alertcondition(C_AbandonedBabyBullish, title = "弃婴 – 看涨", message = "检测到新的弃婴 – 看涨形态")
if C_AbandonedBabyBullish and AbandonedBabyInput and (("看涨" == CandleType) or CandleType == "两者")
    var ttBullishAbandonedBaby = "弃婴\n这种K线形态作为反转形态非常罕见。图形中的第一根是大阴线，接着是向下跳空的十字星。然后是一个上涨并迅速走高的阳线。"
    label.new(bar_index, patternLabelPosLow, text="弃婴", style=label.style_label_up, color=label_color_3_candle_bullish, textcolor=color.white, tooltip=ttBullishAbandonedBaby)

// 弃婴形态（看跌）
C_AbandonedBabyBearishNumberOfCandles = 3
C_AbandonedBabyBearish = C_UpTrend[2] and C_WhiteBody[2] and C_IsDojiBody[1] and high[2] < low[1] and C_BlackBody and low[1] > high
alertcondition(C_AbandonedBabyBearish, title = "弃婴 – 看跌", message = "检测到新的弃婴 – 看跌形态")
if C_AbandonedBabyBearish and AbandonedBabyInput and (("看跌" == CandleType) or CandleType == "两者")
    var ttBearishAbandonedBaby = "弃婴\n一个看跌弃婴是指价格经常信号下降反转的特定K线形态。形成时，十字星形态的最低价和之前K线之间存在间隙。先前K线为绿色且有小影线，之后是红色且有小影线的K线，且上下影线完全跨越前三日和后一天的影线则有弃婴形态效果。"
    label.new(bar_index, patternLabelPosHigh, text="弃婴", style=label.style_label_down, color=label_color_3_candle_bearish, textcolor=color.white, tooltip=ttBearishAbandonedBaby)

// 三星形态（看涨）
C_TriStarBullishNumberOfCandles = 3
C_3DojisBullish = C_Doji[2] and C_Doji[1] and C_Doji
C_BodyGapUpBullish = C_BodyHi[1] < C_BodyLo
C_BodyGapDnBullish = C_BodyLo[1] > C_BodyHi
C_TriStarBullish = C_3DojisBullish and C_DownTrend[2] and C_BodyGapDnBullish[1] and C_BodyGapUpBullish
alertcondition(C_TriStarBullish, title = "三星 – 看涨", message = "检测到新的三星 – 看涨形态")
if C_TriStarBullish and TriStarInput and (("看涨" == CandleType) or CandleType == "两者")
    var ttBullishTriStar = "三星\n看涨的三星形态可以形成于三个十字星连续出现在较长的下跌趋势末尾时。第一个十字星显示多空之间的犹豫不决。第二个十字星在趋势方向上有跳空。第三个十字星在市场情绪改变后在趋势方向的对面开盘。每个十字星都有影线，表示波动性的临时缩减。"
    label.new(bar_index, patternLabelPosLow, text="三星", style=label.style_label_up, color=label_color_3_candle_bullish, textcolor=color.white, tooltip=ttBullishTriStar)

// 三星形态（看跌）
C_TriStarBearishNumberOfCandles = 3
C_3Dojis = C_Doji[2] and C_Doji[1] and C_Doji
C_BodyGapUp = C_BodyHi[1] < C_BodyLo
C_BodyGapDn = C_BodyLo[1] > C_BodyHi
C_TriStarBearish = C_3Dojis and C_UpTrend[2] and C_BodyGapUp[1] and C_BodyGapDn
alertcondition(C_TriStarBearish, title = "三星 – 看跌", message = "检测到新的三星 – 看跌形态")
if C_TriStarBearish and TriStarInput and (("看跌" == CandleType) or CandleType == "两者")
    var ttBearishTriStar = "三星\n这种特定形态可以在三个十字星连续出现在较长的上升趋势末尾时形成。第一个十字星显示多空之间的犹豫不决。第二个十字星在趋势方向上有跳空。第三个十字星在市场情绪改变后在趋势方向的对面开盘。每个十字星都有影线，表示波动性的临时缩减。"
    label.new(bar_index, patternLabelPosHigh, text="三星", style=label.style_label_down, color=label_color_3_candle_bearish, textcolor=color.white, tooltip=ttBearishTriStar)

// 跳空形态（看涨）
C_KickingBullishNumberOfCandles = 2
C_MarubozuShadowPercent = 5.0
C_Marubozu = C_LongBody and C_UpShadow <= C_MarubozuShadowPercent / 100 * C_Body and C_DnShadow <= C_MarubozuShadowPercent / 100 * C_Body
C_MarubozuWhiteBullishKicking = C_Marubozu and C_WhiteBody
C_MarubozuBlackBullish = C_Marubozu and C_BlackBody
C_KickingBullish = C_MarubozuBlackBullish[1] and C_MarubozuWhiteBullishKicking and high[1] < low
alertcondition(C_KickingBullish, title = "跳空 – 看涨", message = "检测到新的跳空 – 看涨形态")
if C_KickingBullish and KickingInput and (("看涨" == CandleType) or CandleType == "两者")
    var ttBullishKicking = "跳空\n第一天K线是一根看跌的光头光脚阴线，几乎没有上影线或下影线，开盘价在当天最高点，收盘价在最低点。第二天是一根看涨的光头光脚阳线，几乎没有上影线或下影线，开盘价在当天最低点，收盘价在最高点。此外，第二天大幅向上跳空，开盘价高于前一天的开盘价。这个日本人称之为窗口的跳空出现在前两天的看涨K线之间。"
    label.new(bar_index, patternLabelPosLow, text="踢线", style=label.style_label_up, color=label_color_bullish, textcolor=color.white, tooltip=ttBullishKicking)

// 跳空形态（看跌）
C_KickingBearishNumberOfCandles = 2
C_MarubozuBullishShadowPercent = 5.0
C_MarubozuBearishKicking = C_LongBody and C_UpShadow <= C_MarubozuBullishShadowPercent / 100 * C_Body and C_DnShadow <= C_MarubozuBullishShadowPercent / 100 * C_Body
C_MarubozuWhiteBearish = C_MarubozuBearishKicking and C_WhiteBody
C_MarubozuBlackBearishKicking = C_MarubozuBearishKicking and C_BlackBody
C_KickingBearish = C_MarubozuWhiteBearish[1] and C_MarubozuBlackBearishKicking and low[1] > high
alertcondition(C_KickingBearish, title = "跳空 – 看跌", message = "检测到新的跳空 – 看跌形态")
if C_KickingBearish and KickingInput and (("看跌" == CandleType) or CandleType == "两者")

    var ttBearishKicking = "跳空\n一个看跌的跳空形态将发生，随后信号反转为一个新的下跌趋势。第一天的K线是一根看涨的光头光脚阳线。第二天大幅向下跳空，开盘价低于前一天的开盘价。这里前一天和第二天之间的看跌K线之间存在一个显著间隙。"
    label.new(bar_index, patternLabelPosHigh, text="踢线", style=label.style_label_down, color=label_color_bearish, textcolor=color.white, tooltip=ttBearishKicking)
    var ttAllCandlestickPatterns = "所有K线形态\n"
    label.new(bar_index, patternLabelPosLow, text="合集", style=label.style_label_up, color=label_color_neutral, textcolor=color.white, tooltip=ttAllCandlestickPatterns)
