//@version=5
indicator("MA+EMA", overlay=true)

// === 移动平均线（MA） ===
ma5  = ta.sma(close, 5)
ma20 = ta.sma(close, 20)
ma100 = ta.sma(close, 100)
ma200 = ta.sma(close, 200)

// 计算过去三天的平均值
ma20_avg3 = (ta.sma(close[1], 20) + ta.sma(close[2], 20) + ta.sma(close[3], 20)) / 3
ma100_avg3 = (ta.sma(close[1], 100) + ta.sma(close[2], 100) + ta.sma(close[3], 100)) / 3
ma200_avg3 = (ta.sma(close[1], 200) + ta.sma(close[2], 200) + ta.sma(close[3], 200)) / 3

// 计算 MA 的变化（斜率）：当前均值与三天均值比较
ma20_slope_up = ma20 > ma20_avg3
ma100_slope_up = ma100 > ma100_avg3
ma200_slope_up = ma200 > ma200_avg3

ma20_slope_down = ma20 < ma20_avg3
ma100_slope_down = ma100 < ma100_avg3
ma200_slope_down = ma200 < ma200_avg3

// 定义 MA 趋势条件，包括斜率判断
is_bullish_ma = ma20 > ma100 and ma100 > ma200 and ma20_slope_up and ma100_slope_up and ma200_slope_up
is_bearish_ma = ma20 < ma100 and ma100 < ma200 and ma20_slope_down and ma100_slope_down and ma200_slope_down

// 设置 MA 颜色
ma_color = is_bullish_ma ? color.new(color.green, 30) : 
           is_bearish_ma ? color.new(color.red, 30) : 
           color.rgb(255, 173, 59, 77)

// 绘制 MA 线，线条粗细随周期递增
plot(ma5, title="MA 5", color=ma_color, linewidth=1)
plot(ma20, title="MA 20", color=ma_color, linewidth=2)
plot(ma100, title="MA 100", color=ma_color, linewidth=3)
plot(ma200, title="MA 200", color=ma_color, linewidth=4)

// === 指数移动平均线（EMA） ===
ema144 = ta.ema(close, 144)
ema233 = ta.ema(close, 233)
ema286 = ta.ema(close, 286)
ema333 = ta.ema(close, 333)

// 绘制 EMA 线并分配给变量
p_ema144 = plot(ema144, title="EMA 144", color=color.green, linewidth=2)
p_ema233 = plot(ema233, title="EMA 233", color=color.green, linewidth=2)
p_ema286 = plot(ema286, title="EMA 286", color=color.red, linewidth=2)
p_ema333 = plot(ema333, title="EMA 333", color=color.red, linewidth=2)

// 定义 EMA 趋势条件
bullish_ema = ema144 > ema286 and ema144 > ema333 and ema233 > ema286 and ema233 > ema333
bearish_ema = ema286 > ema144 and ema286 > ema233 and ema333 > ema144 and ema333 > ema233

// 设置填充颜色的透明度
fill_green_color = bullish_ema ? color.new(color.green, 50) : 
                   bearish_ema ? color.new(color.green, 90) : 
                   color.new(color.green, 70)

fill_red_color = bullish_ema ? color.new(color.red, 90) : 
                 bearish_ema ? color.new(color.red, 50) : 
                 color.new(color.red, 70)

// 绿色填充：EMA144 和 EMA233 之间
fill(p_ema144, p_ema233, color=fill_green_color, title="Fill Green between EMA144 and EMA233")
// 红色填充：EMA286 和 EMA333 之间
fill(p_ema286, p_ema333, color=fill_red_color, title="Fill Red between EMA286 and EMA333")
