// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © PrasadJoshi12

//@version=5
// ╔══════════════════════════════════════════════════════════════════════════════╗
// ║                              使用说明                                        ║
// ╚══════════════════════════════════════════════════════════════════════════════╝
// 
// 功能概述：
// 这个指标可以同时监控20个不同的交易品种，并显示它们相对于基准品种的强弱关系和趋势状态。
//
// 表格说明：
// 1. Symbol: 交易品种代码
// 2. Trend: 趋势方向
//    - 绿色 "Trend Up": 快速均线在慢速均线之上，上升趋势
//    - 红色 "Trend Down": 快速均线在慢速均线之下，下降趋势
// 3. RSI: RSI指标值
// 4. 相对强弱: 相对准品种的强弱程度
//    - 深绿色: 强于基准超过0.5%
//    - 浅绿色: 强于基准0-0.5%
//    - 深红色: 弱于基准超过0.5%
//    - 浅红色: 弱于基准0-0.5%
//    - 灰色: 与基准持平
// 5. Gain/Loss Since Cross: 自上次均线交叉以来的涨跌幅
//
// RSI趋势栏（蓝色和红色方块）：
// - 蓝色方块: 强势RSI趋势
//   条件: RSI(8) >= RSI(14) 且 RSI(14) >= RSI(19) 且 价格 >= EMA(9)
// - 红色方块: 弱势RSI趋势
//   条件: RSI(8) < RSI(14) 且 RSI(14) < RSI(19) 且 价格 < EMA(9)
// - 黑色方块: 中性趋势
//
// 设置选项：
// 1. 相对强弱设置
//    - 相对强弱计算周期：计算相对强弱的周期长度
// 2. MA选项
//    - 快速移动平均线：用于趋势判断的快速EMA周期
//    - 慢速移动平均线：用于趋势判断的慢速EMA周期
// 3. 表格选项
//    - Size：表格大小
//    - Enable Dark Theme：启用暗色主题
//    - RSI Trend Bars：显示的RSI趋势历史长度
//
// 使用建议：
// 1. 关注相对强弱列的颜色深浅，快速识别强势品种
// 2. 结合RSI趋势栏的蓝红变化，判断趋势的持续性
// 3. 通过Gain/Loss Since Cross列观察趋势的强度
// 4. 使用RSI趋势历观察趋势的演变过程
//
// 注意事项：
// 1. 相强实时计算的，反映当bar的相对表现
// 2. RSI趋势是多重RSI的综合判断，可能会有滞后性
// 3. 建议在多个时间周期下观察，获得更全面的市场视角

indicator(title = "Multi-Sector Trend Analysis", shorttitle = "Sector Trend Analyzer", overlay = false)

// 修改参数输入分组和格式
string SymbolsGroup = "══════════════ Symbols ══════════════"
BenchmarkSymbol = input.symbol(defval = "SZSE:399006", title = "基准标的", group = SymbolsGroup)
BenchmarkName = input.string(defval = "创业板指", title = "基准名称", group = SymbolsGroup)

// 在Symbol输入部分增加3个自定义标的
// 在SymbolsGroup下添加以下代码（放在Symbol1之前）:
CustomSymbol1 = input.symbol(defval = "", title = "自定义标的 1", inline="custom1", group = SymbolsGroup)
CustomSymbol1Name = input.string(defval = "", title = "名称", inline="custom1", group = SymbolsGroup)

CustomSymbol2 = input.symbol(defval = "", title = "自定义标的 2", inline="custom2", group = SymbolsGroup)
CustomSymbol2Name = input.string(defval = "", title = "名称", inline="custom2", group = SymbolsGroup)

CustomSymbol3 = input.symbol(defval = "", title = "自定义标的 3", inline="custom3", group = SymbolsGroup)
CustomSymbol3Name = input.string(defval = "", title = "名称", inline="custom3", group = SymbolsGroup)

// 科技类
Symbol1 = input.symbol(defval = "SSE_DLY:512480", title = "Symbol 1", inline="1", group = SymbolsGroup)
Symbol1Name = input.string(defval = "半导体ETF", title = "名称", inline="1", group = SymbolsGroup)

Symbol2 = input.symbol(defval = "SZSE:161631", title = "Symbol 2", inline="2", group = SymbolsGroup)
Symbol2Name = input.string(defval = "人工智能ETF", title = "名称", inline="2", group = SymbolsGroup)

Symbol3 = input.symbol(defval = "SZSE:399283", title = "Symbol 3", inline="3", group = SymbolsGroup)
Symbol3Name = input.string(defval = "机器人50", title = "名称", inline="3", group = SymbolsGroup)

Symbol4 = input.symbol(defval = "SSE_DLY:399804", title = "Symbol 4", inline="4", group = SymbolsGroup)
Symbol4Name = input.string(defval = "金融科技", title = "名称", inline="4", group = SymbolsGroup)

// 医疗类
Symbol5 = input.symbol(defval = "SZSE:159938", title = "Symbol 5", inline="5", group = SymbolsGroup)
Symbol5Name = input.string(defval = "医药ETF", title = "名称", inline="5", group = SymbolsGroup)

// 工业类
Symbol6 = input.symbol(defval = "SZSE:159886", title = "Symbol 6", inline="6", group = SymbolsGroup)
Symbol6Name = input.string(defval = "机械ETF", title = "名称", inline="6", group = SymbolsGroup)

Symbol7 = input.symbol(defval = "SSE_DLY:399803", title = "Symbol 7", inline="7", group = SymbolsGroup)
Symbol7Name = input.string(defval = "工业4.0", title = "名称", inline="7", group = SymbolsGroup)

// 消费类
Symbol8 = input.symbol(defval = "SZSE:159928", title = "Symbol 8", inline="8", group = SymbolsGroup)
Symbol8Name = input.string(defval = "消费ETF", title = "名称", inline="8", group = SymbolsGroup)

// 金融类
Symbol9 = input.symbol(defval = "SZSE:159841", title = "Symbol 9", inline="9", group = SymbolsGroup)
Symbol9Name = input.string(defval = "证券ETF", title = "名称", inline="9", group = SymbolsGroup)

Symbol10 = input.symbol(defval = "SZSE:159887", title = "Symbol 10", inline="10", group = SymbolsGroup)
Symbol10Name = input.string(defval = "银行ETF", title = "名称", inline="10", group = SymbolsGroup)

Symbol11 = input.symbol(defval = "SSE_DLY:931479", title = "Symbol 11", inline="11", group = SymbolsGroup)
Symbol11Name = input.string(defval = "非银金融证保", title = "名称", inline="11", group = SymbolsGroup)

Symbol12 = input.symbol(defval = "SZSE:399809", title = "Symbol 12", inline="12", group = SymbolsGroup)
Symbol12Name = input.string(defval = "保险ETF", title = "名称", inline="12", group = SymbolsGroup)

// 能源类
Symbol13 = input.symbol(defval = "SSE_DLY:515030", title = "Symbol 13", inline="13", group = SymbolsGroup)
Symbol13Name = input.string(defval = "新能车", title = "名称", inline="13", group = SymbolsGroup)

Symbol14 = input.symbol(defval = "SZSE:159980", title = "Symbol 14", inline="14", group = SymbolsGroup)
Symbol14Name = input.string(defval = "有色ETF", title = "名称", inline="14", group = SymbolsGroup)

Symbol15 = input.symbol(defval = "SSE_DLY:510410", title = "Symbol 15", inline="15", group = SymbolsGroup)
Symbol15Name = input.string(defval = "资源ETF", title = "名称", inline="15", group = SymbolsGroup)

Symbol16 = input.symbol(defval = "SZSE:159861", title = "Symbol 16", inline="16", group = SymbolsGroup)
Symbol16Name = input.string(defval = "环保碳中和", title = "名称", inline="16", group = SymbolsGroup)

// 其他
Symbol17 = input.symbol(defval = "SZSE:159869", title = "Symbol 17", inline="17", group = SymbolsGroup)
Symbol17Name = input.string(defval = "游戏ETF", title = "名称", inline="17", group = SymbolsGroup)

Symbol18 = input.symbol(defval = "SZSE:159974", title = "Symbol 18", inline="18", group = SymbolsGroup)
Symbol18Name = input.string(defval = "央企创新", title = "名称", inline="18", group = SymbolsGroup)

Symbol19 = input.symbol(defval = "SSE_DLY:000949", title = "Symbol 19", inline="19", group = SymbolsGroup)
Symbol19Name = input.string(defval = "农业ETF", title = "名称", inline="19", group = SymbolsGroup)

Symbol20 = input.symbol(defval = "SSE_DLY:931775", title = "Symbol 20", inline="20", group = SymbolsGroup)
Symbol20Name = input.string(defval = "房地产", title = "名称", inline="20", group = SymbolsGroup)

string MovingAvgGroup = "══════════════ MA Options ══════════════"
FastMALength = input.int(defval = 5, title = "快速均线周期", inline = "ma", group = MovingAvgGroup)
SlowMALength = input.int(defval = 20, title = "慢速均线周期", inline = "ma", group = MovingAvgGroup)

string RelativeStrengthGroup = "══════════════ 相对强弱设置 ══════════════"
RS_Length = input.int(defval = 10, title = "常周期", inline = "rs", tooltip = "计算相对强弱的常周期长度", group = RelativeStrengthGroup)
RS_Short_Length = input.int(defval = 3, title = "短周期", inline = "rs", tooltip = "计算相对强弱的短周期长度", group = RelativeStrengthGroup)

string OutputGroup = "══════════════ Table Options ══════════════"
OutTableSize = input.string("small", "表格大小", inline = "table", group = OutputGroup, options = ["tiny", "small", "normal", "large", "huge", "auto"])
OutDarkTheme = input.bool(defval = true, title = "暗色主题", inline = "table", group = OutputGroup)
RSITrendLookback = input.int(defval = 20, title = "历史Bar数", tooltip = "显示的历史数据长度", group = OutputGroup)

// Table options.
var TableFrameColor = OutDarkTheme ? color.new(#999999, 50) : color.rgb(241, 241, 241)
var TableBorderColor = OutDarkTheme ? color.new(#999999, 50) : color.rgb(241, 241, 241)
var TableTextSize = OutTableSize

queuePush(_queueArray, _queueSize, _value) =>
    if array.size(_queueArray) >= _queueSize
        array.shift(_queueArray)
    array.push(_queueArray, _value)

// 修改 TypeRelativeStrength 类型
type TypeRelativeStrength
    bool StrongVsBenchmark    // 强于基准
    bool WeakVsBenchmark      // 弱于基准
    float RelativeValue       // 相对强弱值（单根）

// 修改相对强弱判断函数
RelativeStrengthSignal(_symbol_close, _benchmark_close) =>
    _symbol_change = (_symbol_close - _symbol_close[1]) / _symbol_close[1] * 100
    _benchmark_change = (_benchmark_close - _benchmark_close[1]) / _benchmark_close[1] * 100
    _relative_strength = _symbol_change - _benchmark_change
    
    _is_strong = _relative_strength > 0.2 and not na(_relative_strength)
    _is_weak = _relative_strength < -0.2 and not na(_relative_strength)
    [_is_strong, _is_weak]

// Function to draw the table column
TableCell(_table, _row, _column, _value, _txtcolor) =>
    table.cell(table_id = _table, column = _column, row = _row, text = _value, text_color = _txtcolor, text_size = OutTableSize)

TableCellWithBG(_table, _row, _column, _value, _bg_color) =>
    _text_color = _bg_color == color.yellow ? color.black : color.white  // 黄色背景使用黑色文字
    table.cell(table_id = _table, column = _column, row = _row, text = _value, text_color = _text_color, text_size = OutTableSize, bgcolor = _bg_color)

// 修改表格显示函数
DisplaySymbolInfo(_table, int _row, string _symbol, string _name, bool _is_uptrend, float _rsi, float _gain_since_cross, float _relative_strength, float _relative_strength_short, float _single_bar_relative_strength, array<TypeRelativeStrength> _relative_strength_info) =>
    // 先显示历史列（反转顺序）
    for _i = 0 to _relative_strength_info.size()-1
        _info = array.get(_relative_strength_info, _i)  // 直接使用 _i 而不是反转
        _rs_val = _info.RelativeValue
        _color = na(_rs_val) ? color.new(color.yellow, 50) : 
             _rs_val >= 3.0 ? color.new(#00FF00, 50) :    // >= 3%
             _rs_val >= 2.0 ? color.new(#33FF33, 50) :    // >= 2%
             _rs_val >= 1.0 ? color.new(#66FF66, 50) :    // >= 1%
             _rs_val >= 0.5 ? color.new(#FFFF00, 50) :    // >= 0.5%
             _rs_val > -0.5 ? color.new(#FFFF66, 50) :    // -0.5% ~ 0.5%
             _rs_val > -1.0 ? color.new(#FFFF99, 50) :    // > -1%
             _rs_val > -2.0 ? color.new(#FF6666, 50) :    // > -2%
             _rs_val > -3.0 ? color.new(#FF3333, 50) :    // > -3%
             color.new(#FF0000, 50)                       // <= -3%
        
        _rs_value = na(_rs_val) ? "--" : str.tostring(_rs_val, "#.##")
        TableCellWithBG(_table = _table, _row = _row, _column = _i, _value = _rs_value, _bg_color = _color)
    
    // 然后是其他列
    _trend = _is_uptrend ? "Trend Up" : "Trend Down"
    _txt_color = _is_uptrend ? color.green : color.red
    
    // 常周期相对强弱显示
    _scaled_rs = _relative_strength / 100
    _rs_color = na(_relative_strength) ? color.gray : 
         _scaled_rs > 0 ? color.green :    // 正值显示绿色
         _scaled_rs < 0 ? color.red :      // 负值显示红色
         color.gray                        // 0值显示灰色
    _rs_text = na(_relative_strength) ? "--" : str.tostring(_scaled_rs, "#.##%")
    
    // 短周期相对强弱显示
    _scaled_rs_short = _relative_strength_short / 100
    _rs_short_color = na(_relative_strength_short) ? color.gray : 
         _scaled_rs_short > 0 ? color.green :    // 正值显示绿色
         _scaled_rs_short < 0 ? color.red :      // 负值显示红色
         color.gray                              // 0值显示灰色
    _rs_short_text = na(_relative_strength_short) ? "--" : str.tostring(_scaled_rs_short, "#.##%")
    
    // 修改RSI的颜色逻辑，反红绿关系
    _rsi_color = na(_rsi) ? color.gray : 
             _rsi >= 80 ? #FF0000 :    // 深红（极度超买）
             _rsi >= 70 ? #FF3333 :    // 中红（超买）
             _rsi >= 60 ? #FF6666 :    // 浅红（偏高）
             _rsi >= 50 ? #FFFF00 :    // 黄色（中性）
             _rsi >= 40 ? #FFFF66 :    // 浅黄（中性偏低）
             _rsi >= 30 ? #66FF66 :    // 浅绿（机会）
             _rsi >= 20 ? #33FF33 :    // 中绿（超卖）
             #00FF00                   // 深绿（极度超卖）

    TableCell(_table = _table, _row = _row, _column = RSITrendLookback, _value = str.tostring(_gain_since_cross, "#.#") + "%", _txtcolor = _txt_color)
    TableCell(_table = _table, _row = _row, _column = RSITrendLookback+1, _value = _rs_text, _txtcolor = _rs_color)
    TableCell(_table = _table, _row = _row, _column = RSITrendLookback+2, _value = _rs_short_text, _txtcolor = _rs_short_color)
    TableCell(_table = _table, _row = _row, _column = RSITrendLookback+3, _value = str.tostring(_rsi, "#.#"), _txtcolor = _rsi_color)
    TableCell(_table = _table, _row = _row, _column = RSITrendLookback+4, _value = _trend, _txtcolor = _txt_color)
    TableCell(_table = _table, _row = _row, _column = RSITrendLookback+5, _value = _name, _txtcolor = color.white)
    TableCell(_table = _table, _row = _row, _column = RSITrendLookback+6, _value = _symbol, _txtcolor = color.green)

// 修改表头显示函数，反转列顺序
DisplayTableHeader(_table) =>
    // 先显示历史列
    for _i = 0 to RSITrendLookback-2
        _txt = str.tostring(RSITrendLookback-1-_i) + "根前"
        TableCell(_table = _table, _row = 0, _column = _i, _value = _txt, _txtcolor = color.green)
    
    // 然后是其他列
    TableCell(_table = _table, _row = 0, _column = RSITrendLookback-1, _value = "相对强弱\n历史", _txtcolor = color.green)
    TableCell(_table = _table, _row = 0, _column = RSITrendLookback, _value = "已涨/跌", _txtcolor = color.green)
    TableCell(_table = _table, _row = 0, _column = RSITrendLookback+1, _value = str.tostring(RS_Length) + "日强弱", _txtcolor = color.green)
    TableCell(_table = _table, _row = 0, _column = RSITrendLookback+2, _value = str.tostring(RS_Short_Length) + "日强弱", _txtcolor = color.green)
    TableCell(_table = _table, _row = 0, _column = RSITrendLookback+3, _value = "RSI", _txtcolor = color.green)
    TableCell(_table = _table, _row = 0, _column = RSITrendLookback+4, _value = "Trend", _txtcolor = color.green)
    TableCell(_table = _table, _row = 0, _column = RSITrendLookback+5, _value = "名称", _txtcolor = color.green)
    TableCell(_table = _table, _row = 0, _column = RSITrendLookback+6, _value = "Symbol", _txtcolor = color.green)

// 修改 SymbolInfo 函数，合并数据请求
SymbolInfo(string _symbol) =>
    // 使用 [close, time] 合并请求
    [_close, _timestamp] = request.security(symbol = _symbol, expression = [close, time], timeframe = timeframe.period, gaps = barmerge.gaps_off)
    [_benchmark_close, _benchmark_timestamp] = request.security(BenchmarkSymbol, expression = [close, time], timeframe = timeframe.period, gaps = barmerge.gaps_off)
    
    // 只有当时间戳匹配时才进行计算
    _is_synced = _timestamp == _benchmark_timestamp
    
    // 计算指标
    _fast_ema = ta.ema(_close, FastMALength)
    _slow_ema = ta.ema(_close, SlowMALength)
    _is_uptrend = _fast_ema > _slow_ema
    _is_cross_over = ta.crossover(_fast_ema, _slow_ema)
    _is_cross_under = ta.crossunder(_fast_ema, _slow_ema)
    _rsi = ta.rsi(_close, 14)
    var float _cross_point = 0
    _cross_point := (_is_cross_over or _is_cross_under) ? _close : _cross_point
    _gain_since_cross = ((_close - _cross_point) / _cross_point) * 100
    
    // 计算常周期相对强弱
    _symbol_change = (_close - _close[RS_Length]) / _close[RS_Length] * 100
    _benchmark_change = (_benchmark_close - _benchmark_close[RS_Length]) / _benchmark_close[RS_Length] * 100
    _relative_strength = _is_synced ? _symbol_change - _benchmark_change : na
    
    // 计算短周期相对强弱
    _symbol_change_short = (_close - _close[RS_Short_Length]) / _close[RS_Short_Length] * 100
    _benchmark_change_short = (_benchmark_close - _benchmark_close[RS_Short_Length]) / _benchmark_close[RS_Short_Length] * 100
    _relative_strength_short = _is_synced ? _symbol_change_short - _benchmark_change_short : na
    
    // 计算单根相对强弱（用于历史列和判断弱）
    _single_bar_symbol_change = (_close - _close[1]) / _close[1] * 100
    _single_bar_benchmark_change = (_benchmark_close - _benchmark_close[1]) / _benchmark_close[1] * 100
    _single_bar_relative_strength = _is_synced ? _single_bar_symbol_change - _single_bar_benchmark_change : na
    
    // 使用单根对强弱判断强弱
    _is_strong = _is_synced and _single_bar_relative_strength > 0.2 and not na(_single_bar_relative_strength)
    _is_weak = _is_synced and _single_bar_relative_strength < -0.2 and not na(_single_bar_relative_strength)
    
    [_is_uptrend, _rsi, _gain_since_cross, _is_strong, _is_weak, _relative_strength, _relative_strength_short, _single_bar_relative_strength]

InitRSITrend = TypeRelativeStrength.new(false, false, na)

// CustomSymbol1
[custom1_is_uptrend, custom1_rsi, custom1_gains_since_cross, custom1_is_strong, custom1_is_weak, custom1_relative_strength, custom1_relative_strength_short, custom1_single_bar_relative_strength] = SymbolInfo(CustomSymbol1)
var custom1_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = custom1_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(custom1_is_strong, custom1_is_weak, custom1_single_bar_relative_strength))

// CustomSymbol2
[custom2_is_uptrend, custom2_rsi, custom2_gains_since_cross, custom2_is_strong, custom2_is_weak, custom2_relative_strength, custom2_relative_strength_short, custom2_single_bar_relative_strength] = SymbolInfo(CustomSymbol2)
var custom2_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = custom2_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(custom2_is_strong, custom2_is_weak, custom2_single_bar_relative_strength))

// CustomSymbol3
[custom3_is_uptrend, custom3_rsi, custom3_gains_since_cross, custom3_is_strong, custom3_is_weak, custom3_relative_strength, custom3_relative_strength_short, custom3_single_bar_relative_strength] = SymbolInfo(CustomSymbol3)
var custom3_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = custom3_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(custom3_is_strong, custom3_is_weak, custom3_single_bar_relative_strength))

// Symbol1
[sym1_is_uptrend, sym1_rsi, sym1_gains_since_cross, sym1_is_strong, sym1_is_weak, sym1_relative_strength, sym1_relative_strength_short, sym1_single_bar_relative_strength] = SymbolInfo(Symbol1)
var sym1_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = sym1_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(sym1_is_strong, sym1_is_weak, sym1_single_bar_relative_strength))

// Symbol2
[sym2_is_uptrend, sym2_rsi, sym2_gains_since_cross, sym2_is_strong, sym2_is_weak, sym2_relative_strength, sym2_relative_strength_short, sym2_single_bar_relative_strength] = SymbolInfo(Symbol2)
var sym2_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = sym2_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(sym2_is_strong, sym2_is_weak, sym2_single_bar_relative_strength))

// Symbol3
[sym3_is_uptrend, sym3_rsi, sym3_gains_since_cross, sym3_is_strong, sym3_is_weak, sym3_relative_strength, sym3_relative_strength_short, sym3_single_bar_relative_strength] = SymbolInfo(Symbol3)
var sym3_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = sym3_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(sym3_is_strong, sym3_is_weak, sym3_single_bar_relative_strength))

// Symbol4
[sym4_is_uptrend, sym4_rsi, sym4_gains_since_cross, sym4_is_strong, sym4_is_weak, sym4_relative_strength, sym4_relative_strength_short, sym4_single_bar_relative_strength] = SymbolInfo(Symbol4)
var sym4_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = sym4_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(sym4_is_strong, sym4_is_weak, sym4_single_bar_relative_strength))

// Symbol5
[sym5_is_uptrend, sym5_rsi, sym5_gains_since_cross, sym5_is_strong, sym5_is_weak, sym5_relative_strength, sym5_relative_strength_short, sym5_single_bar_relative_strength] = SymbolInfo(Symbol5)
var sym5_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = sym5_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(sym5_is_strong, sym5_is_weak, sym5_single_bar_relative_strength))

// Symbol6
[sym6_is_uptrend, sym6_rsi, sym6_gains_since_cross, sym6_is_strong, sym6_is_weak, sym6_relative_strength, sym6_relative_strength_short, sym6_single_bar_relative_strength] = SymbolInfo(Symbol6)
var sym6_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = sym6_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(sym6_is_strong, sym6_is_weak, sym6_single_bar_relative_strength))

// Symbol7
[sym7_is_uptrend, sym7_rsi, sym7_gains_since_cross, sym7_is_strong, sym7_is_weak, sym7_relative_strength, sym7_relative_strength_short, sym7_single_bar_relative_strength] = SymbolInfo(Symbol7)
var sym7_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = sym7_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(sym7_is_strong, sym7_is_weak, sym7_single_bar_relative_strength))

// Symbol8
[sym8_is_uptrend, sym8_rsi, sym8_gains_since_cross, sym8_is_strong, sym8_is_weak, sym8_relative_strength, sym8_relative_strength_short, sym8_single_bar_relative_strength] = SymbolInfo(Symbol8)
var sym8_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = sym8_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(sym8_is_strong, sym8_is_weak, sym8_single_bar_relative_strength))

// Symbol9
[sym9_is_uptrend, sym9_rsi, sym9_gains_since_cross, sym9_is_strong, sym9_is_weak, sym9_relative_strength, sym9_relative_strength_short, sym9_single_bar_relative_strength] = SymbolInfo(Symbol9)
var sym9_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = sym9_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(sym9_is_strong, sym9_is_weak, sym9_single_bar_relative_strength))

// Symbol10
[sym10_is_uptrend, sym10_rsi, sym10_gains_since_cross, sym10_is_strong, sym10_is_weak, sym10_relative_strength, sym10_relative_strength_short, sym10_single_bar_relative_strength] = SymbolInfo(Symbol10)
var sym10_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = sym10_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(sym10_is_strong, sym10_is_weak, sym10_single_bar_relative_strength))

// Symbol11
[sym11_is_uptrend, sym11_rsi, sym11_gains_since_cross, sym11_is_strong, sym11_is_weak, sym11_relative_strength, sym11_relative_strength_short, sym11_single_bar_relative_strength] = SymbolInfo(Symbol11)
var sym11_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = sym11_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(sym11_is_strong, sym11_is_weak, sym11_single_bar_relative_strength))

// Symbol12
[sym12_is_uptrend, sym12_rsi, sym12_gains_since_cross, sym12_is_strong, sym12_is_weak, sym12_relative_strength, sym12_relative_strength_short, sym12_single_bar_relative_strength] = SymbolInfo(Symbol12)
var sym12_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = sym12_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(sym12_is_strong, sym12_is_weak, sym12_single_bar_relative_strength))

// Symbol13
[sym13_is_uptrend, sym13_rsi, sym13_gains_since_cross, sym13_is_strong, sym13_is_weak, sym13_relative_strength, sym13_relative_strength_short, sym13_single_bar_relative_strength] = SymbolInfo(Symbol13)
var sym13_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = sym13_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(sym13_is_strong, sym13_is_weak, sym13_single_bar_relative_strength))

// Symbol14
[sym14_is_uptrend, sym14_rsi, sym14_gains_since_cross, sym14_is_strong, sym14_is_weak, sym14_relative_strength, sym14_relative_strength_short, sym14_single_bar_relative_strength] = SymbolInfo(Symbol14)
var sym14_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = sym14_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(sym14_is_strong, sym14_is_weak, sym14_single_bar_relative_strength))

// Symbol15
[sym15_is_uptrend, sym15_rsi, sym15_gains_since_cross, sym15_is_strong, sym15_is_weak, sym15_relative_strength, sym15_relative_strength_short, sym15_single_bar_relative_strength] = SymbolInfo(Symbol15)
var sym15_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = sym15_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(sym15_is_strong, sym15_is_weak, sym15_single_bar_relative_strength))

// Symbol16
[sym16_is_uptrend, sym16_rsi, sym16_gains_since_cross, sym16_is_strong, sym16_is_weak, sym16_relative_strength, sym16_relative_strength_short, sym16_single_bar_relative_strength] = SymbolInfo(Symbol16)
var sym16_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = sym16_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(sym16_is_strong, sym16_is_weak, sym16_single_bar_relative_strength))

// Symbol17
[sym17_is_uptrend, sym17_rsi, sym17_gains_since_cross, sym17_is_strong, sym17_is_weak, sym17_relative_strength, sym17_relative_strength_short, sym17_single_bar_relative_strength] = SymbolInfo(Symbol17)
var sym17_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = sym17_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(sym17_is_strong, sym17_is_weak, sym17_single_bar_relative_strength))

// Symbol18
[sym18_is_uptrend, sym18_rsi, sym18_gains_since_cross, sym18_is_strong, sym18_is_weak, sym18_relative_strength, sym18_relative_strength_short, sym18_single_bar_relative_strength] = SymbolInfo(Symbol18)
var sym18_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = sym18_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(sym18_is_strong, sym18_is_weak, sym18_single_bar_relative_strength))

// Symbol19
[sym19_is_uptrend, sym19_rsi, sym19_gains_since_cross, sym19_is_strong, sym19_is_weak, sym19_relative_strength, sym19_relative_strength_short, sym19_single_bar_relative_strength] = SymbolInfo(Symbol19)
var sym19_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = sym19_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(sym19_is_strong, sym19_is_weak, sym19_single_bar_relative_strength))

// Symbol20
[sym20_is_uptrend, sym20_rsi, sym20_gains_since_cross, sym20_is_strong, sym20_is_weak, sym20_relative_strength, sym20_relative_strength_short, sym20_single_bar_relative_strength] = SymbolInfo(Symbol20)
var sym20_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = sym20_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(sym20_is_strong, sym20_is_weak, sym20_single_bar_relative_strength))

// 添加基准标的的计算
[benchmark_is_uptrend, benchmark_rsi, benchmark_gains_since_cross, benchmark_is_strong, benchmark_is_weak, benchmark_relative_strength, benchmark_relative_strength_short, benchmark_single_bar_relative_strength] = SymbolInfo(BenchmarkSymbol)
var benchmark_relative_strength_info = array.new<TypeRelativeStrength>(RSITrendLookback, InitRSITrend)
queuePush(_queueArray = benchmark_relative_strength_info, _queueSize = RSITrendLookback, _value = TypeRelativeStrength.new(benchmark_is_strong, benchmark_is_weak, benchmark_single_bar_relative_strength))

// 修改表格创建，增加列数以适应更多的历史bar
var InfoTable = table.new("middle" + "_" + "left", columns = RSITrendLookback+8, rows = 25, frame_color=TableFrameColor, frame_width=1, border_color=TableBorderColor, border_width=1)



// 显示所有标的
DisplayTableHeader(InfoTable)
DisplaySymbolInfo(_table = InfoTable, _row = 1, _symbol = BenchmarkSymbol, _name = BenchmarkName, _is_uptrend = benchmark_is_uptrend, _rsi = benchmark_rsi, _gain_since_cross = benchmark_gains_since_cross, _relative_strength = 0.0, _relative_strength_short = 0.0, _single_bar_relative_strength = 0.0, _relative_strength_info = benchmark_relative_strength_info)

// 添加自定义标的显示
DisplaySymbolInfo(_table = InfoTable, _row = 2, _symbol = CustomSymbol1, _name = CustomSymbol1Name, _is_uptrend = custom1_is_uptrend, _rsi = custom1_rsi, _gain_since_cross = custom1_gains_since_cross, _relative_strength = custom1_relative_strength, _relative_strength_short = custom1_relative_strength_short, _single_bar_relative_strength = custom1_single_bar_relative_strength, _relative_strength_info = custom1_relative_strength_info)

DisplaySymbolInfo(_table = InfoTable, _row = 3, _symbol = CustomSymbol2, _name = CustomSymbol2Name, _is_uptrend = custom2_is_uptrend, _rsi = custom2_rsi, _gain_since_cross = custom2_gains_since_cross, _relative_strength = custom2_relative_strength, _relative_strength_short = custom2_relative_strength_short, _single_bar_relative_strength = custom2_single_bar_relative_strength, _relative_strength_info = custom2_relative_strength_info)

DisplaySymbolInfo(_table = InfoTable, _row = 4, _symbol = CustomSymbol3, _name = CustomSymbol3Name, _is_uptrend = custom3_is_uptrend, _rsi = custom3_rsi, _gain_since_cross = custom3_gains_since_cross, _relative_strength = custom3_relative_strength, _relative_strength_short = custom3_relative_strength_short, _single_bar_relative_strength = custom3_single_bar_relative_strength, _relative_strength_info = custom3_relative_strength_info)

DisplaySymbolInfo(_table = InfoTable, _row = 5, _symbol = Symbol1, _name = Symbol1Name, _is_uptrend = sym1_is_uptrend, _rsi = sym1_rsi, _gain_since_cross = sym1_gains_since_cross, _relative_strength = sym1_relative_strength, _relative_strength_short = sym1_relative_strength_short, _single_bar_relative_strength = sym1_single_bar_relative_strength, _relative_strength_info = sym1_relative_strength_info)

DisplaySymbolInfo(_table = InfoTable, _row = 6, _symbol = Symbol2, _name = Symbol2Name, _is_uptrend = sym2_is_uptrend, _rsi = sym2_rsi, _gain_since_cross = sym2_gains_since_cross, _relative_strength = sym2_relative_strength, _relative_strength_short = sym2_relative_strength_short, _single_bar_relative_strength = sym2_single_bar_relative_strength, _relative_strength_info = sym2_relative_strength_info)

DisplaySymbolInfo(_table = InfoTable, _row = 7, _symbol = Symbol3, _name = Symbol3Name, _is_uptrend = sym3_is_uptrend, _rsi = sym3_rsi, _gain_since_cross = sym3_gains_since_cross, _relative_strength = sym3_relative_strength, _relative_strength_short = sym3_relative_strength_short, _single_bar_relative_strength = sym3_single_bar_relative_strength, _relative_strength_info = sym3_relative_strength_info)

DisplaySymbolInfo(_table = InfoTable, _row = 8, _symbol = Symbol4, _name = Symbol4Name, _is_uptrend = sym4_is_uptrend, _rsi = sym4_rsi, _gain_since_cross = sym4_gains_since_cross, _relative_strength = sym4_relative_strength, _relative_strength_short = sym4_relative_strength_short, _single_bar_relative_strength = sym4_single_bar_relative_strength, _relative_strength_info = sym4_relative_strength_info)

DisplaySymbolInfo(_table = InfoTable, _row = 9, _symbol = Symbol5, _name = Symbol5Name, _is_uptrend = sym5_is_uptrend, _rsi = sym5_rsi, _gain_since_cross = sym5_gains_since_cross, _relative_strength = sym5_relative_strength, _relative_strength_short = sym5_relative_strength_short, _single_bar_relative_strength = sym5_single_bar_relative_strength, _relative_strength_info = sym5_relative_strength_info)

DisplaySymbolInfo(_table = InfoTable, _row = 10, _symbol = Symbol6, _name = Symbol6Name, _is_uptrend = sym6_is_uptrend, _rsi = sym6_rsi, _gain_since_cross = sym6_gains_since_cross, _relative_strength = sym6_relative_strength, _relative_strength_short = sym6_relative_strength_short, _single_bar_relative_strength = sym6_single_bar_relative_strength, _relative_strength_info = sym6_relative_strength_info)

DisplaySymbolInfo(_table = InfoTable, _row = 11, _symbol = Symbol7, _name = Symbol7Name, _is_uptrend = sym7_is_uptrend, _rsi = sym7_rsi, _gain_since_cross = sym7_gains_since_cross, _relative_strength = sym7_relative_strength, _relative_strength_short = sym7_relative_strength_short, _single_bar_relative_strength = sym7_single_bar_relative_strength, _relative_strength_info = sym7_relative_strength_info)

DisplaySymbolInfo(_table = InfoTable, _row = 12, _symbol = Symbol8, _name = Symbol8Name, _is_uptrend = sym8_is_uptrend, _rsi = sym8_rsi, _gain_since_cross = sym8_gains_since_cross, _relative_strength = sym8_relative_strength, _relative_strength_short = sym8_relative_strength_short, _single_bar_relative_strength = sym8_single_bar_relative_strength, _relative_strength_info = sym8_relative_strength_info)

DisplaySymbolInfo(_table = InfoTable, _row = 13, _symbol = Symbol9, _name = Symbol9Name, _is_uptrend = sym9_is_uptrend, _rsi = sym9_rsi, _gain_since_cross = sym9_gains_since_cross, _relative_strength = sym9_relative_strength, _relative_strength_short = sym9_relative_strength_short, _single_bar_relative_strength = sym9_single_bar_relative_strength, _relative_strength_info = sym9_relative_strength_info)

DisplaySymbolInfo(_table = InfoTable, _row = 14, _symbol = Symbol10, _name = Symbol10Name, _is_uptrend = sym10_is_uptrend, _rsi = sym10_rsi, _gain_since_cross = sym10_gains_since_cross, _relative_strength = sym10_relative_strength, _relative_strength_short = sym10_relative_strength_short, _single_bar_relative_strength = sym10_single_bar_relative_strength, _relative_strength_info = sym10_relative_strength_info)

DisplaySymbolInfo(_table = InfoTable, _row = 15, _symbol = Symbol11, _name = Symbol11Name, _is_uptrend = sym11_is_uptrend, _rsi = sym11_rsi, _gain_since_cross = sym11_gains_since_cross, _relative_strength = sym11_relative_strength, _relative_strength_short = sym11_relative_strength_short, _single_bar_relative_strength = sym11_single_bar_relative_strength, _relative_strength_info = sym11_relative_strength_info)

DisplaySymbolInfo(_table = InfoTable, _row = 16, _symbol = Symbol12, _name = Symbol12Name, _is_uptrend = sym12_is_uptrend, _rsi = sym12_rsi, _gain_since_cross = sym12_gains_since_cross, _relative_strength = sym12_relative_strength, _relative_strength_short = sym12_relative_strength_short, _single_bar_relative_strength = sym12_single_bar_relative_strength, _relative_strength_info = sym12_relative_strength_info)

DisplaySymbolInfo(_table = InfoTable, _row = 17, _symbol = Symbol13, _name = Symbol13Name, _is_uptrend = sym13_is_uptrend, _rsi = sym13_rsi, _gain_since_cross = sym13_gains_since_cross, _relative_strength = sym13_relative_strength, _relative_strength_short = sym13_relative_strength_short, _single_bar_relative_strength = sym13_single_bar_relative_strength, _relative_strength_info = sym13_relative_strength_info)

DisplaySymbolInfo(_table = InfoTable, _row = 18, _symbol = Symbol14, _name = Symbol14Name, _is_uptrend = sym14_is_uptrend, _rsi = sym14_rsi, _gain_since_cross = sym14_gains_since_cross, _relative_strength = sym14_relative_strength, _relative_strength_short = sym14_relative_strength_short, _single_bar_relative_strength = sym14_single_bar_relative_strength, _relative_strength_info = sym14_relative_strength_info)

DisplaySymbolInfo(_table = InfoTable, _row = 19, _symbol = Symbol15, _name = Symbol15Name, _is_uptrend = sym15_is_uptrend, _rsi = sym15_rsi, _gain_since_cross = sym15_gains_since_cross, _relative_strength = sym15_relative_strength, _relative_strength_short = sym15_relative_strength_short, _single_bar_relative_strength = sym15_single_bar_relative_strength, _relative_strength_info = sym15_relative_strength_info)

DisplaySymbolInfo(_table = InfoTable, _row = 20, _symbol = Symbol16, _name = Symbol16Name, _is_uptrend = sym16_is_uptrend, _rsi = sym16_rsi, _gain_since_cross = sym16_gains_since_cross, _relative_strength = sym16_relative_strength, _relative_strength_short = sym16_relative_strength_short, _single_bar_relative_strength = sym16_single_bar_relative_strength, _relative_strength_info = sym16_relative_strength_info)

DisplaySymbolInfo(_table = InfoTable, _row = 21, _symbol = Symbol17, _name = Symbol17Name, _is_uptrend = sym17_is_uptrend, _rsi = sym17_rsi, _gain_since_cross = sym17_gains_since_cross, _relative_strength = sym17_relative_strength, _relative_strength_short = sym17_relative_strength_short, _single_bar_relative_strength = sym17_single_bar_relative_strength, _relative_strength_info = sym17_relative_strength_info)

DisplaySymbolInfo(_table = InfoTable, _row = 22, _symbol = Symbol18, _name = Symbol18Name, _is_uptrend = sym18_is_uptrend, _rsi = sym18_rsi, _gain_since_cross = sym18_gains_since_cross, _relative_strength = sym18_relative_strength, _relative_strength_short = sym18_relative_strength_short, _single_bar_relative_strength = sym18_single_bar_relative_strength, _relative_strength_info = sym18_relative_strength_info)

DisplaySymbolInfo(_table = InfoTable, _row = 23, _symbol = Symbol19, _name = Symbol19Name, _is_uptrend = sym19_is_uptrend, _rsi = sym19_rsi, _gain_since_cross = sym19_gains_since_cross, _relative_strength = sym19_relative_strength, _relative_strength_short = sym19_relative_strength_short, _single_bar_relative_strength = sym19_single_bar_relative_strength, _relative_strength_info = sym19_relative_strength_info)

DisplaySymbolInfo(_table = InfoTable, _row = 24, _symbol = Symbol20, _name = Symbol20Name, _is_uptrend = sym20_is_uptrend, _rsi = sym20_rsi, _gain_since_cross = sym20_gains_since_cross, _relative_strength = sym20_relative_strength, _relative_strength_short = sym20_relative_strength_short, _single_bar_relative_strength = sym20_single_bar_relative_strength, _relative_strength_info = sym20_relative_strength_info)
