//@version=5
indicator("MCSI MMT Test", overlay=false)

// === MMT参数设置 ===
var leveling = 10
var cyclicMemory = 34
var channelWeight = 0.5  // 轨道分数权重，改为50%
var divergenceWeight = 0.5  // 背离分数权重，改为50%

// === MMT计算函数 ===
Cycle1(i, waveThrottle, cycs) =>
    ret = 6.0 * waveThrottle + 1.0
    if (i == 0)
        ret := 1.0 + waveThrottle
    else if (i == 1)
        ret := 1.0 + waveThrottle * 5.0
    else if (i == (cycs-1))
        ret := 1.0 + waveThrottle
    else if (i == (cycs-2))
        ret := 1.0 + waveThrottle * 5.0
    ret

Cycle2(i, waveThrottle, cycs) =>
    ret = -4.0 * waveThrottle
    if (i == 0)
        ret := -2.0 * waveThrottle
    else if (i == (cycs-1))
        ret := 0.0
    else if (i == (cycs-2))
        ret := -2.0 * waveThrottle
    ret

Cycle3(i, waveThrottle, cycs) =>
    ret = waveThrottle
    if (i == (cycs-1))
        ret := 0.0
    else if (i == (cycs-2))
        ret := 0.0
    ret

iWTT_CSI_processor(CycleCount) =>
    wtt1=0.0, wtt2=0.0, wtt3=0.0, wtt4=0.0, wtt5=0.0
    _wtt1=0.0, _wtt2=0.0, _wtt3=0.0, _wtt4=0.0, _wtt5=0.0
    momentum=0.0, acceleration=0.0, swing=0.0
    cycs=50
    waveThrottle=float(160*CycleCount)
    currentVal=0.0  
    
    for i = 0 to cycs - 1
        swing:=Cycle1(i,waveThrottle,cycs)-wtt4*wtt1-_wtt5*_wtt2
        if(swing==0) 
            break
        momentum := Cycle2(i,waveThrottle,cycs)
        _wtt1 := wtt1
        wtt1 := (momentum-wtt4*wtt2)/swing

        acceleration:=Cycle3(i,waveThrottle,cycs)
        _wtt2 := wtt2
        wtt2 := acceleration/swing
 
        currentVal:=(close[49-i]-_wtt3*_wtt5-wtt3*wtt4)/swing
        _wtt3  := wtt3
        wtt3   := currentVal
        wtt4   := momentum-wtt5*_wtt1
        _wtt5  := wtt5
        wtt5   := acceleration

    currentVal

banding(CRSI, Period, Leveling) =>
    percent = Leveling / 100.0
    periodMinusOne = Period-1
    maxima = -999999.0
    minima =  999999.0
    for i=0 to periodMinusOne
        crsi = nz(CRSI[i])
        if crsi > maxima
            maxima := crsi
        else if crsi < minima
            minima := crsi
    stepFactor = (maxima - minima) / 100.0
    float lowBand = na
    for steps=0 to 100
        testValue = minima + stepFactor * steps
        below = 0
        for m=0 to periodMinusOne
            if CRSI[m] < testValue
                below := below + 1
        if below/Period >= percent
            lowBand := testValue
            break
    float highBand = na
    for steps=0 to 100
        testValue = maxima - stepFactor * steps
        above = 0
        for m=0 to periodMinusOne
            if CRSI[m] >= testValue
                above := above + 1
        if above/Period >= percent
            highBand := testValue
            break
    [highBand, lowBand]

// === MMT计算 ===
thrust1=iWTT_CSI_processor(1)
thrust2=iWTT_CSI_processor(10)
CSIBuffer = thrust1-thrust2

[highBand, lowBand] = banding(CSIBuffer, cyclicMemory, leveling)

// === 计算动量和标准差 ===
momentum = CSIBuffer - CSIBuffer[1]  // 动量定义为CSI的变化
stdDev = ta.stdev(CSIBuffer, cyclicMemory)  // 使用相同的周期计算标准差

// === 背离检测参数 ===
lbR = input.int(title="Pivot Lookback Right", defval=2)  // 改为2天确认
lbL = input.int(title="Pivot Lookback Left", defval=5)   // 保持不变
rangeUpper = input.int(title="Max of Lookback Range", defval=60)
rangeLower = input.int(title="Min of Lookback Range", defval=5)

// === 背离检测函数 ===
_inRange(cond) =>
    bars = ta.barssince(cond == true)
    rangeLower <= bars and bars <= rangeUpper

// 寻找枢轴点
plFound = na(ta.pivotlow(CSIBuffer, lbL, lbR)) ? false : true
phFound = na(ta.pivothigh(CSIBuffer, lbL, lbR)) ? false : true

// 常规看涨背离
oscHL = CSIBuffer[lbR] > ta.valuewhen(plFound, CSIBuffer[lbR], 1) and _inRange(plFound[1])
priceLL = low[lbR] < ta.valuewhen(plFound, low[lbR], 1)
bullDiv = priceLL and oscHL and plFound

// 常规看跌背离
oscLH = CSIBuffer[lbR] < ta.valuewhen(phFound, CSIBuffer[lbR], 1) and _inRange(phFound[1])
priceHH = high[lbR] > ta.valuewhen(phFound, high[lbR], 1)
bearDiv = priceHH and oscLH and phFound

// 隐藏看涨背离
oscLL = CSIBuffer[lbR] < ta.valuewhen(plFound, CSIBuffer[lbR], 1) and _inRange(plFound[1])
priceHL = low[lbR] > ta.valuewhen(plFound, low[lbR], 1)
hiddenBullDiv = priceHL and oscLL and plFound

// 隐藏看跌背离
oscHH = CSIBuffer[lbR] > ta.valuewhen(phFound, CSIBuffer[lbR], 1) and _inRange(phFound[1])
priceLH = high[lbR] < ta.valuewhen(phFound, high[lbR], 1)
hiddenBearDiv = priceLH and oscHH and phFound

// === 修改评分计算 ===
var float mmtScore = 0.0
var float channelScore = 0.0
var float divergenceScore = 0.0
var int channelSignalDuration = 0  // 轨道信号持续时间
var int divergenceSignalDuration = 0  // 背离信号持续时间
var float lastChannelScore = 0.0  // 记录上一次轨道分数
var float lastDivergenceScore = 0.0  // 记录上一次背离分数

// 轨道位置分数计算（80%权重）
if CSIBuffer[1] > highBand[1] and CSIBuffer < highBand
    channelScore := -100
    lastChannelScore := -100
    channelSignalDuration := 2
else if CSIBuffer[1] < lowBand[1] and CSIBuffer > lowBand
    channelScore := 100
    lastChannelScore := 100
    channelSignalDuration := 2
else if CSIBuffer > highBand
    channelScore := momentum < 0 ? -80 : -20
    lastChannelScore := channelScore
    channelSignalDuration := 2
else if CSIBuffer < lowBand
    channelScore := momentum > 0 ? 80 : 20
    lastChannelScore := channelScore
    channelSignalDuration := 2
else
    if channelSignalDuration > 0
        channelSignalDuration := channelSignalDuration - 1
        channelScore := lastChannelScore
    else
        channelScore := 0

// 背离分数计算（50%权重）
if bearDiv
    divergenceScore := -100
    lastDivergenceScore := -100
    divergenceSignalDuration := 1  // 改为1个周期
else if bullDiv
    divergenceScore := 100
    lastDivergenceScore := 100
    divergenceSignalDuration := 1  // 改为1个周期
else if hiddenBearDiv
    divergenceScore := -70
    lastDivergenceScore := -70
    divergenceSignalDuration := 1  // 改为1个周期
else if hiddenBullDiv
    divergenceScore := 70
    lastDivergenceScore := 70
    divergenceSignalDuration := 1  // 改为1个周期
else
    if divergenceSignalDuration > 0
        divergenceSignalDuration := divergenceSignalDuration - 1
        divergenceScore := lastDivergenceScore
    else
        divergenceScore := 0

// 最终分数计算
mmtScore := channelScore * channelWeight + divergenceScore * divergenceWeight

// === 绘制 ===
highband = plot(highBand, "HighBand", color=color.aqua, editable=false)
lowband = plot(lowBand, "LowBand", color=color.aqua, editable=false)
fill(highband, lowband, color=color.new(#CCCC77, 90))

plot(0, color=color.new(color.black, 25), linewidth=1)
plot(CSIBuffer, color=CSIBuffer > 0 ? color.green : color.red, linewidth=1)

// 分别绘制轨道分数和背离分数
plot(channelScore, "轨道分数", color=color.blue, linewidth=1)
plot(divergenceScore, "背离分数", color=color.yellow, linewidth=1)
plot(mmtScore, "MMT总分", color=color.white, linewidth=2)

// 显示调试信息
if barstate.islast
    var table infoTable = table.new(position.top_right, 1, 1, bgcolor=color.new(color.black, 80))
    table.cell(infoTable, 0, 0, 
               "轨道分数: " + str.tostring(channelScore, "#.##") + "\n" +
               "轨道信号持续: " + str.tostring(channelSignalDuration, "#") + "\n" +
               "背离分数: " + str.tostring(divergenceScore, "#.##") + "\n" +
               "背离信号持续: " + str.tostring(divergenceSignalDuration, "#") + "\n" +
               "MMT总分: " + str.tostring(mmtScore, "#.##"),
               text_color=color.white, text_size=size.small)

// 绘制背离标记
plotshape(bullDiv ? CSIBuffer[lbR] : na, title="看涨背离", style=shape.circle, location=location.absolute, color=color.green, size=size.tiny, offset=-lbR)
plotshape(bearDiv ? CSIBuffer[lbR] : na, title="看跌背离", style=shape.circle, location=location.absolute, color=color.red, size=size.tiny, offset=-lbR)
plotshape(hiddenBullDiv ? CSIBuffer[lbR] : na, title="隐藏看涨背离", style=shape.circle, location=location.absolute, color=color.new(color.green, 50), size=size.tiny, offset=-lbR)
plotshape(hiddenBearDiv ? CSIBuffer[lbR] : na, title="隐藏看跌背离", style=shape.circle, location=location.absolute, color=color.new(color.red, 50), size=size.tiny, offset=-lbR)
