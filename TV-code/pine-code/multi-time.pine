// 本源代码受Mozilla公共许可证2.0条款的约束，网址为https://mozilla.org/MPL/2.0/
// Ox_kali
// ________________________________________________________________________________________________________________________________       
                                  
//  ██████╗ ██╗  ██╗        ██╗  ██╗ █████╗ ██╗     ██╗     █████╗ ██╗      ██████╗  ██████╗ ██████╗ ██╗████████╗██╗  ██╗███╗   ███╗
// ██╔═══██╗╚██╗██╔╝        ██║ ██╔╝██╔══██╗██║     ██║    ██╔══██╗██║     ██╔════╝ ██╔═══██╗██╔══██╗██║╚══██╔══╝██║  ██║████╗ ████║
// ██║   ██║ ╚███╔╝         █████╔╝ ███████║██║     ██║    ███████║██║     ██║  ███╗██║   ██║██████╔╝██║   ██║   ███████║██╔████╔██║
// ██║   ██║ ██╔██╗         ██╔═██╗ ██╔══██║██║     ██║    ██╔══██║██║     ██║   ██║██║   ██║██╔══██╗██║   ██║   ██╔══██║██║╚██╔╝██║
// ╚██████╔╝██╔╝ ██╗███████╗██║  ██╗██║  ██║███████╗██║    ██║  ██║███████╗╚██████╔╝╚██████╔╝██║  ██║██║   ██║   ██║  ██║██║ ╚═╝ ██║
//  ╚═════╝ ╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝╚═╝  ╚═╝╚══════╝╚═╝    ╚═╝  ╚═╝╚══════╝ ╚═════╝  ╚═════╝ ╚═╝  ╚═╝╚═╝   ╚═╝   ╚═╝  ╚═╝╚═╝     ╚═╝
// ________________________________________________________________________________________________________________________________       

//@version=5
// 指标标题和简称
// ________________________________________________________________________________________________________________________________                      
indicator("多时间周期趋势指标", shorttitle="MTFTI", overlay=true)

// 添加移动平均线类型选项
// ________________________________________________________________________________________________________________________________     
maType = input.string(title="移动平均线类型", defval="EMA", options=["SMA", "EMA", "WMA", "AMA"])

// 表格位置输入
// ________________________________________________________________________________________________________________________________                      
tablePosition = input.string(title="表格位置", defval="middle_right", options=["top_left", "top_right", "middle_left", "middle_right", "bottom_left", "bottom_right"])

// 选择显示的时间周期
// ________________________________________________________________________________________________________________________________    
showTf1 = input(false, title="显示1分钟")
showTf2 = input(false, title="显示2分钟")
showTf3 = input(false, title="显示3分钟")
showTf4 = input(true, title="显示5分钟")
showTf5 = input(false, title="显示10分钟")
showTf6 = input(true, title="显示15分钟")
showTf7 = input(true, title="显示30分钟")
showTf8 = input(true, title="显示1小时")
showTf9 = input(true, title="显示2小时")
showTf10 = input(true, title="显示4小时")
showTf11 = input(true, title="显示6小时")
showTf12 = input(true, title="显示12小时")
showTf13 = input(true, title="显示1天")
showTf14 = input(true, title="显示1周")
showTf15 = input(true, title="显示平均值")

// 输入时间周期
// ________________________________________________________________________________________________________________________________                      
tf1 = "1"
tf2= "2"
tf3 = "3"
tf4 = "5"
tf5 = "10"
tf6 = "15"
tf7 = "30"
tf8 = "60"
tf9 = "120"
tf10 = "240"
tf11 = "360"
tf12 = "720"
tf13 = "1440"
tf14 = "1W"

// EMA输入参数
// ________________________________________________________________________________________________________________________________                      
shortTerm = input(50, title="短期EMA长度")
longTerm = input(200, title="长期EMA长度")
showShortTerm = input(true, title="显示短期EMA")
showLongTerm = input(true, title="显示长期EMA")

// 函数：根据用户输入转换为位置类型
// ________________________________________________________________________________________________________________________________                      
get_position(value) =>
    pos = position.middle_right
    if value == "top_left"
        pos := position.top_left
    else if value == "top_right"
        pos := position.top_right
    else if value == "middle_left"
        pos := position.middle_left
    else if value == "middle_right"
        pos := position.middle_right
    else if value == "bottom_left"
        pos := position.bottom_left
    else if value == "bottom_right"
        pos := position.bottom_right
    pos

// 函数：根据用户选择计算移动平均线
// ________________________________________________________________________________________________________________________________     
calcMa(price, length) =>
    float ma = na
    if maType == "SMA"
        ma := ta.sma(price, length)
    else if maType == "EMA"
        ma := ta.ema(price, length)
    else if maType == "WMA"
        ma := ta.wma(price, length)
    else if maType == "AMA"
        ma := ta.hma(price, length)
    ma

// 函数：计算趋势
// ________________________________________________________________________________________________________________________________     
trendCalc(timeframe) =>
    float maShort = request.security(syminfo.tickerid, timeframe, calcMa(close, shortTerm))
    float maLong = request.security(syminfo.tickerid, timeframe, calcMa(close, longTerm))
    trend = maShort > maLong ? 1 : -1
    trend

// 初始化变量
// ________________________________________________________________________________________________________________________________                      
float trend1 = na
float trend2 = na
float trend3 = na
float trend4 = na
float trend5 = na
float trend6 = na
float trend7 = na
float trend8 = na
float trend9 = na
float trend10 = na
float trend11 = na
float trend12 = na
float trend13 = na
float trend14 = na

// 初始化趋势和
// ________________________________________________________________________________________________________________________________                      
trendSum = 0.0
activeTfs = 0
if showTf1
    trend1 := trendCalc(tf1)
    trendSum := trendSum + trend1
    activeTfs := activeTfs + 1

if showTf2
    trend2 := trendCalc(tf2)
    trendSum := trendSum + trend2
    activeTfs := activeTfs + 1

if showTf3
    trend3 := trendCalc(tf3)
    trendSum := trendSum + trend3
    activeTfs := activeTfs + 1

if showTf4
    trend4 := trendCalc(tf4)
    trendSum := trendSum + trend4
    activeTfs := activeTfs + 1

if showTf5
    trend5 := trendCalc(tf5)
    trendSum := trendSum + trend5
    activeTfs := activeTfs + 1

if showTf6
    trend6 := trendCalc(tf6)
    trendSum := trendSum + trend6
    activeTfs := activeTfs + 1

if showTf7
    trend7 := trendCalc(tf7)
    trendSum := trendSum + trend7
    activeTfs := activeTfs + 1

if showTf8
    trend8 := trendCalc(tf8)
    trendSum := trendSum + trend8
    activeTfs := activeTfs + 1

if showTf9
    trend9 := trendCalc(tf9)
    trendSum := trendSum + trend9
    activeTfs := activeTfs + 1

if showTf10
    trend10 := trendCalc(tf10)
    trendSum := trendSum + trend10
    activeTfs := activeTfs + 1

if showTf11
    trend11 := trendCalc(tf11)
    trendSum := trendSum + trend11
    activeTfs := activeTfs + 1

if showTf12
    trend12 := trendCalc(tf12)
    trendSum := trendSum + trend12
    activeTfs := activeTfs + 1

if showTf13
    trend13 := trendCalc(tf13)
    trendSum := trendSum + trend13
    activeTfs := activeTfs + 1

if showTf14
    trend14 := trendCalc(tf14)
    trendSum := trendSum + trend14
    activeTfs := activeTfs + 1

// 计算平均趋势
// ________________________________________________________________________________________________________________________________ 
trendAvg = activeTfs != 0 ? trendSum / activeTfs : na

// 颜色设置
// ________________________________________________________________________________________________________________________________                      
colorStrongDown = input.color(color.rgb(172, 1, 1), title='强势下跌颜色', group='颜色')
colorDown = input.color(color.red, title='下跌颜色', group='颜色')
colorStrongUp = input.color(color.rgb(1, 128, 5), title='强势上涨颜色', group='颜色')
colorUp = input.color(color.green, title='上涨颜色', group='颜色')      
colorNeutral = input.color(color.rgb(169, 169, 169), title='中性颜色', group='颜色')  

// 设置平均趋势文本和背景颜色
// ________________________________________________________________________________________________________________________________                      
trendAvgText = ""
trendAvgBgcolor = colorNeutral  
if trendAvg <= -0.8
    trendAvgText := "暴跌"
    trendAvgBgcolor := colorStrongDown
else if trendAvg <= -0.2
    trendAvgText := "跌"
    trendAvgBgcolor := colorDown
else if trendAvg >= 0.8
    trendAvgText := "暴涨"
    trendAvgBgcolor := colorStrongUp
else if trendAvg >= 0.2
    trendAvgText := "涨"
    trendAvgBgcolor := colorUp
else
    trendAvgText := "中性"
    trendAvgBgcolor := colorNeutral  

// 创建表格
// ________________________________________________________________________________________________________________________________                      
trendTable = table.new(position=get_position(tablePosition), columns=2, rows=16, frame_color=color.white, bgcolor=color.black)

// 添加表格头部
// ________________________________________________________________________________________________________________________________                      
table.cell(trendTable, column=0, row=0, text="趋势", bgcolor=color.black, text_color=color.white)
table.cell(trendTable, column=1, row=0, text="周期", bgcolor=color.black, text_color=color.white)

// 填充表格单元格
// ________________________________________________________________________________________________________________________________                      
if showTf1
    table.cell(trendTable, column=1, row=1, text="1分", bgcolor=color.rgb(0, 0, 0, 70), text_color=color.white)
    table.cell(trendTable, column=0, row=1, text=(trend1 > 0 ? "涨" : "跌"), bgcolor=trend1 > 0 ? colorUp : colorDown)

if showTf2
    table.cell(trendTable, column=1, row=2, text="2分", bgcolor=color.rgb(0, 0, 0, 70), text_color=color.white)
    table.cell(trendTable, column=0, row=2, text=(trend2 > 0 ? "涨" : "跌"), bgcolor=trend2 > 0 ? colorUp : colorDown)

if showTf3
    table.cell(trendTable, column=1, row=3, text="3分", bgcolor=color.rgb(0, 0, 0, 70), text_color=color.white)
    table.cell(trendTable, column=0, row=3, text=(trend3 > 0 ? "涨" : "跌"), bgcolor=trend3 > 0 ? colorUp : colorDown)

if showTf4
    table.cell(trendTable, column=1, row=4, text="5分", bgcolor=color.rgb(0, 0, 0, 70), text_color=color.white)
    table.cell(trendTable, column=0, row=4, text=(trend4 > 0 ? "涨" : "跌"), bgcolor=trend4 > 0 ? colorUp : colorDown)

if showTf5
    table.cell(trendTable, column=1, row=5, text="10分", bgcolor=color.rgb(0, 0, 0, 70), text_color=color.white)
    table.cell(trendTable, column=0, row=5, text=(trend5 > 0 ? "涨" : "跌"), bgcolor=trend5 > 0 ? colorUp : colorDown)

if showTf6
    table.cell(trendTable, column=1, row=6, text="15分", bgcolor=color.rgb(0, 0, 0, 70), text_color=color.white)
    table.cell(trendTable, column=0, row=6, text=(trend6 > 0 ? "涨" : "跌"), bgcolor=trend6 > 0 ? colorUp : colorDown)

if showTf7
    table.cell(trendTable, column=1, row=7, text="30分", bgcolor=color.rgb(0, 0, 0, 70), text_color=color.white)
    table.cell(trendTable, column=0, row=7, text=(trend7 > 0 ? "涨" : "跌"), bgcolor=trend7 > 0 ? colorUp : colorDown)

if showTf8
    table.cell(trendTable, column=1, row=8, text="1时", bgcolor=color.rgb(0, 0, 0, 70), text_color=color.white)
    table.cell(trendTable, column=0, row=8, text=(trend8 > 0 ? "涨" : "跌"), bgcolor=trend8 > 0 ? colorUp : colorDown)

if showTf9
    table.cell(trendTable, column=1, row=9, text="2时", bgcolor=color.rgb(0, 0, 0, 70), text_color=color.white)
    table.cell(trendTable, column=0, row=9, text=(trend9 > 0 ? "涨" : "跌"), bgcolor=trend9 > 0 ? colorUp : colorDown)

if showTf10
    table.cell(trendTable, column=1, row=10, text="4时", bgcolor=color.rgb(0, 0, 0, 70), text_color=color.white)
    table.cell(trendTable, column=0, row=10, text=(trend10 > 0 ? "涨" : "跌"), bgcolor=trend10 > 0 ? colorUp : colorDown)

if showTf11
    table.cell(trendTable, column=1, row=11, text="6时", bgcolor=color.rgb(0, 0, 0, 70), text_color=color.white)
    table.cell(trendTable, column=0, row=11, text=(trend11 > 0 ? "涨" : "跌"), bgcolor=trend11 > 0 ? colorUp : colorDown)

if showTf12
    table.cell(trendTable, column=1, row=12, text="12时", bgcolor=color.rgb(0, 0, 0, 70), text_color=color.white)
    table.cell(trendTable, column=0, row=12, text=(trend12 > 0 ? "涨" : "跌"), bgcolor=trend12 > 0 ? colorUp : colorDown)

if showTf13
    table.cell(trendTable, column=1, row=13, text="1天", bgcolor=color.rgb(0, 0, 0, 70), text_color=color.white)
    table.cell(trendTable, column=0, row=13, text=(trend13 > 0 ? "涨" : "跌"), bgcolor=trend13 > 0 ? colorUp : colorDown)

if showTf14
    table.cell(trendTable, column=1, row=14, text="1周", bgcolor=color.rgb(0, 0, 0, 70), text_color=color.white)
    table.cell(trendTable, column=0, row=14, text=(trend14 > 0 ? "涨" : "跌"), bgcolor=trend14 > 0 ? colorUp : colorDown)

if showTf15
    table.cell(trendTable, column=1, row=15, text="平均", bgcolor=color.rgb(0, 0, 0, 70), text_color=color.white)
    table.cell(trendTable, column=0, row=15, text=trendAvgText, bgcolor=trendAvgBgcolor)

// 使用calcMa计算短期和长期移动平均线
// ________________________________________________________________________________________________________________________________                      
maShort = calcMa(close, shortTerm)
maLong = calcMa(close, longTerm)

// 绘制表格和移动平均线
// ________________________________________________________________________________________________________________________________                      
plot(showShortTerm ? maShort : na, title="短期移动平均线", color=color.blue, linewidth=2)
plot(showLongTerm ? maLong : na, title="长期移动平均线", color=color.orange, linewidth=2)

// 定义基于趋势平均值的警报条件
// ________________________________________________________________________________________________________________________________       
alertcondition(trendAvg <= -0.8, title="暴跌警报", message="平均趋势为暴跌。")
alertcondition(trendAvg <= -0.2 and trendAvg > -0.8, title="跌警报", message="平均趋势为跌。")
alertcondition(trendAvg > -0.2 and trendAvg < 0.2, title="中性警报", message="平均趋势为中性。")
alertcondition(trendAvg >= 0.2 and trendAvg < 0.8, title="涨警报", message="平均趋势为涨。")
alertcondition(trendAvg >= 0.8, title="暴涨警报", message="平均趋势为暴涨。")
