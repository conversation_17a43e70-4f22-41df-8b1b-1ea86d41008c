// 本 Pine Script™ 代码受 Mozilla 公共许可证 2.0 的条款约束，网址为 https://mozilla.org/MPL/2.0/
// © QuantAlgo

//@version=5
indicator(title="动态评分 PSAR [QuantAlgo]", overlay=false, timeframe="", timeframe_gaps=true)

//              ╔════════════════════════════════╗              //
//              ║      用户定义设置              ║              //
//              ╚════════════════════════════════╝              //

// 输入组
var string PSAR_Settings          = "════════ PSAR 设置 ════════"
var string Dynamic_Score_Settings = "════════ 动态评分设置 ════════"
var string Appearance_Settings    = "════════ 外观设置 ════════"

// 输入描述（工具提示）
tooltipPSARStart                  = "PSAR 计算的初始值。较小的值使 PSAR 对价格变化更敏感。"
tooltipPSARIncrement              = "PSAR 加速的步长值。增加此值使 PSAR 更快响应价格变动。"
tooltipPSARMax                    = "PSAR 在计算过程中可以达到的最大值。较高的值允许 PSAR 更紧密地跟随强趋势。"
tooltipWindowLength               = "用于计算动态评分的窗口长度。较大的窗口对长期趋势给予更多权重。"
tooltipUptrendThreshold           = "识别上升趋势的阈值。增加此值将检测到更强的上升趋势信号。"
tooltipDowntrendThreshold         = "识别下降趋势的阈值。减少此值将使指标检测到较弱的下降趋势信号。"
tooltipBullishColor               = "当 PSAR 处于看涨状态（高于零）时使用的颜色。"
tooltipBearishColor               = "当 PSAR 处于看跌状态（低于零）时使用的颜色。"
tooltipNeutralColor               = "当 PSAR 处于中性状态（不高于或低于零）时使用的颜色。"
tooltip_bar_coloring              = "根据检测到的趋势方向启用或禁用柱状图着色。如果禁用，请更改柱状图的 '中性颜色'。"

// PSAR 设置
psar_start                        = input.float(0.02,     "PSAR 起始值",          step=0.001, minval=0.0, tooltip=tooltipPSARStart,          group=PSAR_Settings)
psar_increment                    = input.float(0.0005,   "PSAR 增量",      step=0.001, minval=0.0, tooltip=tooltipPSARIncrement,      group=PSAR_Settings)
psar_max                          = input.float(0.2,      "PSAR 最大值",      step=0.001,             tooltip=tooltipPSARMax,            group=PSAR_Settings)

// 动态评分设置
window_len                        = input.int(60,         "窗口长度",                               tooltip=tooltipWindowLength,       group=Dynamic_Score_Settings)
uptrend_threshold                 = input.int(30,         "上升趋势阈值",                           tooltip=tooltipUptrendThreshold,   group=Dynamic_Score_Settings)
downtrend_threshold               = input.int(-20,        "下降趋势阈值",                         tooltip=tooltipDowntrendThreshold, group=Dynamic_Score_Settings)

// 外观设置
bullish_color                     = input.color(#00ffaa, "看涨颜色",                              tooltip=tooltipBullishColor,       group=Appearance_Settings)
bearish_color                     = input.color(#ff0000, "看跌颜色",                              tooltip=tooltipBearishColor,       group=Appearance_Settings)
neutral_color                     = input.color(#787b86, "中性颜色",                              tooltip=tooltipNeutralColor,       group=Appearance_Settings)
bar                               = input.bool(true, "着色柱状图?",                                      tooltip=tooltip_bar_coloring,      group=Appearance_Settings)

//              ╔════════════════════════════════╗              //
//              ║        PSAR 计算              ║              //
//              ╚════════════════════════════════╝              //

psar_out                          = ta.sar(psar_start, psar_increment, psar_max)
psar                              = close - psar_out
normalized_psar                   = psar / ta.ema(high - low, 21) * 100

//              ╔════════════════════════════════╗              //
//              ║   QUANT ALGO 的动态评分      ║              //
//              ╚════════════════════════════════╝              //

var float[] score_array = array.new_float(window_len, 0)

dynamic_score() =>
    float score = 0.0
    for i = 0 to window_len - 1
        float comparison = normalized_psar > normalized_psar[i + 1] ? 1 : -1
        array.set(score_array, i, comparison)
        score += comparison
    score

trend_score = dynamic_score()

//              ╔════════════════════════════════╗              //
//              ║        趋势检测              ║              //
//              ╚════════════════════════════════╝              //

long_condition                    = (normalized_psar > 0) and (trend_score > uptrend_threshold)
short_condition                   = (normalized_psar < 0) and (trend_score < downtrend_threshold)

//              ╔════════════════════════════════╗              //
//              ║         可视化              ║              //
//              ╚════════════════════════════════╝              //

// 使用渐变颜色绘制 PSAR 值，基于趋势方向
uptrend_color                     = color.from_gradient(normalized_psar, 0, 400, color.new(bullish_color, 40), bullish_color)
downtrend_color                   = color.from_gradient(normalized_psar, -400, 0, bearish_color, color.new(bearish_color, 40))
final_color                       = normalized_psar > 0 ? uptrend_color : downtrend_color

plot(normalized_psar, "动态 PSAR", color=final_color, style=plot.style_columns)

// 为看涨和看跌反转添加标记
plotchar(ta.crossunder(normalized_psar, 0) ? 400 : na, "看跌反转", "▼", location.absolute, bearish_color, size=size.tiny)
plotchar(ta.crossover(normalized_psar, 0) ? -400 : na, "看涨反转", "▲", location.absolute, bullish_color, size=size.tiny)

// 绘制超买/超卖水平，并填充区域以突出显示区域
ob_upper = 900
os_upper = -900
ob_lower = 600
os_lower = -600

obupper = plot(ob_upper, "上超买", uptrend_color)
osupper = plot(os_upper, "上超卖", downtrend_color)
oblower = plot(ob_lower, "下超买", uptrend_color)
oslower = plot(os_lower, "下超卖", downtrend_color)

fill(obupper, oblower, color.new(bullish_color, 90), title="超买填充")
fill(osupper, oslower, color.new(bearish_color, 90), title="超卖填充")

// 绘制 0 线以供参考
midline = hline(0, "0 线", #787b86, hline.style_solid)

// 添加趋势背景着色
bgcolor(long_condition ? color.new(bullish_color, 90) : short_condition ? color.new(bearish_color, 90) : na)

// 保持趋势转换时的背景闪烁效果
bgcolor(ta.crossover(normalized_psar, 0) ? color.new(bullish_color, 70) : na)
bgcolor(ta.crossunder(normalized_psar, 0) ? color.new(bearish_color, 70) : na)

//              ╔════════════════════════════════╗              //
//              ║             警报             ║              //
//              ╚════════════════════════════════╝              //

alertcondition(ta.crossover(normalized_psar, 0)  and long_condition,  title="看涨反转", message="动态评分 PSAR 交叉上升至零线并确认看涨")
alertcondition(ta.crossunder(normalized_psar, 0) and short_condition, title="看跌反转", message="动态评分 PSAR 交叉下降至零线并确认看跌")

//              ╔════════════════════════════════╗              //
//              ║           创建者             ║              //
//              ╚════════════════════════════════╝              //

// ██████╗ ██╗   ██╗ █████╗ ███╗   ██╗████████╗     █████╗ ██╗      ██████╗  ██████╗ 
//██╔═══██╗██║   ██║██╔══██╗████╗  ██║╚══██╔══╝    ██╔══██╗██║     ██╔════╝ ██╔═══██╗
//██║   ██║██║   ██║███████║██╔██╗ ██║   ██║       ███████║██║     ██║  ███╗██║   ██║
//██║▄▄ ██║██║   ██║██╔══██║██║╚██╗██║   ██║       ██╔══██║██║     ██║   ██║██║   ██║
//╚██████╔╝╚██████╔╝██║  ██║██║ ╚████║   ██║       ██║  ██║███████╗╚██████╔╝╚██████╔╝
// ╚══▀▀═╝  ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═══╝   ╚═╝       ╚═╝  ╚═╝╚══════╝ ╚═════╝  ╚═════╝