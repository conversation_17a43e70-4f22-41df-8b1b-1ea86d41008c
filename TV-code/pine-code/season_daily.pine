//@version=6
//@description 日级别季节性分析指标，分析每个月内不同日期的历史表现
//<AUTHOR>
//@lib_version=2

indicator("日级别季节性", overlay = false, max_boxes_count = 500)

// === 输入参数 ===
var string analysisMode = input.string("月份范围", "分析模式", options=["月份范围", "跨年同月"], tooltip="月份范围：分析指定年份内不同月份的表现\n跨年同月：分析多个年份同一月份的表现")

// 月份范围模式参数组
var string MONTH_RANGE_GRP = "月份范围模式设置"
var int yearToObserve = input.int(2015, "观察年份", minval=1800, group=MONTH_RANGE_GRP, tooltip="选择要分析的具体年份")
var int startMonth = input.int(1, "起始月份", minval=1, maxval=12, group=MONTH_RANGE_GRP, tooltip="分析的起始月份")
var int endMonth = input.int(12, "结束月份", minval=1, maxval=12, group=MONTH_RANGE_GRP, tooltip="分析的结束月份")

// 跨年同月模式参数组
var string YEAR_RANGE_GRP = "跨年同月模式设置"
var int startYear = input.int(2015, "起始年份", minval=1800, group=YEAR_RANGE_GRP, tooltip="跨年对比的起始年份")
var int endYear = input.int(2023, "结束年份", minval=1800, group=YEAR_RANGE_GRP, tooltip="跨年对比的结束年份（最多显示12年）")
var int compareMonth = input.int(12, "观察月份", minval=1, maxval=12, group=YEAR_RANGE_GRP, tooltip="选择要跨年对比的月份")
var bool filterSpecificYears = input.bool(false, "启用年份筛选", group=YEAR_RANGE_GRP, tooltip="是否只显示指定年份的数据")
var string specificYears = input.string("2020,2022,2023", "指定年份（用逗号分隔）", group=YEAR_RANGE_GRP, tooltip="输入要显示的年份，多个年份用逗号分隔，例如：2020,2022,2023")

// 通用设置参数组
var string COMMON_GRP = "通用设置"

// 解析年份字符串为数组
f_split_years(yearsStr) =>
    var a_years = array.new_int()
    array.clear(a_years)
    
    if str.length(yearsStr) > 0
        years = str.split(yearsStr, ",")
        for yearStr in years
            if str.length(yearStr) > 0
                array.push(a_years, math.round(str.tonumber(yearStr)))
    a_years

var selectedYears = f_split_years(specificYears)

var bool useTradingDays = input.bool(false, "使用交易日模式", group=COMMON_GRP, tooltip="开启后将按照实际交易日计算，忽略非交易日")
var color posColor = input.color(#089981, "正值颜色", inline="colors", group=COMMON_GRP)
var color negColor = input.color(#F23745, "负值颜色", inline="colors", group=COMMON_GRP)
var float cutoffPercent = input.float(5.0, "颜色强度截止值 (%)", minval=0.1, maxval=100.0, group=COMMON_GRP)

// 表格行计数变量
var int statRowPos = 0  // 用于存储统计行的位置

// 显示设置参数组
var string DISPLAY_GRP = "显示设置"
string displayMode = input.string("详细", "显示模式", options=["精简", "详细"], group=DISPLAY_GRP, tooltip="精简模式只显示趋势行，详细模式显示所有统计数据")
bool showDetailedStats = displayMode == "详细"
bool showAvg = showDetailedStats
bool showStDev = showDetailedStats
bool showPos = true  // 保持为true因为趋势行需要这个数据
var float tableWidth = input.float(100, "表格宽度 (%)", minval=0, maxval=100, group=DISPLAY_GRP, tooltip="表格宽度占所在窗格的百分比。如果该值为0，表格的宽度将适应内容。")
var float tableHeight = input.float(100, "表格高度 (%)", minval=0, maxval=100, group=DISPLAY_GRP, tooltip="表格高度占所在窗格的百分比。如果该值为0，表格的高度将适应内容。")
var string tablePosition = input.string("中间", "表格位置", options=["左", "中间", "右"], group=DISPLAY_GRP)
var string fontSize = input.string("small", "字体大小", options=["small", "normal", "large"], group=DISPLAY_GRP)
var string fontSizeStr = fontSize == "small" ? "8" : fontSize == "normal" ? "12" : "16"

// === 辅助函数 ===
calcColor(float value) =>
    baseColor = value >= 0 ? posColor : negColor
    strength = math.min(math.abs(value) / cutoffPercent, 1.0)
    color.new(baseColor, 100 - math.round(strength * 100))

// 实时数据的特殊颜色计算
calcRealtimeColor(float value) =>
    baseColor = value >= 0 ? color.new(posColor, 20) : color.new(negColor, 20)
    strength = math.min(math.abs(value) / cutoffPercent, 1.0)
    color.new(baseColor, 100 - math.round(strength * 100))

// 正变百分比的颜色计算
calcPosColor(float value) =>
    baseColor = value >= 50 ? posColor : negColor  // 大于50%显示正色，小于50%显示负色
    strength = math.min(math.abs(value - 50) / 25, 1.0)  // 以50%为中心，±25%作为颜色强度范围
    color.new(baseColor, 100 - math.round(strength * 100))

// === 数据收集 ===
var changes = array.new_float(31, 0.0)  // 存储每日变化
var counts = array.new_float(31, 0.0)   // 存储每日数据点数量
var pos_counts = array.new_float(31, 0.0)  // 存储每日正变化数量
var stdevs = array.new_float(31, 0.0)   // 存储每日标准差
var monthlyChanges = array.new_float(12 * 31, 0.0)  // 存储每月每日的变化
var monthlyCounts = array.new_float(12 * 31, 0.0)   // 存储每月每日的数据点数量
var monthlyPosCounts = array.new_float(12 * 31, 0.0)  // 存储每月每日的正变化数量
var yearlyChanges = array.new_float(12 * 31, 0.0)  // 存储每年指定月份的变化（最多12年）
var yearlyCounts = array.new_float(12 * 31, 0.0)   // 存储每年指定月份的数据点数量
var yearlyPosCounts = array.new_float(12 * 31, 0.0)  // 存储每年指定月份的正变化数量

// 交易日计数器
var int tradingDayCount = 0
var int lastYear = 0
var int lastMonth = 0

int currYear = year(time)
int dayOfMonth = dayofmonth(time)
int currentMonth = month(time)

// === 数据更新 ===
if not na(close) and not na(close[1])
    float dailyChange = (close - close[1]) / close[1] * 100
    int monthIndex = (currentMonth - 1) * 31 + dayOfMonth - 1
    int adjustedDayOfMonth = dayOfMonth
    
    if useTradingDays
        // 重置交易日计数的条件
        bool isNewMonth = currentMonth != lastMonth
        bool isNewYear = currYear != lastYear
        
        // 在新的年份或月份开始时重置计数器
        if isNewYear or isNewMonth
            tradingDayCount := 1
        else
            tradingDayCount := tradingDayCount + 1
        
        // 更新上一次的年份和月份
        lastYear := currYear
        lastMonth := currentMonth
        
        // 使用交易日序号替代自然日
        adjustedDayOfMonth := tradingDayCount
    
    if analysisMode == "月份范围"
        if currYear == yearToObserve and currentMonth >= startMonth and currentMonth <= endMonth
            monthIndex := (currentMonth - 1) * 31 + (adjustedDayOfMonth - 1)
            array.set(monthlyChanges, monthIndex, array.get(monthlyChanges, monthIndex) + dailyChange)
            array.set(monthlyCounts, monthIndex, array.get(monthlyCounts, monthIndex) + 1)
            if dailyChange > 0
                array.set(monthlyPosCounts, monthIndex, array.get(monthlyPosCounts, monthIndex) + 1)
    else  // 跨年同月模式
        if currYear >= startYear and currYear <= endYear and currentMonth == compareMonth
            int yearIndex = math.min(currYear - startYear, 11)  // 限制最多12年
            int dayIndex = yearIndex * 31 + (adjustedDayOfMonth - 1)
            array.set(yearlyChanges, dayIndex, array.get(yearlyChanges, dayIndex) + dailyChange)
            array.set(yearlyCounts, dayIndex, array.get(yearlyCounts, dayIndex) + 1)
            if dailyChange > 0
                array.set(yearlyPosCounts, dayIndex, array.get(yearlyPosCounts, dayIndex) + 1)

if barstate.islast
    // 首先计算每日的数据
    for d = 0 to 30
        float totalChange = 0.0
        float totalCount = 0.0
        float posCount = 0.0
        float sum = 0.0
        float sumSquared = 0.0
        
        if analysisMode == "月份范围"
            // 收集该日期的所有月份的数据，排除当前日期
            for m = startMonth to endMonth
                int idx = (m-1)*31 + d
                float value = array.get(monthlyChanges, idx)
                float count = array.get(monthlyCounts, idx)
                float posValue = array.get(monthlyPosCounts, idx)
                
                // 排除当前年月日的数据
                if count > 0 and not (currYear == yearToObserve and m == month and d + 1 == dayOfMonth)
                    totalChange += value
                    totalCount += count
                    posCount += posValue
                    sum += value
                    sumSquared += value * value
        else
            // 收集该日期的所有年份的数据，排除当前日期
            int yearCount = math.min(endYear - startYear + 1, 12)
            for y = 0 to yearCount - 1
                int idx = y*31 + d
                float value = array.get(yearlyChanges, idx)
                float count = array.get(yearlyCounts, idx)
                float posValue = array.get(yearlyPosCounts, idx)
                
                // 排除当前年月日的数据
                if count > 0 and not (currYear == startYear + y and month == compareMonth and d + 1 == dayOfMonth)
                    totalChange += value
                    totalCount += count
                    posCount += posValue
                    sum += value
                    sumSquared += value * value

        // 计算并存储结果
        if totalCount > 0
            array.set(changes, d, totalChange / totalCount)  // 平均值
            array.set(counts, d, totalCount)  // 总数据点
            array.set(pos_counts, d, posCount)  // 正值数量
            
            // 计算标准差
            float mean = totalChange / totalCount
            if totalCount > 1
                float variance = (sumSquared - (totalChange * totalChange / totalCount)) / (totalCount - 1)
                array.set(stdevs, d, math.sqrt(variance))

    // 根据分析模式确定行数和统计行位置
    int rowCount = if analysisMode == "月份范围"
        endMonth - startMonth + 1
    else
        math.min(endYear - startYear + 1, 12)  // 限制最多显示12年
    
    // 设置统计行的位置
    if analysisMode == "月份范围"
        statRowPos := rowCount + 2  // 在所有月份数据之后
    else
        statRowPos := 4  // 使用显示计数来确定统计行的位置

    // 设置表格位置
    var pos = switch tablePosition
        "左" => position.top_left
        "右" => position.top_right
        => position.top_center

    var t = table.new(position=pos, columns=32, rows=rowCount + 10, bgcolor=color.new(color.black, 90))
    
    // 计算单元格宽度
    float cellWidth = tableWidth > 0 ? math.round(tableWidth / 32) : 0  // 32列平分宽度
    float cellHeight = tableHeight > 0 ? math.round(tableHeight / (rowCount + 10)) : 0  // 总行数平分高度
    
    // 添加空行
    table.cell(t, 0, 0, text="", bgcolor=color.new(color.black, 100), text_color=color.white, text_size=fontSize, width=cellWidth, height=cellHeight)
    
    // 日期行（只保留一行）
    table.cell(t, 0, 1, text="日期", bgcolor=color.new(color.gray, 90),
               text_color=color.white, text_size=fontSize, width=cellWidth, height=cellHeight)
    for i = 1 to 31
        table.cell(t, i, 1, text=str.tostring(i), bgcolor=color.new(color.gray, 90),
                   text_color=color.white, text_size=fontSize, width=cellWidth, height=cellHeight)
    
    if analysisMode == "月份范围"
        // 月度数据显示（从第2行开始）
        for m = startMonth to endMonth
            monthRow = m - startMonth + 2  // 月份数据从第2行开始
            table.cell(t, 0, monthRow, text=str.tostring(m) + "月",
                       text_color=color.white, text_size=fontSize, width=cellWidth, height=cellHeight)
            
            for d = 0 to 30
                monthIndex = (m-1)*31 + d
                float value = array.get(monthlyChanges, monthIndex)
                float count = array.get(monthlyCounts, monthIndex)
                
                if count > 0
                    float avgChange = value / count
                    string displayValue = if showDetailedStats
                        str.tostring(avgChange, format.percent)
                    else
                        str.tostring(avgChange, "#.#")
                    
                    color cellColor = calcColor(avgChange)
                    // 检查是否是当前日期
                    bool isCurrentDay = (currYear == yearToObserve and m == month and d + 1 == dayOfMonth)
                    if isCurrentDay
                        float dailyChange = (close - close[1]) / close[1] * 100
                        displayValue := "▶" + str.tostring(dailyChange, "#.##") + "%"
                        cellColor := calcRealtimeColor(dailyChange)
                    
                    table.cell(t, d + 1, monthRow, text=displayValue,
                               bgcolor=cellColor,
                               text_color=color.white, text_size=fontSize, width=cellWidth, height=cellHeight)
                else
                    table.cell(t, d + 1, monthRow, text="-",
                               text_color=color.white, text_size=fontSize, width=cellWidth, height=cellHeight)
    else
        // 跨年同月数据显示（从第2行开始）
        int yearCount = 0
        int displayedCount = 0  // 用于跟踪实际显示的行数
        
        for y = startYear to endYear
            if yearCount < 12  // 限制最多显示12年
                showYear = not filterSpecificYears
                if filterSpecificYears
                    for i = 0 to array.size(selectedYears) - 1
                        if y == array.get(selectedYears, i)
                            showYear := true
                            break
                
                if showYear  // 检查是否显示该年份的数据
                    yearRow = displayedCount + 2  // 年份数据从第2行开始，使用显示计数而不是年份计数
                    table.cell(t, 0, yearRow, text=str.tostring(y) + "年" + str.tostring(compareMonth) + "月",
                               text_color=color.white, text_size=fontSize, width=cellWidth, height=cellHeight)
                    
                    for d = 0 to 30
                        int dayIndex = yearCount * 31 + d
                        float value = array.get(yearlyChanges, dayIndex)
                        float count = array.get(yearlyCounts, dayIndex)
                        
                        if count > 0
                            float avgChange = value / count
                            string displayValue = if showDetailedStats
                                str.tostring(avgChange, format.percent)
                            else
                                str.tostring(avgChange, "#.#")
                            
                            color cellColor = calcColor(avgChange)
                            // 检查是否是当前日期
                            bool isCurrentDay = (currYear == y and month == compareMonth and d + 1 == dayOfMonth)
                            if isCurrentDay
                                float dailyChange = (close - close[1]) / close[1] * 100
                                displayValue := "▶" + str.tostring(dailyChange, "#.##") + "%"
                                cellColor := calcRealtimeColor(dailyChange)
                            
                            table.cell(t, d + 1, yearRow, text=displayValue,
                                       bgcolor=cellColor,
                                       text_color=color.white, text_size=fontSize, width=cellWidth, height=cellHeight)
                        else
                            table.cell(t, d + 1, yearRow, text="-",
                                       text_color=color.white, text_size=fontSize, width=cellWidth, height=cellHeight)
                    displayedCount += 1  // 只在实际显示数据时增加显示计数
                yearCount += 1

    // 统计信息（在数据行下方）
    if showDetailedStats
        // 平均值行
        table.cell(t, 0, statRowPos, text="平均值", text_color=color.white, text_size=fontSize, width=cellWidth, height=cellHeight)
        for d = 0 to 30
            float avg = array.get(changes, d)
            table.cell(t, d + 1, statRowPos, text=str.tostring(avg, format.percent),
                       text_color=color.white, text_size=fontSize, width=cellWidth, height=cellHeight)
        statRowPos += 1

        // 中位数行
        table.cell(t, 0, statRowPos, text="中位数", text_color=color.white, text_size=fontSize, width=cellWidth, height=cellHeight)
        for d = 0 to 30
            float[] dailyChanges = array.new_float(0)
            if analysisMode == "月份范围"
                for m = startMonth to endMonth
                    int idx = (m-1)*31 + d
                    float value = array.get(monthlyChanges, idx)
                    float count = array.get(monthlyCounts, idx)
                    if count > 0
                        array.push(dailyChanges, value / count)
            else
                int yearCount = math.min(endYear - startYear + 1, 12)
                for y = 0 to yearCount - 1
                    int idx = y*31 + d
                    float value = array.get(yearlyChanges, idx)
                    float count = array.get(yearlyCounts, idx)
                    if count > 0
                        array.push(dailyChanges, value / count)
            
            float median = array.size(dailyChanges) > 0 ? array.median(dailyChanges) : 0.0
            table.cell(t, d + 1, statRowPos, text=str.tostring(median, format.percent),
                       text_color=color.white, text_size=fontSize, width=cellWidth, height=cellHeight)
        statRowPos += 1

        // 标准差行
        table.cell(t, 0, statRowPos, text="标准差", text_color=color.white, text_size=fontSize, width=cellWidth, height=cellHeight)
        for d = 0 to 30
            float stdev = array.get(stdevs, d)
            table.cell(t, d + 1, statRowPos, text=str.tostring(stdev, format.percent),
                       text_color=color.white, text_size=fontSize, width=cellWidth, height=cellHeight)
        statRowPos += 1

        // 涨概率行
        table.cell(t, 0, statRowPos, text="涨概率", text_color=color.white, text_size=fontSize, width=cellWidth, height=cellHeight)
        for d = 0 to 30
            float count = array.get(counts, d)
            float posCount = array.get(pos_counts, d)
            if count > 0
                float posPercent = posCount / count * 100
                color cellColor = calcPosColor(posPercent)
                table.cell(t, d + 1, statRowPos, text=str.tostring(posPercent, "#.#") + "%",
                           bgcolor=cellColor,
                           text_color=color.white, text_size=fontSize, width=cellWidth, height=cellHeight)
            else
                table.cell(t, d + 1, statRowPos, text="-",
                           text_color=color.white, text_size=fontSize, width=cellWidth, height=cellHeight)
        statRowPos += 1
    
    // 趋势行（无论是否精简模式都显示）
    table.cell(t, 0, statRowPos, text="趋势", text_color=color.white, text_size=fontSize, width=cellWidth, height=cellHeight)

    // 计算阈值
    float maxAbsValue = 0.0
    float maxStdev = 0.0
    for d = 0 to 30
        float avg = array.get(changes, d)
        float[] dailyChanges = array.new_float(0)
        if analysisMode == "月份范围"
            for m = startMonth to endMonth
                int idx = (m-1)*31 + d
                float value = array.get(monthlyChanges, idx)
                float count = array.get(monthlyCounts, idx)
                if count > 0
                    array.push(dailyChanges, value / count)
        else
            int yearCount = math.min(endYear - startYear + 1, 12)
            for y = 0 to yearCount - 1
                int idx = y*31 + d
                float value = array.get(yearlyChanges, idx)
                float count = array.get(yearlyCounts, idx)
                if count > 0
                    array.push(dailyChanges, value / count)
        
        float median = array.size(dailyChanges) > 0 ? array.median(dailyChanges) : 0.0
        float stdev = array.get(stdevs, d)
        
        maxAbsValue := math.max(maxAbsValue, math.abs(avg))
        maxAbsValue := math.max(maxAbsValue, math.abs(median))
        maxStdev := math.max(maxStdev, stdev)

    float valueThreshold = maxAbsValue
    float stdevThreshold = maxStdev

    for d = 0 to 30
        // 收集该日期的所有变化数据
        float[] dailyChanges = array.new_float(0)
        if analysisMode == "月份范围"
            for m = startMonth to endMonth
                int idx = (m-1)*31 + d
                float value = array.get(monthlyChanges, idx)
                float count = array.get(monthlyCounts, idx)
                if count > 0
                    array.push(dailyChanges, value / count)
        else
            int yearCount = math.min(endYear - startYear + 1, 12)
            for y = 0 to yearCount - 1
                int idx = y*31 + d
                float value = array.get(yearlyChanges, idx)
                float count = array.get(yearlyCounts, idx)
                if count > 0
                    array.push(dailyChanges, value / count)
        
        float avg = array.size(dailyChanges) > 0 ? array.sum(dailyChanges) / array.size(dailyChanges) : 0.0
        float median = array.size(dailyChanges) > 0 ? array.median(dailyChanges) : 0.0
        float stdev = array.get(stdevs, d)
        float totalCount = array.get(counts, d)
        float posCount = array.get(pos_counts, d)
        float posRatio = totalCount > 0 ? (posCount / totalCount) * 100 : 0.0
        
        string symbol = "?"
        float probability = 0.0
        
        if totalCount < 3  // 数据点太少
            probability := 0.0
        else
            // 1. 计算方向性指标
            float posRatioScore = (posRatio - 50) / 25  // 归一化并放大效果
            float avgScore = avg / valueThreshold  // 使用最大绝对值归一化
            float medianScore = median / valueThreshold  // 使用最大绝对值归一化
            
            // 2. 计算指标一致性
            bool posRatioAgrees = (posRatio > 50 and avg > 0) or (posRatio < 50 and avg < 0)
            bool medianAgrees = (avg > 0 and median > 0) or (avg < 0 and median < 0)
            float consistencyScore = (posRatioAgrees ? 1.2 : 0.4) * (medianAgrees ? 1.2 : 0.4)
            
            // 3. 计算标准差影响因子
            float stdevFactor = math.exp(-stdev / stdevThreshold * 2)  // 使用最大标准差归一化
            
            // 4. 计算综合方向分数
            float directionScore = math.abs(posRatioScore * 0.35 + avgScore * 0.3 + medianScore * 0.35)
            
            // 5. 计算最终概率
            probability := math.min(directionScore * consistencyScore * (0.6 + 0.4 * stdevFactor) * 100, 98)
            
            // 6. 确定方向
            if probability >= 5
                if ((posRatio > 52 and avg > 0) or (posRatio > 60))
                    symbol := "↑"
                else if ((posRatio < 48 and avg < 0) or (posRatio < 40))
                    symbol := "↓"
                else
                    symbol := "?"
                    probability := 5
            else
                symbol := "?"
                probability := 5

        string displayText = if showDetailedStats
            str.tostring(probability, format.percent) + " " + symbol  // 详细模式：显示带百分号的概率
        else
            str.tostring(math.round(probability), "#") + "% " + symbol  // 精简模式：显示整数概率
        
        table.cell(t, d+1, statRowPos, text=displayText, 
                  text_color=symbol == "↑" ? posColor : symbol == "↓" ? negColor : color.white, 
                  text_size=fontSize, width=cellWidth, height=cellHeight)
    
    // 添加底部空行
    statRowPos += 1
    table.cell(t, 0, statRowPos, text="", bgcolor=color.new(color.black, 100), text_color=color.white, text_size=fontSize, width=cellWidth, height=cellHeight)
    for i = 1 to 31
        table.cell(t, i, statRowPos, text="", bgcolor=color.new(color.black, 100), text_color=color.white, text_size=fontSize, width=cellWidth, height=cellHeight)
