//@version=5
indicator("散户情绪与大户仓位估计", overlay=true)

// --- 散户情绪估计 ---
rsiLength = input.int(14, title="RSI周期")
rsi = ta.rsi(close, rsiLength)

// 定义情绪阈值
overbought = 70
oversold = 30

// 散户情绪信号
retailSentiment = rsi > overbought ? -1 : (rsi < oversold ? 1 : 0)

// --- 大户仓位估计 ---
mfiLength = input.int(14, title="MFI周期")

// 手动计算 MFI
typicalPrice = (high + low + close) / 3
moneyFlow = typicalPrice * volume

// 计算正货币流和负货币流
posMF = typicalPrice > nz(typicalPrice[1]) ? moneyFlow : 0
negMF = typicalPrice < nz(typicalPrice[1]) ? moneyFlow : 0

// 使用 ta.sma() 计算 sum(posMF, mfiLength) = ta.sma(posMF, mfiLength) * mfiLength
mfPositive = ta.sma(posMF, mfiLength) * mfiLength
mfNegative = ta.sma(negMF, mfiLength) * mfiLength

// 计算 MFI
mfi = (mfNegative == 0 and mfPositive == 0) ? 50 : 100 - (100 / (1 + mfPositive / mfNegative))

// 定义大户仓位阈值
mfiOverbought = 80
mfiOversold = 20

// --- 背景颜色设置 ---
color bgColor = na
if (retailSentiment == 1 and mfi < mfiOversold)
    bgColor := color.new(color.green, 90)
else if (retailSentiment == -1 and mfi > mfiOverbought)
    bgColor := color.new(color.red, 90)

bgcolor(bgColor)