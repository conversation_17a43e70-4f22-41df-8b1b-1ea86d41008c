# 📊 TradingView Pine Script 量化分析工具集

本仓库包含了一系列专业的量化交易分析工具，主要基于TradingView Pine Script v5开发。

## 📁 项目结构

```
TV-code/
├── 📄 MCSI_Logic_Documentation.md    # MCSI多指标综合评分系统完整文档
├── 📄 MCSI.pine                      # MCSI多指标综合评分主脚本
├── 📄 all-pattern.pine               # 全K线形态识别脚本
├── 📄 SectorRotationMap.pine         # 板块轮动分析脚本
├── 📄 README.md                      # 项目说明文档
└── 📁 其他Pine脚本文件...
```

## 🎯 核心功能

### 📊 MCSI多指标综合评分系统
- **文档**: `MCSI_Logic_Documentation.md`
- **脚本**: `MCSI.pine`
- **功能**: 基于5个核心指标的综合评分系统
- **指标**: CRSI、MACD、TD9、SMC、MMT
- **特点**: 专业评分、风险控制、实时分析

### 🔍 K线形态识别
- **脚本**: `all-pattern.pine`
- **功能**: 识别常见的K线形态
- **类型**: 反转形态、持续形态、组合形态

### 📈 板块轮动分析
- **脚本**: `SectorRotationMap.pine`
- **功能**: 分析不同板块的轮动情况
- **应用**: 行业配置、资产轮动

## 🛠 使用指南

### 1. TradingView平台使用
1. 打开TradingView网站
2. 进入Pine Script编辑器
3. 复制对应的`.pine`文件内容
4. 粘贴到编辑器并保存
5. 添加到图表中使用

### 2. Python集成使用
- 参考`MCSI_Logic_Documentation.md`中的实现细节
- 使用pandas和numpy重现Pine Script逻辑
- 集成到自己的量化交易系统中

## 📚 详细文档

### 🔗 MCSI系统文档
详细的MCSI系统说明请查看：[MCSI_Logic_Documentation.md](./MCSI_Logic_Documentation.md)

包含内容：
- 🎨 可视化图表展示
- 📖 实践应用指南
- 🏗️ 系统架构总结
- 📋 总结与展望

### 📊 指标说明

| 指标 | 全称 | 功能 |
|------|------|------|
| CRSI | Cyclic RSI | 周期性相对强弱指标 |
| MACD | Moving Average Convergence Divergence | 异同移动平均线 |
| TD9 | Tom DeMark Sequential | TD序列计数 |
| SMC | Smart Money Concept | 智能资金概念 |
| MMT | Market Momentum Tracker | 市场动量跟踪 |

## ⚡ 快速开始

```bash
# 克隆项目
git clone http://billy1991830.x3322.net:8418/billy/TradingView-code.git

# 查看MCSI文档
cd TradingView-code
cat MCSI_Logic_Documentation.md
```

## 🔧 技术栈

- **Pine Script v5**: TradingView原生脚本语言
- **Python 3.8+**: 数据处理和系统集成
- **pandas**: 数据分析库
- **numpy**: 数值计算库
- **Flask**: Web框架（用于系统集成）

## 📈 性能特点

- **高精度**: 与TradingView完全一致的计算结果
- **高性能**: 单股评分处理时间 < 100ms
- **实时性**: 支持实时数据更新和分析
- **可扩展**: 模块化设计，易于添加新指标

## 📞 联系方式

- **项目地址**: http://billy1991830.x3322.net:8418/billy/TradingView-code.git
- **技术支持**: 请通过项目Issues提交问题

## 📄 许可证

本项目仅供学习和研究使用，请勿用于商业目的。

---

*版权所有 © 2024 | 仅供学习研究使用*
