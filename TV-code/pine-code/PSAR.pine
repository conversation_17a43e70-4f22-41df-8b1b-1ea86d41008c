//@version=5
indicator("动态评分 PSAR [QuantAlgo]", overlay=true)

// === 输入参数 === (类比PID控制器参数)
debug_mode = input.bool(false, "调试模式 - 显示目标曲线和实际曲线")
auto_optimize = input.bool(true, "启用自动参数优化")
optimization_period = input.int(100, "优化周期(K线数)", minval=50, maxval=1000)
lookback_period = input.int(20, "回溯周期", minval=10, maxval=100)

// 初始参数设置
var float curr_startFactor = 0.02
var float curr_increment = 0.02
var float curr_maxFactor = 0.2

// 颜色设置
psar_color_up = input.color(color.green, "上涨颜色")
psar_color_down = input.color(color.red, "下跌颜色")
bullish_color = input.color(color.green, "看涨颜色")
bearish_color = input.color(color.red, "看跌颜色")

// 趋势参数
window_len = input.int(300, "评分窗口长度")
uptrend_threshold = input.float(0.6, "上涨趋势阈值")
downtrend_threshold = input.float(-0.6, "下跌趋势阈值")

// === 趋势匹配度评分函数 ===
calculate_trend_score(float start_f, float incr, float max_f) =>
    var float score = 0.0
    var int count = 0
    
    for i = 0 to lookback_period - 1
        float test_psar = nz(ta.sar(start_f, incr, max_f)[i])
        float test_normalized = ((close[i] - test_psar) / ta.ema(high - low, 21)[i]) * 100
        
        float price_trend = ((close[i] - close[i+1]) / close[i+1]) * 100
        
        float direction_match = math.sign(test_normalized) == math.sign(price_trend) ? 1.0 : 0.0
        float strength_match = 1.0 / (1.0 + math.abs(math.abs(test_normalized) - math.abs(price_trend)))
        
        score += direction_match * strength_match
        count += 1
    
    result = count > 0 ? score / count : 0.0
    result

// === 参数优化函数 ===
optimize_parameters() =>
    var float best_score = 0.0
    var float opt_start = curr_startFactor
    var float opt_incr = curr_increment
    var float opt_max = curr_maxFactor
    
    // 定义参数搜索范围
    var test_starts = array.new_float(5, 0.0)
    var test_increments = array.new_float(5, 0.0)
    var test_maxes = array.new_float(5, 0.0)
    
    if array.size(test_starts) == 5
        array.set(test_starts, 0, 0.001)
        array.set(test_starts, 1, 0.005)
        array.set(test_starts, 2, 0.01)
        array.set(test_starts, 3, 0.02)
        array.set(test_starts, 4, 0.05)
        
        array.set(test_increments, 0, 0.001)
        array.set(test_increments, 1, 0.005)
        array.set(test_increments, 2, 0.01)
        array.set(test_increments, 3, 0.02)
        array.set(test_increments, 4, 0.05)
        
        array.set(test_maxes, 0, 0.05)
        array.set(test_maxes, 1, 0.1)
        array.set(test_maxes, 2, 0.2)
        array.set(test_maxes, 3, 0.3)
        array.set(test_maxes, 4, 0.4)
    
    for i = 0 to 4
        float start_f = array.get(test_starts, i)
        for j = 0 to 4
            float incr = array.get(test_increments, j)
            for k = 0 to 4
                float max_f = array.get(test_maxes, k)
                float current_score = calculate_trend_score(start_f, incr, max_f)
                
                if current_score > best_score
                    best_score := current_score
                    opt_start := start_f
                    opt_incr := incr
                    opt_max := max_f
    
    [opt_start, opt_incr, opt_max, best_score]

// === 定期优化参数 ===
if auto_optimize and bar_index % optimization_period == 0
    [new_start, new_incr, new_max, score] = optimize_parameters()
    curr_startFactor := new_start
    curr_increment := new_incr
    curr_maxFactor := new_max

// === PSAR计算 ===
psar_out = ta.sar(curr_startFactor, curr_increment, curr_maxFactor)
psar = close - psar_out
normalized_psar = psar / ta.ema(high - low, 21) * 100

// === 实际值和目标值计算 ===
price_change = ((close - close[1]) / close[1]) * 100
actual_value = ta.sma(price_change, 5)
target_value = normalized_psar

// === 误差计算 ===
error = math.abs(actual_value - target_value)
var float avg_error = 0.0
avg_error := (avg_error * 19 + error) / 20

// === 动态评分 ===
var float[] score_array = array.new_float(window_len, 0)

dynamic_score() =>
    var float score = 0.0
    for i = 0 to window_len - 1
        float comparison = normalized_psar > normalized_psar[i + 1] ? 1.0 : -1.0
        array.set(score_array, i, comparison)
        score += comparison
    score

trend_score = dynamic_score()

// === 趋势检测 ===
long_condition = (normalized_psar > 0) and (trend_score > uptrend_threshold)
short_condition = (normalized_psar < 0) and (trend_score < downtrend_threshold)

// === 可视化 ===
uptrend_color = color.from_gradient(normalized_psar, 0, 400, color.new(bullish_color, 40), bullish_color)
downtrend_color = color.from_gradient(normalized_psar, -400, 0, bearish_color, color.new(bearish_color, 40))
final_color = normalized_psar > 0 ? uptrend_color : downtrend_color

// 调试曲线
debug_actual = debug_mode ? actual_value : na
debug_target = debug_mode ? target_value : na
debug_error = debug_mode ? avg_error : na

plot(debug_actual, "实际价格变化", color=color.new(color.blue, 50), linewidth=2)
plot(debug_target, "趋势判断结果", color=color.new(color.orange, 50), linewidth=2)
plot(debug_error, "平均误差", color=color.new(color.red, 50), linewidth=1)

plot(normalized_psar, "动态 PSAR", color=final_color, style=plot.style_columns)

// 趋势反转标记
plotchar(ta.crossunder(normalized_psar, 0) ? 400 : na, "看跌反转", "▼", location.absolute, bearish_color, size=size.tiny)
plotchar(ta.crossover(normalized_psar, 0) ? -400 : na, "看涨反转", "▲", location.absolute, bullish_color, size=size.tiny)

// 参考线
h1 = hline(600, "上超买", color=color.new(bullish_color, 50))
h2 = hline(-600, "下超卖", color=color.new(bearish_color, 50))
fill(h1, h2, color=color.new(color.gray, 95))
hline(0, "0 线", color=color.new(color.gray, 50))

// 背景着色
bgcolor(long_condition ? color.new(bullish_color, 90) : short_condition ? color.new(bearish_color, 90) : na)
bgcolor(ta.crossover(normalized_psar, 0) ? color.new(bullish_color, 70) : na)
bgcolor(ta.crossunder(normalized_psar, 0) ? color.new(bearish_color, 70) : na)

// === 参数显示 ===
if barstate.islast
    label.new(bar_index, high, 
             text="当前参数:\n起始: " + str.tostring(curr_startFactor, "#.####") + 
                  "\n增量: " + str.tostring(curr_increment, "#.####") + 
                  "\n最大: " + str.tostring(curr_maxFactor, "#.####") + 
                  "\n平均误差: " + str.tostring(avg_error, "#.##"),
             style=label.style_label_down,
             color=color.new(color.blue, 80),
             textcolor=color.white,
             size=size.small)