# TradingView Pine 脚本功能说明

本仓库收录了多个 TradingView Pine Script 脚本，涵盖趋势、动量、形态、季节性等多种量化分析工具。

---

## 各脚本功能简介

### 1. AVWAP.pine
自动锚定 VWAP（成交量加权平均价）指标，支持不同加权算法，帮助判断价格与成交量的动态关系。

### 2. EMA+MA.pine
绘制多周期 EMA 与 SMA 均线，支持多均线斜率与排列判断，辅助趋势识别。

### 3. MCSI-macd-test.pine
多指标综合评分系统的 MACD 测试脚本，用于单独评估 MACD 指标的信号打分效果。

### 4. MCSI-mmt-test.pine
多指标综合评分系统的 MMT 测试脚本，用于单独评估 MMT 指标的信号打分效果。

### 5. MCSI-rsi-test.pine
多指标综合评分系统的 RSI 测试脚本，用于单独评估 RSI 指标的信号打分效果。

### 6. MCSI-td9-test.pine
多指标综合评分系统的 TD9 测试脚本，用于单独评估 TD9 指标的信号打分效果。

### 7. MCSI.pine
多指标综合评分主脚本，融合 RSI、MACD、TD9、SMC、MMT 等多种信号，输出综合分数，辅助买卖决策。

### 8. MTSI.pine
多周期趋势评分脚本，适合多时间框架趋势一致性分析。

### 9. Mmt.pine
基于周期动量的趋势指标，适合捕捉主导周期的波动与拐点。

### 10. PSAR.pine
动态评分版 PSAR（抛物线转向指标），结合趋势评分和参数自适应优化。

### 11. SectorRotationMap.pine
板块轮动分析工具，显示各行业/板块的相对强弱、趋势和收益表现。

### 12. TC.pine
趋势变化检测指标，结合 EMA、ATR、SMA 等多种均线，适合捕捉多周期趋势转换。

### 13. TD9.pine
TD Sequential 序列计数指标，辅助判断趋势耗竭与反转时机。

### 14. all-pattern.pine
K线形态合集，自动识别多种经典单根/多根 K 线形态，适合形态交易者。

### 15. macd.pine
经典 MACD 指标，带多种可视化和信号点标记。

### 16. multi-time.pine
多时间周期趋势指标，支持多周期趋势一致性分析。

### 17. rsi.pine
循环平滑 RSI 指标，支持背离检测和多周期信号。

### 18. season.pine
月度季节性分析指标，统计不同月份的历史表现和涨跌概率。

### 19. season_daily.pine
日级别季节性分析，分析每月内不同日期的历史涨跌表现。

### 20. six.pine
多指标趋势评分脚本，融合六种常用技术指标，输出综合趋势分数。

### 21. smc.pine
散户情绪与大户仓位估计，结合 RSI 与 MFI 判断市场资金结构。

### 22. time_pattern_analysis.pine
时间段趋势概率分析，统计不同时间段内趋势出现的概率。

### 23. try.pine
动态评分 PSAR 测试脚本，包含多种自定义参数与评分逻辑。

### 24. try1.pine
趋势变化指标测试脚本，支持多周期均线与趋势检测。

---

如需详细逻辑说明，请参考各脚本内注释。
