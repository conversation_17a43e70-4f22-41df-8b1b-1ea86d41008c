// 创建者: 用户 ChrisMoody 更新于2014-10-04
// 修改者: [您的名字] 更新于2024-10-14

// 常规MACD指标，带有根据零线上下方向绘制4种颜色的柱状图

// 更新允许勾选框选项，显示MACD和信号线，基于信号线交叉显示MACD线颜色变化

// 在MACD和信号线交叉处显示点，柱状图可以显示4种颜色或1种，开启或关闭柱状图

// 特别感谢技术支持部门的那位不可思议的人，我不会说你的名字，以免你被邮件轰炸

// 注意：技术支持向我展示了如何将指标的默认时间框设置为图表时间框架，同时允许你选择不同的时间框架的功能

// 顺便说一下，我完全披露我完全从"TheLark"那里偷来的MACD交叉点的点

study(title="CM_MACD_终极_MTF", shorttitle="CM_终极_MACD_MTF")

// 数据源设置为收盘价
source = close

// 是否使用当前图表的分辨率
useCurrentRes = input(true, title="使用当前图表分辨率？")

// 自定义时间框架，当“使用当前图表分辨率？”未选中时生效
resCustom = input(title="使用不同的时间框架？请取消上方复选框", type=resolution, defval="60")

// 是否显示MACD和信号线，同时关闭交叉点的显示
smd = input(true, title="显示MACD和信号线？同时关闭下方点")

// 是否在MACD与信号线交叉时显示点
sd = input(true, title="MACD与信号线交叉时显示点？")

// 是否显示柱状图
sh = input(true, title="显示柱状图？")

// 是否根据信号线交叉更改MACD线的颜色
macd_colorChange = input(true, title="基于信号线交叉更改MACD线颜色？")

// 是否让MACD柱状图显示4种颜色
hist_colorChange = input(true, title="MACD柱状图显示4种颜色？")

// 选择时间框架
res = useCurrentRes ? period : resCustom

// 设置MACD的默认参数
fastLength = input(19, title="快线长度", minval=1)       // 快速EMA的周期长度，从12改为19
slowLength = input(39, title="慢线长度", minval=1)       // 慢速EMA的周期长度，从26改为39
signalLength = input(9, title="信号长度", minval=1)      // 信号线的周期长度，保持为9

// 计算快速和慢速的指数移动平均线
fastMA = ema(source, fastLength)
slowMA = ema(source, slowLength)

// 计算MACD线
macd = fastMA - slowMA

// 计算信号线
signal = sma(macd, signalLength)

// 计算柱状图
hist = macd - signal

// 获取指定时间框架下的MACD、信号线和柱状图的值
outMacD = security(tickerid, res, macd)
outSignal = security(tickerid, res, signal)
outHist = security(tickerid, res, hist)

// 判断柱状图的增减和位置
histA_IsUp = outHist > outHist[1] and outHist > 0
histA_IsDown = outHist < outHist[1] and outHist > 0
histB_IsDown = outHist < outHist[1] and outHist <= 0
histB_IsUp = outHist > outHist[1] and outHist <= 0

// MACD 颜色定义，判断MACD线是否在信号线之上
macd_IsAbove = outMacD >= outSignal
macd_IsBelow = outMacD < outSignal

// 根据设置选择柱状图的颜色
plot_color = hist_colorChange ? 
             histA_IsUp ? aqua : 
             histA_IsDown ? blue : 
             histB_IsDown ? red : 
             histB_IsUp ? maroon : 
             yellow 
             : gray

// 根据设置选择MACD线的颜色
macd_color = macd_colorChange ? 
             (macd_IsAbove ? lime : red) 
             : red

// 根据设置选择信号线的颜色
signal_color = macd_colorChange ? 
               (macd_IsAbove ? yellow : yellow) 
               : lime

// 交叉点的Y轴位置设为信号线的值
circleYPosition = outSignal

// 绘制MACD线
plot(smd and outMacD ? outMacD : na, title="MACD", color=macd_color, linewidth=4)

// 绘制信号线
plot(smd and outSignal ? outSignal : na, title="信号线", color=signal_color, style=line, linewidth=2)

// 绘制柱状图
plot(sh and outHist ? outHist : na, title="柱状图", color=plot_color, style=histogram, linewidth=4)

// 绘制交叉点
plot(sd and cross(outMacD, outSignal) ? circleYPosition : na, title="交叉", style=circles, linewidth=4, color=macd_color)

// 绘制零线
hline(0, '零线', linestyle=solid, linewidth=2, color=black)
