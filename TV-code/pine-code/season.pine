//@version=5
indicator("季节性", overlay = false, max_boxes_count = 500)

//#region ———————————————————— 常量、输入和全局变量

// 工具提示
string TT_SY = "起始年份。方框绘制在此值后的下一年开始。"
string TT_PC = "用于显示正值的方框和表格单元格的基础颜色。"
string TT_NC = "用于显示负值的方框和表格单元格的基础颜色。"
string TT_CP = "最大颜色强度的阈值。绝对值等于或超过此水平时颜色保持不变。"
string TT_WT = "表格宽度占所在窗格的百分比。如果该值为0，表格的宽度将适应内容，且表格可能比窗格更宽。"
string TT_HG = "表格高度占所在窗格的百分比。如果该值为0，表格的高度将适应内容，且表格可能比窗格更高。"
string TT_SA = "切换显示“平均值”行，显示每个月的平均变化百分比。"
string TT_SD = "切换显示“标准差”行，显示每个月的百分比的标准差。"
string TT_SP = "切换显示“正变%”行，显示每个月列中正变化的百分比。"
string TT_SM = "指定要跳过的月份。按“YYYY-MM”格式书写，逗号及空格分隔。"
string TT_AT = "数值阈值 (%)"
string TT_ST = "标准差阈值 (%)"

// 起始年份设置
int startYearInput = input.int(2015, "起始年份", minval = 1800, tooltip = TT_SY, display = display.data_window)

// 颜色设置
string COLOR_GRP          = "颜色设置"
color  posColorInput      = color.new(input.color(#089981, "正值颜色", group = COLOR_GRP, tooltip = TT_PC), 0)
color  negColorInput      = color.new(input.color(#F23745, "负值颜色", group = COLOR_GRP, tooltip = TT_NC), 0)
int    cutoffPercentInput = input.int(10, "颜色强度截止值 (%)", group = COLOR_GRP, tooltip = TT_CP, display = display.none)

// 表格设置
string HEATMAP_GRP          = "热图设置"
string tablePositionInput   = input.string("中间", "表格位置", options = ["左", "中间", "右"], group = HEATMAP_GRP, display = display.none)
float  tableWidthInput      = input.float(100, "表格宽度 (%)", maxval = 100, minval = 0, group = HEATMAP_GRP, tooltip = TT_WT,  display = display.none)
float  tableHeightInput     = input.float(95, "表格高度 (%)", maxval = 100, minval = 0, group = HEATMAP_GRP, tooltip = TT_HG,  display = display.none)
bool   showAvgInput         = input.bool(true, "显示平均值", group = HEATMAP_GRP, tooltip = TT_SA)
bool   showStDevInput       = input.bool(true, "显示标准差", group = HEATMAP_GRP, tooltip = TT_SD)
bool   showPosInput         = input.bool(true, "显示正变百分比", group = HEATMAP_GRP, tooltip = TT_SP)
//@variable 控制热图底部指标行的显示方式。
bool showMetrics = showAvgInput or showStDevInput or showPosInput

//#region 新增选举周期设置
// 选举周期设置
string CYCLE_GRP = "选举周期设置"
string cycleOption = input.string("Elections", "选择周期", options=["Elections", "Post Elections", "Midterm Elections", "Pre Elections", "ALL"], group = CYCLE_GRP, tooltip="选择要分析的选举周期。")

// 附加设置
string ADD_GRP            = "附加设置"
string skippedMonthsInput = input.text_area("YYYY-MM, YYYY-MM", "忽略的月份", group = ADD_GRP, tooltip = TT_SM)

// 函数：判断年份是否属于选定的周期
isInSelectedCycle(int targetYear, string cycle) =>
    switch cycle
        "Elections" => targetYear % 4 == 0
        "Midterm Elections" => targetYear % 4 == 2
        "Post Elections" => (targetYear % 4) == 1
        "Pre Elections" => (targetYear % 4) == 3
        "ALL" => true  // 当选择 "ALL" 时，返回 true，不进行筛选
        => false

//#endregion

//@variable 一个整数数组，包含所有要忽略的月份，格式为“YYYYMM”。
var ignoredMonthsArray = array.new<int>()
if barstate.isfirst
    var ignoredStrArray = str.split(str.replace_all(skippedMonthsInput, " ", ""), ",")
    var ignoredIntArray = array.new<int>()
    for item in ignoredStrArray
        num = str.tonumber(str.replace_all(item, "-", ""))
        ignoredIntArray.push(math.round(num))
    ignoredMonthsArray := ignoredIntArray

//@variable 收盘时的当前年份。
int currYear = year(time_close)
//@variable 收盘时的当前月份。
int currMonth = month(time_close)
//#endregion


//#region ———————————————————— 函数和方法

//@function 计算方框和热图单元格使用的颜色。
calcColor(float value, int topTranspValue = na) =>
    color naColor     = color.gray
    float heavyTransp = 50
    float lightTransp = 90
    color heavyColor  = color.new(value >= 0 ? posColorInput : negColorInput, heavyTransp)
    color lightColor  = color.new(value >= 0 ? posColorInput : negColorInput, lightTransp)
    color baseColor   = na(value) ? naColor : value >= 0 ? posColorInput : negColorInput
    color transpColor = color.from_gradient(math.abs(value), 0, topTranspValue, lightColor, heavyColor)
    color result      = na(topTranspValue) ? baseColor : transpColor

//@function 返回`source`的一bar变化的百分比。
changePercent(float source) => 100.0 * (source - source[1]) / source[1]

//@function 返回数组中非NA值的数量。
method nonNA(array<float> this) =>
    int result = 0
    for item in this
        if not na(item)
            result += 1
    result

//@function 返回数组中正值的非NA值的百分比。
method percentPositive(array<float> this) =>
    int nonNA = 0
    int pos   = 0
    for item in this
        if not na(item)
            nonNA += 1
            if item >= 0
                pos += 1
    float result = 100.0 * pos / nonNA

//@function 按指定年份计算月度变化矩阵。过滤只保留符合选举周期的年份。
calculateMontlyChanges(int startYear) =>
    var matrix<float> dataMatrix = matrix.new<float>(0, 13) // Column 0 is unused for data but aligns with 1-12 month indexing.
    var array<int> yearIndexArray = array.new<int>()    // Stores the actual years corresponding to matrix rows

    // `time` in request.security("1M") refers to the open time of each 1-month historical bar.
    int barYear = year(time)      // Year of the 1M bar's open time
    int barMonth = month(time)    // Month of the 1M bar's open time
    float barChangePercent = na   // Declare barChangePercent here, initially as na

    // Only proceed with calculations if the bar's date is not in the future
    if barYear < year(timenow) or (barYear == year(timenow) and barMonth <= month(timenow))
        // Main logic for processing historical/current bars:
        barChangePercent := changePercent(close) // Assign value here

        // Filter conditions:
        // 1. The bar's year must be the startYear or later.
        // 2. The bar's month/year must not be in the user's list of skipped months.
        // 3. The bar's year must be in the selected election cycle.
        if barYear >= startYear and 
           not ignoredMonthsArray.includes(barYear * 100 + barMonth) and 
           isInSelectedCycle(barYear, cycleOption)

            // Manually find the index of barYear in yearIndexArray
            int yearRowIndex = -1
            if array.size(yearIndexArray) > 0 // Check if the array is not empty
                for i = 0 to array.size(yearIndexArray) - 1
                    if array.get(yearIndexArray, i) == barYear
                        yearRowIndex := i
                        break
            
            if yearRowIndex == -1 // If barYear is not yet in yearIndexArray (it's a new year for the matrix)
                // Add a new row to dataMatrix for this barYear.
                dataMatrix.add_row()
                // Add the new barYear to our index.
                array.push(yearIndexArray, barYear)
                // The new row is at the end of the matrix.
                yearRowIndex := array.size(yearIndexArray) - 1 // Corrected assignment operator
            
            // Set the calculated percentage change for barMonth 
            // in the row corresponding to barYear (yearRowIndex) and column barMonth.
            // Note: matrix.set requires row index to be valid.
            if yearRowIndex >= 0 and yearRowIndex < dataMatrix.rows() 
                dataMatrix.set(yearRowIndex, barMonth, barChangePercent)
    
    // Always return the persisted arrays; they will be empty or partially filled if future bars are skipped.
    [yearIndexArray, dataMatrix]

//@function 返回当前月的实时涨跌幅
getCurrentMonthChange() =>
    float currentChange = changePercent(close)
    [currentChange]
//#endregion


//#region ———————————————————— 主要计算和输出

// 获取实时月度数据
[currentChange] = request.security(syminfo.tickerid, "1M", getCurrentMonthChange(), lookahead = barmerge.lookahead_on)

// 包含年份索引和月度变化的元组（不包含当前月）
[yearIndexArray, changesMatrix] = request.security(syminfo.tickerid, "1M", calculateMontlyChanges(startYearInput), lookahead = barmerge.lookahead_on)

// 方框绘制和绘图计算
var float currMonthAverage       = na
var float currMonthStDev         = na
var float currAvgNumberOfMonths  = na
var float currMonthExpectedPrice = na
if timeframe.change("1M") and not na(changesMatrix)
    currMonthAverage       := changesMatrix.col(currMonth).avg()
    currMonthStDev         := changesMatrix.col(currMonth).stdev(false)
    currAvgNumberOfMonths  := changesMatrix.col(currMonth).nonNA()
    currMonthExpectedPrice := close[1] + close[1] * currMonthAverage / 100
    // 绘制新的月度预测框。
    box.new(
         left          = time,
         top           = currMonthExpectedPrice,
         right         = time_close("1M"),
         bottom        = close[1],
         xloc          = xloc.bar_time,
         bgcolor       = calcColor(currMonthAverage, cutoffPercentInput),
         border_color  = calcColor(currMonthAverage),
         text          = str.tostring(currMonthAverage, format.percent),
         text_color    = color.new(chart.fg_color, 60),
         border_style  = line.style_dashed,
         force_overlay = true
     )

//@variable 除`currAvgNumberOfMonths`外的系列图显示位置。
displayLoc = display.data_window + display.status_line 
// 绘制`currMonthExpectedPrice`、`currMonthAverage`和`currMonthStDev`在状态栏和数据窗口中。
plot(
     currMonthExpectedPrice, "当前月的预期价格", color = calcColor(currMonthAverage), 
     display = displayLoc
 )
plot(
     currMonthAverage, "当前月的历史平均值", color = calcColor(currMonthAverage), 
     display = displayLoc, format = format.percent
 )
plot(
     currMonthStDev, "当前月的历史标准差", color = color.gray, 
     display = displayLoc, precision = 2
 )
// 绘制`currAvgNumberOfMonths`在数据窗口中。
plot(currAvgNumberOfMonths, "当前平均中使用的月份数量", display = display.data_window, precision = 0)

//@variable 包含每个月缩写名称的数组。
var monthNames = array.from("年份", "一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月")

//@function 计算要在热图表中显示的总行数。
countRows(changesMatrix, showMetrics, showAvgInput, showStDevInput, showPosInput) =>
    totalRowCount = 1                       // 月份标题 
    totalRowCount += changesMatrix.rows()   // 年份数量
    if showMetrics
        totalRowCount += 1 // 指标分隔行
        totalRowCount += showAvgInput ? 1 : 0
        totalRowCount += showStDevInput ? 1 : 0
        totalRowCount += 1  // 中位数行
        totalRowCount += showPosInput ? 2 : 0  // 正变百分比行和趋势行
    totalRowCount

// 热图计算
if barstate.islast

    tablePosition = switch tablePositionInput
        "左" => position.bottom_left
        "中间" => position.bottom_center
        "右" => position.bottom_right

    // 计算总行数
    int totalRows = changesMatrix.rows() + 1  // 标题行
    if showMetrics
        totalRows += 1  // 分隔行
        totalRows += showAvgInput ? 1 : 0  // 平均值行
        totalRows += showStDevInput ? 1 : 0  // 标准差行
        totalRows += 1  // 中位数行
        totalRows += showPosInput ? 2 : 0  // 正变百分比行和趋势行

    table dataTable = table.new(tablePosition, 13, totalRows)
    color informerCellBgcolor = color.new(color.gray, 80)
    color textColor = chart.fg_color
    float cellHeight = tableHeightInput / totalRows
    float cellWidth = tableWidthInput / 13.0

    // 月份标题 
    for [index, item] in monthNames
        dataTable.cell(index, 0, item, bgcolor = informerCellBgcolor, text_color = textColor, height = cellHeight, width = cellWidth)
    // 月度变化数据单元格逻辑
    for [arrIndex, arr] in changesMatrix
        thisYear = yearIndexArray.get(arrIndex)
        for [itemIndex, item] in arr
            if itemIndex == 0
                dataTable.cell(
                         itemIndex, arrIndex + 1, str.tostring(thisYear), bgcolor = informerCellBgcolor, 
                         text_color = textColor, height = cellHeight, width = cellWidth
                     )
            else
                isSkipped = ignoredMonthsArray.includes(int(thisYear * 100 + itemIndex))
                cellText  = isSkipped ? "跳过" : str.tostring(item, format.percent)
                cellColor = isSkipped ? color.new(color.gray, 50) : calcColor(item, cutoffPercentInput)
                dataTable.cell(itemIndex, arrIndex + 1, cellText, bgcolor = cellColor, text_color = textColor, height = cellHeight, width = cellWidth)
    // 指标单元格逻辑
    if showMetrics
        // 显示指标的分隔行。
        dividerRow = changesMatrix.rows() + 1
        dataTable.cell(0, dividerRow, "", text_color = na, bgcolor = informerCellBgcolor, text_size = size.tiny, height = cellHeight, width = cellWidth)
        dataTable.merge_cells(0, dividerRow, 12, dividerRow)

        if showAvgInput
            // "平均值"数据单元格计算
            avgsRow = changesMatrix.rows() + 2
            dataTable.cell(0, avgsRow, "平均值:", bgcolor = informerCellBgcolor, text_color = textColor, height = cellHeight, width = cellWidth)
            for i = 1 to changesMatrix.columns() - 1
                avgValue = changesMatrix.col(i).avg()
                // 收集该月份的所有变化数据
                float[] monthChanges = array.new_float(0)
                for j = 0 to changesMatrix.rows() - 1
                    float value = changesMatrix.get(j, i)
                    // 排除当前年月的数据
                    int rowYear = yearIndexArray.get(j)
                    if not na(value) and (rowYear != currYear or i != currMonth)
                        array.push(monthChanges, value)
                float medianValue = array.size(monthChanges) > 0 ? array.median(monthChanges) : 0.0
                float stdevValue = changesMatrix.col(i).stdev(false)
                float posRatio = changesMatrix.col(i).percentPositive()
                
                dataTable.cell(
                     i, avgsRow, str.tostring(avgValue, format.percent), 
                     bgcolor = calcColor(avgValue, cutoffPercentInput), text_color = textColor, height = cellHeight, width = cellWidth
                 )

        // "中位数"数据单元格计算
        medianRow = changesMatrix.rows() + 3
        dataTable.cell(0, medianRow, "中位数:", bgcolor = informerCellBgcolor, text_color = textColor, height = cellHeight, width = cellWidth)
        for i = 1 to changesMatrix.columns() - 1
            float[] monthChanges = array.new_float(0)
            for j = 0 to changesMatrix.rows() - 1
                float value = changesMatrix.get(j, i)
                // 排除当前年月的数据
                int rowYear = yearIndexArray.get(j)
                if not na(value) and (rowYear != currYear or i != currMonth)
                    array.push(monthChanges, value)
            float medianValue = array.size(monthChanges) > 0 ? array.median(monthChanges) : 0.0
            dataTable.cell(
                 i, medianRow, str.tostring(medianValue, format.percent), 
                 bgcolor = calcColor(medianValue, cutoffPercentInput), text_color = textColor, height = cellHeight, width = cellWidth
             )

        // "标准差"数据单元格计算
        if showStDevInput
            stDevRow = changesMatrix.rows() + 4
            dataTable.cell(0, stDevRow, "标准差:", bgcolor = informerCellBgcolor, text_color = textColor, height = cellHeight, width = cellWidth)
            for i = 1 to changesMatrix.columns() - 1
                stdevValue = changesMatrix.col(i).stdev(false)
                dataTable.cell(
                     i, stDevRow, str.tostring(stdevValue, "##.##"), bgcolor = color.new(color.gray, 80), 
                     text_color = textColor, height = cellHeight, width = cellWidth
                 )

        // "涨概率"数据单元格计算
        if showPosInput
            ratioRow = changesMatrix.rows() + 5
            dataTable.cell(0, ratioRow, "涨概率:", bgcolor = informerCellBgcolor, text_color = textColor, height = cellHeight, width = cellWidth)
            for i = 1 to changesMatrix.columns() - 1
                float posRatio = changesMatrix.col(i).percentPositive()
                dataTable.cell(
                     i, ratioRow, str.tostring(posRatio, '#.#') + "%", bgcolor = calcColor(posRatio - 50, 50), 
                     text_color = textColor, height = cellHeight, width = cellWidth
                 )

            // "趋势"数据单元格计算
            trendRow = changesMatrix.rows() + 6
            dataTable.cell(0, trendRow, "趋势:", bgcolor = informerCellBgcolor, text_color = textColor, height = cellHeight, width = cellWidth)
            float maxAbsValue = 0.0
            float maxStdev = 0.0
            for col = 1 to changesMatrix.columns() - 1
                float avgVal = changesMatrix.col(col).avg()
                float[] monthVals = array.new_float(0)
                for row = 0 to changesMatrix.rows() - 1
                    float val = changesMatrix.get(row, col)
                    // 排除当前年月的数据
                    int rowYear = yearIndexArray.get(row)
                    if not na(val) and (rowYear != currYear or col != currMonth)
                        array.push(monthVals, val)
                float medianVal = array.size(monthVals) > 0 ? array.median(monthVals) : 0.0
                float stdevVal = changesMatrix.col(col).stdev(false)
                
                maxAbsValue := math.max(maxAbsValue, math.abs(avgVal))
                maxAbsValue := math.max(maxAbsValue, math.abs(medianVal))
                maxStdev := math.max(maxStdev, stdevVal)

            float valueThreshold = maxAbsValue
            float stdevThreshold = maxStdev

            for i = 1 to changesMatrix.columns() - 1
                float avgValue = changesMatrix.col(i).avg()
                // 收集该月份的所有变化数据
                float[] monthChanges = array.new_float(0)
                for j = 0 to changesMatrix.rows() - 1
                    float value = changesMatrix.get(j, i)
                    // 排除当前年月的数据
                    int rowYear = yearIndexArray.get(j)
                    if not na(value) and (rowYear != currYear or i != currMonth)
                        array.push(monthChanges, value)
                
                float medianValue = array.size(monthChanges) > 0 ? array.median(monthChanges) : 0.0
                float stdevValue = changesMatrix.col(i).stdev(false)
                float posRatio = changesMatrix.col(i).percentPositive()  
                
                // 计算趋势
                string symbol = "?"
                string trendText = ""
                float probability = 0.0
                
                if array.size(monthChanges) < 3  // 数据点太少
                    probability := 0.0
                else
                    // 1. 计算方向性指标
                    float posRatioScore = (posRatio - 50) / 25  // 归一化并放大效果
                    float avgScore = avgValue / valueThreshold  // 使用最大绝对值归一化
                    float medianScore = medianValue / valueThreshold  // 使用最大绝对值归一化
                    
                    // 2. 计算指标一致性
                    bool posRatioAgrees = (posRatio > 50 and avgValue > 0) or (posRatio < 50 and avgValue < 0)
                    bool medianAgrees = (avgValue > 0 and medianValue > 0) or (avgValue < 0 and medianValue < 0)
                    float consistencyScore = (posRatioAgrees ? 1.2 : 0.4) * (medianAgrees ? 1.2 : 0.4)
                    
                    // 3. 计算标准差影响因子
                    float stdevFactor = math.exp(-stdevValue / stdevThreshold * 2)  // 使用最大标准差归一化
                    
                    // 4. 计算综合方向分数
                    float directionScore = math.abs(posRatioScore * 0.35 + avgScore * 0.3 + medianScore * 0.35)
                    
                    // 5. 计算最终概率
                    probability := math.min(directionScore * consistencyScore * (0.6 + 0.4 * stdevFactor) * 100, 98)
                    
                    // 6. 确定方向
                    if probability >= 5
                        if ((posRatio > 52 and avgValue > 0) or (posRatio > 60))
                            symbol := "↑"
                        else if ((posRatio < 48 and avgValue < 0) or (posRatio < 40))
                            symbol := "↓"
                        else
                            symbol := "?"
                            probability := 5
                    else
                        symbol := "?"
                        probability := 5
                
                trendText := str.tostring(math.round(probability), "#") + "% " + symbol
                
                dataTable.cell(
                     i, trendRow, trendText,
                     bgcolor = symbol == "↑" ? posColorInput : symbol == "↓" ? negColorInput : color.new(color.gray, 50),
                     text_color = textColor, height = cellHeight, width = cellWidth
                 )

    // 在表格中显示实时月份数据
    if currYear >= startYearInput and isInSelectedCycle(currYear, cycleOption)
        int currentMonthCol = currMonth
        float value = currentChange
        if not na(value)
            // 找到当前年份对应的行
            int currentYearRow = 0
            for i = 0 to yearIndexArray.size() - 1
                if yearIndexArray.get(i) == currYear
                    currentYearRow := i + 1  // +1 是因为第一行是月份标题
            dataTable.cell(currentMonthCol, currentYearRow, "▶" + str.tostring(value, format.percent), text_color = value >= 0 ? posColorInput : negColorInput, bgcolor = calcColor(value, cutoffPercentInput), text_size = size.normal, height = cellHeight, width = cellWidth)
