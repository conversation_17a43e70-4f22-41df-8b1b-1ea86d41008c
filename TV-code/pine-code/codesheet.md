# MCSI代码逻辑说明

## 1. RSI评分逻辑
### 日线信号计算 (dailyScore)
1. 突破确认信号：
   - 下轨突破上升：+67分
   - 上轨突破下降：-67分

2. 轨道外信号：
   - 下轨下方上升：+27分
   - 下轨下方其他：+13分
   - 上轨上方下降：-27分
   - 上轨上方其他：-13分

### 周线信号计算 (weeklyScore)
1. 突破确认信号：
   - 下轨突破上升：+67分
   - 上轨突破下降：-67分

2. 轨道外信号：
   - 下轨下方：+33分
   - 上轨上方：-33分

### 信号持续时间
- 所有信号触发后保持2个周期
- 持续期间保持原有分数
- 持续结束且无新信号时重置为0

## 2. MACD评分逻辑
### 动态阈值计算
- 使用lookbackPeriod周期的标准差
- 动态阈值 = 标准差 * 1.5

### 评分计算
1. 阈值内信号：
   - 分数 = 0

2. 高位转向下降：
   - 基础分 = -50
   - 额外分 = min(50, (|hist/threshold| - 1) * 25)
   - 最终分 = -(基础分 + 额外分)

3. 低位转向上升：
   - 基础分 = +50
   - 额外分 = min(50, (|hist/threshold| - 1) * 25)
   - 最终分 = 基础分 + 额外分

## 3. TD9分逻辑
### 计数评分
- 计数1-6：0分
- 计数7：±20分
- 计数8：±50分
- 计数9：±100分
- 计数10-12：±80分
- 计数13-16：±100分
- 计数>16：重置为0

## 4. SMC评分逻辑
### 条件判断
1. 买入信号：
   - RSI < 30 且 MFI < 20
   - 分数 = +100

2. 卖出信号：
   - RSI > 70 且 MFI > 80
   - 分数 = -100

3. 权重调整：
   - 有信号时使用设定权重
   - 无信号时权重为0

## 5. MMT评分逻辑
### 轨道信号（50%权重）
- 轨道分数（50%权重）：
  * 突破上轨下穿：-100分
  * 突破下轨上穿：+100分
  * 上轨上方运行：动量下降时-80分，其他-20分
  * 下轨下方运行：动量上升时+80分，其他+20分
  * 信号持续2个周期
- 背离分数（50%权重）：
  * 看跌背离：-100分
  * 看涨背离：+100分
  * 隐藏看跌背离：-70分
  * 隐藏看涨背离：+70分
  * 信号持续1个周期

### 参数设置
1. cyclicMemory：34（周期长度）
2. leveling：10（轨道平滑度）
3. 背离检测：
   - 右侧确认：2周期
   - 左侧回溯：5周期

## 6. 多时间框架处理
### 支持的时间框架
- 15分钟、1小时、4小时、日线、周线、月线
- 每个时间框架可以独立选择是否显示
- 只计算选中的时间框架数据，提高性能

### 数据计算
- 使用request.security获取不同时间框架的综合得分
- 只在barstate.islast时更新数据
- 使用na值初始化，避免不必要的计算

## 7. 综合评分计算
- combinedScore = rsiScore * rsiWeight + 
                  td9Score * td9Weight + 
                  macdScore * macdWeight + 
                  smcScore * dynamicSmcWeight +
                  mmtScore * mmtWeight

## 8. 显示效果
### 分数线显示
- MACD：蓝色细线（50%透明度）
- RSI：红色细线（50%透明度）
- TD9：绿色细线（50%透明度）
- SMC：橙色细线（50%透明度）
- MMT：青色细线（50%透明度）
- 综合得分：渐变色粗线

### 颜色渐变逻辑
- 正分（买入）显示绿色
- 负分（卖出）显示红色
- 零分附近显示黄色
- 使用三色渐变：绿-黄-红

### 表格显示
- 多时间框架表格（右下角）：
  * 动态列数：根据选中的时间框架数量调整
  * 2行：表头和综合得分
  * 最多7列：时间框架名称 + 6个时间框架数据
  * 只显示选中的时间框架数据
  * 综合得分使用对应的颜色显示
  * 使用#.##格式保留两位小数

## 9. 输入参数设置
### 权重设置
- RSI权重：0.8（默认）
- TD9权重：0.5（默认）
- MACD权重：0.8（默认）
- SMC权重：1.0（默认）
- MMT权重：0.8（默认）

### 时间框架显示设置
- 可选时间框架：15分钟、1小时、4小时、日线、周线、月线
- 每个时间框架都可以独立开关
- 默认全部启用

