// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © Electrified (electrifiedtrading)
// @version=4
study(title="Auto AVWAP (Anchored-VWAP)", shorttitle="Auto AVWAP", overlay=true, format=format.price, precision=2, resolution="") // image v3

kcolor = #0094FF
dcolor = #FF6A00
WMA = "WMA", EMA = "EMA", SMA = "SMA", VWMA = "VWMA", VAWMA = "VAWMA"

///////////////////////////////////////////////////
// Input

useHiLow    = input(true, "Use High/Low instead of HLC3", group="Anchored VWAP", tooltip="When true, high and low values are used to caclulate the value of each AVWAP instead of HLC3.")
useOpen     = input(true, "Use open instead of close for alerts.", group="Anchored VWAP", tooltip="Using the open instead of the close is more confirmative of a breakout as live values are based upon the close value and could be transient.")

k_mode      = input(WMA, "K Mode", group="Stochastic RSI", inline="Source", options=[SMA, EMA, WMA, VWMA, VAWMA])
src         = input(hlc3, "Source", group="Stochastic RSI", inline="Source")
smoothK     = input(4, "K", group="Stochastic RSI", inline="Values", minval=1)
smoothD     = input(4, "D", group="Stochastic RSI", inline="Values", minval=1)


lengthRSI   = input(64, "RSI", group="Lengths", inline="Lengths", minval=1)
lengthStoch = input(48, "Stochastic", group="Lengths", inline="Lengths", minval=1)


lowerBand   = input(20, "Lower", group="Band", inline="Band", maxval=50, minval=0)
upperBand   = input(80, "Upper", group="Band", inline="Band", minval=50, maxval=100)

lowerReversal   = input(20, "Lower", group="Reversal", inline="Reversal", maxval=100, minval=0, tooltip="The level that if broken (down) signfies a reversal has begun/occured.\nDefault represents the lower band.")
upperReversal   = input(80, "Upper", group="Reversal", inline="Reversal", minval=0, maxval=100, tooltip="The level that if broken (up) signfies a reversal has begun/occured.\nDefault represents the upper band.")

///////////////////////////////////////////////////
// Functions
vawma(src, len) =>
    sum = 0.0
    vol = 0.0
    for m = 1 to len // m = triangular multiple
        i = len - m
        v = volume[i] * m
        vol := vol + v
        sum := sum + src[i] * v
    sum/vol
////

getMA(series, mode, len) =>
    mode==WMA ? wma(series, len) :
      mode==EMA ? ema(series, len) :
      mode==VWMA ? vwma(series, len) :
      mode==VAWMA ? vawma(series, len) :
      sma(series, len)
////

///////////////////////////////////////////////////
// Calculation
rsi1 = rsi(src, lengthRSI)
stoch = stoch(rsi1, rsi1, rsi1, lengthStoch)
k = getMA(stoch, k_mode, smoothK)
d = sma(k, smoothD)
k_c = change(k)
d_c = change(d)
kd = k - d

var hi = high
var lo = low
var phi = high
var plo = low
var state = 0

var float hiAVWAP_s = 0
var float loAVWAP_s = 0
var float hiAVWAP_v = 0
var float loAVWAP_v = 0
var float hiAVWAP_s_next = 0
var float loAVWAP_s_next = 0
var float hiAVWAP_v_next = 0
var float loAVWAP_v_next = 0

if(d<lowerBand or high>phi)
    phi := high
    hiAVWAP_s_next := 0
    hiAVWAP_v_next := 0
if(d>upperBand or low<plo)
    plo := low
    loAVWAP_s_next := 0
    loAVWAP_v_next := 0

if(high>hi)
    hi := high
    hiAVWAP_s := 0
    hiAVWAP_v := 0    
if(low<lo)
    lo := low
    loAVWAP_s := 0
    loAVWAP_v := 0

vwapHi = useHiLow ? high : hlc3
vwapLo = useHiLow ? low : hlc3
    
hiAVWAP_s += vwapHi * volume
loAVWAP_s += vwapLo * volume
hiAVWAP_v += volume
loAVWAP_v += volume
hiAVWAP_s_next += vwapHi * volume
loAVWAP_s_next += vwapLo * volume
hiAVWAP_v_next += volume
loAVWAP_v_next += volume


if(state!=-1 and d<lowerBand)
    state := -1
else if(state!=+1 and d>upperBand)
    state := +1

if(hi>phi and state==+1 and k<d and k<lowerReversal)
    hi := phi
    hiAVWAP_s := hiAVWAP_s_next
    hiAVWAP_v := hiAVWAP_v_next
if(lo<plo and state==-1 and k>d and k>upperReversal)
    lo := plo
    loAVWAP_s := loAVWAP_s_next
    loAVWAP_v := loAVWAP_v_next
    
    
hiAVWAP = hiAVWAP_s / hiAVWAP_v
loAVWAP = loAVWAP_s / loAVWAP_v
hiAVWAP_next = hiAVWAP_s_next / hiAVWAP_v_next
loAVWAP_next = loAVWAP_s_next / loAVWAP_v_next
    
plot(hiAVWAP_next, "High Next",color.new(color.red, 75), 1, style=plot.style_circles)
plot(loAVWAP_next, "Low Next", color.new(color.green, 75), 1, style=plot.style_circles)
plot(hiAVWAP, "High",color.new(color.red, 50), 2, style=plot.style_circles)
plot(loAVWAP, "Low", color.new(color.green, 50), 2, style=plot.style_circles)

alertValue = useOpen ? open : close
resistance = alertValue - hiAVWAP
support = alertValue - loAVWAP

alertcondition(resistance>0 or support<0, title="Breakout (▲▼)", message="Breakout ({{ticker}} {{interval}})")
alertcondition(resistance>0 , title="Resistance Broken ▲", message="Resistance Broken ▲ ({{ticker}} {{interval}})")
alertcondition(support<0 , title="Support Broken ▼", message="Support Broken ▼ ({{ticker}} {{interval}})")
