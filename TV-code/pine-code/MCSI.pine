//@version=5
indicator("MCSI - 多指标综合评分", overlay=false)

// === 输入参数 ===
// 历史长度限制
maxBarsBack = input.int(title="历史K线数量限制", defval=500, options=[50, 100, 200, 500, 1000], tooltip="限制指标计算的历史K线数量，数值越小加载越快", group="性能优化")

rsiWeight = input.float(0.8, "RSI权重", 0, 1, 0.1), td9Weight = input.float(0.5, "TD9权重", 0, 1, 0.1), macdWeight = input.float(0.8, "MACD权重", 0, 1, 0.1), smcWeight = input.float(1.0, "SMC权重", 0, 1, 0.1), mmtWeight = input.float(0.8, "MMT权重", 0, 1, 0.1)
fastLength = input.int(19, "MACD快线"), slowLength = input.int(39, "MACD慢线"), signalLength = input.int(9, "MACD信号"), lookbackPeriod = input.int(20, "MACD阈值周期")
domcycle = input.int(14, "RSI周期", 10), rsiLength = input.int(14, "SMC RSI周期"), mfiLength = input.int(14, "SMC MFI周期")

// 添加时间框架显示选项
show_15m = input.bool(true, "显示15分钟", group="时间框架显示")
show_1h = input.bool(true, "显示1小时", group="时间框架显示") 
show_4h = input.bool(true, "显示4小时", group="时间框架显示")
show_1d = input.bool(true, "显示日线", group="时间框架显示")
show_1w = input.bool(true, "显示周线", group="时间框架显示")
show_1M = input.bool(true, "显示月线", group="时间框架显示")

// === 变量初始化 ===
// 历史数据限制
var int bar_count = 0
bar_count := bar_count + 1
use_bar = bar_count <= maxBarsBack

var float macdScore = 0.0, var float rsiScore = 0.0, var float td9Score = 0.0, var float smcScore = 0.0, var float dynamicSmcWeight = 0.0, var float mmtScore = 0.0
var int TD = 0, var int TS = 0, var float dailyScore = 0.0, var float weeklyScore = 0.0
var int signalDuration = 0, var int weeklySignalDuration = 0

// === RSI计算 ===
calculate_crsi(src) =>
    if not use_bar
        [float(na), float(na), float(na)]
    else
        cyclelen = domcycle / 2
        vibration = 10
        torque = 2.0 / (vibration + 1)
        phasingLag = (vibration - 1) / 2.0
        leveling = 10.0
        cyclicmemory = domcycle * 2
        var crsi = 0.0
        var lmax = -999999.0
        var lmin = 999999.0
        var db = 0.0
        var ub = 0.0
        up = ta.rma(math.max(ta.change(src), 0), cyclelen)
        down = ta.rma(-math.min(ta.change(src), 0), cyclelen)
        rsi = down == 0 ? 100 : up == 0 ? 0 : 100 - 100 / (1 + up / down)
        crsi := torque * (2 * rsi - rsi[phasingLag]) + (1 - torque) * nz(crsi[1], 0)
        lmax := math.max(nz(crsi, lmax), lmax)
        lmin := math.min(nz(crsi, lmin), lmin)
        mstep = (lmax - lmin) / 100
        aperc = leveling / 100
        for steps = 0 to 100
            testvalue = lmin + mstep * steps
            below = 0.0
            for i = 0 to cyclicmemory - 1
                below := below + (crsi[i] < testvalue ? 1 : 0)
            if below / cyclicmemory >= aperc
                db := testvalue
                break
        for steps = 0 to 100
            testvalue = lmax - mstep * steps
            above = 0.0
            for i = 0 to cyclicmemory - 1
                above := above + (crsi[i] >= testvalue ? 1 : 0)
            if above / cyclicmemory >= aperc
                ub := testvalue
                break
        [crsi, db, ub]

// === 指标计算 ===
[crsi_daily, db_daily, ub_daily] = calculate_crsi(close)
[crsi_weekly, db_weekly, ub_weekly] = request.security(syminfo.tickerid, "W", calculate_crsi(close))
[macdLine, signalLine, histLine] = ta.macd(close, fastLength, slowLength, signalLength)
histStdDev = ta.stdev(histLine, lookbackPeriod), dynamicThreshold = histStdDev * 1.5

// === TD9计算 ===
TD := close > close[4] ? nz(TD[1])+1 : 0, TS := close < close[4] ? nz(TS[1])+1 : 0
TDUp = TD - ta.valuewhen(TD < TD[1], TD, 1), TDDn = TS - ta.valuewhen(TS < TS[1], TS, 1)
getTD9Score(count) => count == 0 or count <= 6 ? 0.0 : count == 7 ? 20.0 : count == 8 ? 50.0 : count == 9 or (count >= 13 and count <= 16) ? 100.0 : count >= 10 and count <= 12 ? 80.0 : 0.0
td9Score := TDDn > 0 ? getTD9Score(TDDn) : -getTD9Score(TDUp)

// === MACD状态判断 ===
histA_IsUp = histLine > histLine[1] and histLine > 0, histA_IsDown = histLine < histLine[1] and histLine > 0
histB_IsDown = histLine < histLine[1] and histLine <= 0, histB_IsUp = histLine > histLine[1] and histLine <= 0
histColorChanged = (histA_IsUp[1] and not histA_IsUp) or (histA_IsDown[1] and not histA_IsDown) or (histB_IsDown[1] and not histB_IsDown) or (histB_IsUp[1] and not histB_IsUp)

// === SMC计算 ===
rsi_smc = ta.rsi(close, rsiLength)
typicalPrice = (high + low + close) / 3, moneyFlow = typicalPrice * volume
posMF = typicalPrice > nz(typicalPrice[1]) ? moneyFlow : 0, negMF = typicalPrice < nz(typicalPrice[1]) ? moneyFlow : 0
mfPositive = ta.sma(posMF, mfiLength) * mfiLength, mfNegative = ta.sma(negMF, mfiLength) * mfiLength
mfi = (mfNegative == 0 and mfPositive == 0) ? 50 : 100 - (100 / (1 + mfPositive / mfNegative))
retailSentiment = rsi_smc > 70 ? -1 : (rsi_smc < 30 ? 1 : 0)

// === 评分计算 ===
// RSI评分计算
// 日线信号分数计算
if crsi_daily[1] < db_daily[1] and crsi_daily > db_daily[1] and crsi_daily > crsi_daily[1]
    dailyScore := 67, signalDuration := 2
else if crsi_daily[1] > ub_daily[1] and crsi_daily < ub_daily[1] and crsi_daily < crsi_daily[1]
    dailyScore := -67, signalDuration := 2
else if crsi_daily < db_daily
    dailyScore := crsi_daily > crsi_daily[1] ? 27 : 13, signalDuration := 2
else if crsi_daily > ub_daily
    dailyScore := crsi_daily < crsi_daily[1] ? -27 : -13, signalDuration := 2
else if signalDuration > 0
    signalDuration := signalDuration - 1
else
    dailyScore := 0

// 周线信号分数计算
if crsi_weekly[1] < db_weekly[1] and crsi_weekly > db_weekly[1] and crsi_weekly > crsi_weekly[1]
    weeklyScore := 67, weeklySignalDuration := 2
else if crsi_weekly[1] > ub_weekly[1] and crsi_weekly < ub_weekly[1] and crsi_weekly < crsi_weekly[1]
    weeklyScore := -67, weeklySignalDuration := 2
else if crsi_weekly < db_weekly
    weeklyScore := crsi_weekly > crsi_weekly[1] ? 33 : 33, weeklySignalDuration := 2
else if crsi_weekly > ub_weekly
    weeklyScore := crsi_weekly < crsi_weekly[1] ? -33 : -33, weeklySignalDuration := 2
else if weeklySignalDuration > 0
    weeklySignalDuration := weeklySignalDuration - 1
else
    weeklyScore := 0

// 计算RSI最终分数
rsiScore := math.max(-100, math.min(100, dailyScore + weeklyScore))

// MACD评分
if math.abs(histLine) <= dynamicThreshold
    macdScore := 0
else if histLine > dynamicThreshold and histA_IsUp[1] and histA_IsDown
    macdScore := -(50 + math.min(50, (math.abs(histLine / dynamicThreshold) - 1) * 25))
else if histLine < -dynamicThreshold and histB_IsDown[1] and histB_IsUp
    macdScore := 50 + math.min(50, (math.abs(histLine / dynamicThreshold) - 1) * 25)
else if histColorChanged
    macdScore := 0
else
    macdScore := macdScore[1]

// SMC评分
smcScore := retailSentiment == 1 and mfi < 20 ? 100 : retailSentiment == -1 and mfi > 80 ? -100 : 0
dynamicSmcWeight := smcScore == 0 ? 0.0 : smcWeight

// === 综合评分 ===
// 获取各指标的有效权重
effectiveRsiWeight = rsiWeight
effectiveTd9Weight = td9Weight
effectiveMacdWeight = macdWeight
effectiveSmcWeight = smcScore == 0 ? 0.0 : smcWeight
effectiveMmtWeight = mmtWeight

// 直接计算加权和，不再除以总权重
combinedScore = rsiScore * effectiveRsiWeight + td9Score * effectiveTd9Weight + macdScore * effectiveMacdWeight + smcScore * effectiveSmcWeight + mmtScore * effectiveMmtWeight

// === 显示效果 ===
getScoreColor(score) => 
    normalizedScore = math.min(math.max(score, -100), 100), scoreRatio = (-normalizedScore + 100) / 200, r1 = #FF0000, r2 = #FFFF00, r3 = #00FF00, result = scoreRatio <= 0.5 ? color.from_gradient(scoreRatio * 2, bottom_value=0, top_value=1, bottom_color=r3, top_color=r2) : color.from_gradient((scoreRatio - 0.5) * 2, bottom_value=0, top_value=1, bottom_color=r2, top_color=r1)

plot(macdScore, "MACD", color.new(color.blue, 50), 1), plot(rsiScore, "RSI", color.new(color.red, 50), 1), plot(td9Score, "TD9", color.new(color.green, 50), 1), plot(smcScore, "SMC", color.new(color.orange, 50), 1), plot(mmtScore, "MMT", color.new(color.aqua, 50), 1), plot(combinedScore, "综合得分", getScoreColor(combinedScore), 3)
bgcolor(color.new(color.gray, 90))
hline(-100, "下限", color=color.gray)
hline(0, "零线", color=color.new(color.white, 50), linestyle=hline.style_dashed)
hline(100, "上限", color=color.gray)

// === MMT参数设置 ===
var leveling = 10
var cyclicMemory = 34
var channelWeight = 0.5  // 轨道分数权重50%
var divergenceWeight = 0.5  // 背离分数权重50%

// === MMT计算函数 ===
Cycle1(i, waveThrottle, cycs) =>
    ret = 6.0 * waveThrottle + 1.0
    if (i == 0)
        ret := 1.0 + waveThrottle
    else if (i == 1)
        ret := 1.0 + waveThrottle * 5.0
    else if (i == (cycs-1))
        ret := 1.0 + waveThrottle
    else if (i == (cycs-2))
        ret := 1.0 + waveThrottle * 5.0
    ret

Cycle2(i, waveThrottle, cycs) =>
    ret = -4.0 * waveThrottle
    if (i == 0)
        ret := -2.0 * waveThrottle
    else if (i == (cycs-1))
        ret := 0.0
    else if (i == (cycs-2))
        ret := -2.0 * waveThrottle
    ret

Cycle3(i, waveThrottle, cycs) =>
    ret = waveThrottle
    if (i == (cycs-1))
        ret := 0.0
    else if (i == (cycs-2))
        ret := 0.0
    ret

iWTT_CSI_processor(CycleCount) =>
    wtt1=0.0, wtt2=0.0, wtt3=0.0, wtt4=0.0, wtt5=0.0
    _wtt1=0.0, _wtt2=0.0, _wtt3=0.0, _wtt4=0.0, _wtt5=0.0
    momentum=0.0, acceleration=0.0, swing=0.0
    cycs=50
    waveThrottle=float(160*CycleCount)
    currentVal=0.0  
    
    for i = 0 to cycs - 1
        swing:=Cycle1(i,waveThrottle,cycs)-wtt4*wtt1-_wtt5*_wtt2
        if(swing==0) 
            break
        momentum := Cycle2(i,waveThrottle,cycs)
        _wtt1 := wtt1
        wtt1 := (momentum-wtt4*wtt2)/swing

        acceleration:=Cycle3(i,waveThrottle,cycs)
        _wtt2 := wtt2
        wtt2 := acceleration/swing
 
        currentVal:=(close[49-i]-_wtt3*_wtt5-wtt3*wtt4)/swing
        _wtt3  := wtt3
        wtt3   := currentVal
        wtt4   := momentum-wtt5*_wtt1
        _wtt5  := wtt5
        wtt5   := acceleration

    currentVal

banding(CRSI, Period, Leveling) =>
    percent = Leveling / 100.0
    periodMinusOne = Period-1
    maxima = -999999.0
    minima =  999999.0
    for i=0 to periodMinusOne
        crsi = nz(CRSI[i])
        if crsi > maxima
            maxima := crsi
        else if crsi < minima
            minima := crsi
    stepFactor = (maxima - minima) / 100.0
    float lowBand = na
    for steps=0 to 100
        testValue = minima + stepFactor * steps
        below = 0
        for m=0 to periodMinusOne
            if CRSI[m] < testValue
                below := below + 1
        if below/Period >= percent
            lowBand := testValue
            break
    float highBand = na
    for steps=0 to 100
        testValue = maxima - stepFactor * steps
        above = 0
        for m=0 to periodMinusOne
            if CRSI[m] >= testValue
                above := above + 1
        if above/Period >= percent
            highBand := testValue
            break
    [highBand, lowBand]

// === MMT计算 ===
thrust1=iWTT_CSI_processor(1)
thrust2=iWTT_CSI_processor(10)
CSIBuffer = thrust1-thrust2

[mmtHighBand, mmtLowBand] = banding(CSIBuffer, cyclicMemory, leveling)

// === MMT动量计算 ===
mmtMomentum = CSIBuffer - CSIBuffer[1]
mmtStdDev = ta.stdev(CSIBuffer, cyclicMemory)

// === MMT背离检测 ===
// 背离检测参数
mmtLbR = 2  // 右侧确认周期
mmtLbL = 5  // 左侧回溯周期
mmtRangeUpper = 60
mmtRangeLower = 5

// 背离检测函数
_mmtInRange(cond) =>
    bars = ta.barssince(cond == true)
    mmtRangeLower <= bars and bars <= mmtRangeUpper

// 寻找枢轴点
mmtPlFound = na(ta.pivotlow(CSIBuffer, mmtLbL, mmtLbR)) ? false : true
mmtPhFound = na(ta.pivothigh(CSIBuffer, mmtLbL, mmtLbR)) ? false : true

// 背离条件计算
mmtOscHL = CSIBuffer[mmtLbR] > ta.valuewhen(mmtPlFound, CSIBuffer[mmtLbR], 1) and _mmtInRange(mmtPlFound[1])
mmtPriceLL = low[mmtLbR] < ta.valuewhen(mmtPlFound, low[mmtLbR], 1)
mmtBullDiv = mmtPriceLL and mmtOscHL and mmtPlFound

mmtOscLH = CSIBuffer[mmtLbR] < ta.valuewhen(mmtPhFound, CSIBuffer[mmtLbR], 1) and _mmtInRange(mmtPhFound[1])
mmtPriceHH = high[mmtLbR] > ta.valuewhen(mmtPhFound, high[mmtLbR], 1)
mmtBearDiv = mmtPriceHH and mmtOscLH and mmtPhFound

mmtOscLL = CSIBuffer[mmtLbR] < ta.valuewhen(mmtPlFound, CSIBuffer[mmtLbR], 1) and _mmtInRange(mmtPlFound[1])
mmtPriceHL = low[mmtLbR] > ta.valuewhen(mmtPlFound, low[mmtLbR], 1)
mmtHiddenBullDiv = mmtPriceHL and mmtOscLL and mmtPlFound

mmtOscHH = CSIBuffer[mmtLbR] > ta.valuewhen(mmtPhFound, CSIBuffer[mmtLbR], 1) and _mmtInRange(mmtPhFound[1])
mmtPriceLH = high[mmtLbR] < ta.valuewhen(mmtPhFound, high[mmtLbR], 1)
mmtHiddenBearDiv = mmtPriceLH and mmtOscHH and mmtPhFound

// === MMT评分计算 ===
var float mmtChannelScore = 0.0
var float mmtDivergenceScore = 0.0
var int mmtChannelSignalDuration = 0
var int mmtDivergenceSignalDuration = 0
var float mmtLastChannelScore = 0.0
var float mmtLastDivergenceScore = 0.0

// 轨道位置分数计算（50%权重）
if CSIBuffer[1] > mmtHighBand[1] and CSIBuffer < mmtHighBand
    mmtChannelScore := -100
    mmtLastChannelScore := -100
    mmtChannelSignalDuration := 2
else if CSIBuffer[1] < mmtLowBand[1] and CSIBuffer > mmtLowBand
    mmtChannelScore := 100
    mmtLastChannelScore := 100
    mmtChannelSignalDuration := 2
else if CSIBuffer > mmtHighBand
    mmtChannelScore := mmtMomentum < 0 ? -80 : -20
    mmtLastChannelScore := mmtChannelScore
    mmtChannelSignalDuration := 2
else if CSIBuffer < mmtLowBand
    mmtChannelScore := mmtMomentum > 0 ? 80 : 20
    mmtLastChannelScore := mmtChannelScore
    mmtChannelSignalDuration := 2
else
    if mmtChannelSignalDuration > 0
        mmtChannelSignalDuration := mmtChannelSignalDuration - 1
        mmtChannelScore := mmtLastChannelScore
    else
        mmtChannelScore := 0

// 背离分数计算（50%权重）
if mmtBearDiv
    mmtDivergenceScore := -100
    mmtLastDivergenceScore := -100
    mmtDivergenceSignalDuration := 1
else if mmtBullDiv
    mmtDivergenceScore := 100
    mmtLastDivergenceScore := 100
    mmtDivergenceSignalDuration := 1
else if mmtHiddenBearDiv
    mmtDivergenceScore := -70
    mmtLastDivergenceScore := -70
    mmtDivergenceSignalDuration := 1
else if mmtHiddenBullDiv
    mmtDivergenceScore := 70
    mmtLastDivergenceScore := 70
    mmtDivergenceSignalDuration := 1
else
    if mmtDivergenceSignalDuration > 0
        mmtDivergenceSignalDuration := mmtDivergenceSignalDuration - 1
        mmtDivergenceScore := mmtLastDivergenceScore
    else
        mmtDivergenceScore := 0

// 最终MMT分数计算
mmtScore := mmtChannelScore * channelWeight + mmtDivergenceScore * divergenceWeight

// === 综合评分计算 ===
// 修改合评分计算，加入MMT分数
combinedScore := rsiScore * rsiWeight + td9Score * td9Weight + macdScore * macdWeight + smcScore * dynamicSmcWeight + mmtScore * mmtWeight

// === 多时间框架数据获取 ===
get_tf_score(tf) =>
    // 直接获取目标时间框架的数据，不考虑当前时间框架
    request.security(syminfo.tickerid, tf, combinedScore, lookahead=barmerge.lookahead_off)

// 只在需要时获取数据
var float combined_15m = na
var float combined_1h = na
var float combined_4h = na
var float combined_1d = na
var float combined_1w = na
var float combined_1M = na

if barstate.islast
    if show_15m
        combined_15m := get_tf_score("15")
    if show_1h
        combined_1h := get_tf_score("60")
    if show_4h
        combined_4h := get_tf_score("240")
    if show_1d
        combined_1d := get_tf_score("D")
    if show_1w
        combined_1w := get_tf_score("W")
    if show_1M
        combined_1M := get_tf_score("M")

// === 表格显示 ===
if barstate.islast
    // 计算需要显示的时间框架数量
    visible_tf_count = 0
    visible_tf_count := visible_tf_count + (show_15m ? 1 : 0)
    visible_tf_count := visible_tf_count + (show_1h ? 1 : 0)
    visible_tf_count := visible_tf_count + (show_4h ? 1 : 0)
    visible_tf_count := visible_tf_count + (show_1d ? 1 : 0)
    visible_tf_count := visible_tf_count + (show_1w ? 1 : 0)
    visible_tf_count := visible_tf_count + (show_1M ? 1 : 0)

    // 只有当至少择了一个时间框架时才显示表格
    if visible_tf_count > 0
        var table scoreTable = table.new(position.bottom_right, visible_tf_count + 1, 2, bgcolor=color.new(color.black, 80))
        
        // 重置表格
        table.clear(scoreTable, 0, 0)
        
        // 设置表头
        table.cell(scoreTable, 0, 0, "时间框架", text_color=color.white, text_size=size.small)
        table.cell(scoreTable, 0, 1, "综合得分", text_color=color.white, text_size=size.small)
        
        // 动态添加选中的时间框架列
        col = 1
        if show_15m and not na(combined_15m)
            table.cell(scoreTable, col, 0, "15分钟", text_color=color.white, text_size=size.small)
            table.cell(scoreTable, col, 1, str.tostring(combined_15m, "#.##"), text_color=getScoreColor(combined_15m), text_size=size.small)
            col := col + 1
            
        if show_1h and not na(combined_1h)
            table.cell(scoreTable, col, 0, "1小时", text_color=color.white, text_size=size.small)
            table.cell(scoreTable, col, 1, str.tostring(combined_1h, "#.##"), text_color=getScoreColor(combined_1h), text_size=size.small)
            col := col + 1
            
        if show_4h and not na(combined_4h)
            table.cell(scoreTable, col, 0, "4小时", text_color=color.white, text_size=size.small)
            table.cell(scoreTable, col, 1, str.tostring(combined_4h, "#.##"), text_color=getScoreColor(combined_4h), text_size=size.small)
            col := col + 1
            
        if show_1d and not na(combined_1d)
            table.cell(scoreTable, col, 0, "日线", text_color=color.white, text_size=size.small)
            table.cell(scoreTable, col, 1, str.tostring(combined_1d, "#.##"), text_color=getScoreColor(combined_1d), text_size=size.small)
            col := col + 1
            
        if show_1w and not na(combined_1w)
            table.cell(scoreTable, col, 0, "周线", text_color=color.white, text_size=size.small)
            table.cell(scoreTable, col, 1, str.tostring(combined_1w, "#.##"), text_color=getScoreColor(combined_1w), text_size=size.small)
            col := col + 1
            
        if show_1M and not na(combined_1M)
            table.cell(scoreTable, col, 0, "月线", text_color=color.white, text_size=size.small)
            table.cell(scoreTable, col, 1, str.tostring(combined_1M, "#.##"), text_color=getScoreColor(combined_1M), text_size=size.small)
