//@version=5
indicator('趋势变化指标', overlay=true) 

// === 指标参数设置 ===
ema_fast_input = input(30, title = "快速EMA周期")
ema_slow_input = input(60, title = "慢速EMA周期")
atr_length_input = input(60, title = "ATR周期")
atr_margin_input = input(0.3, title = "ATR倍数")
sma_input = input(140, title = "SMA周期")

// === 辅助函数 ===
format_timeframe_value(tf_value) =>
    tf_num = str.tonumber(tf_value)
    if na(tf_num)
        switch tf_value
            "D" => "日线"
            "W" => "周线"
            "M" => "月线"
            => tf_value
    else
        switch tf_num
            1 => "1分钟"
            3 => "3分钟"
            5 => "5分钟"
            15 => "15分钟"
            30 => "30分钟"
            45 => "45分钟"
            60 => "1小时"
            120 => "2小时"
            180 => "3小时"
            240 => "4小时"
            360 => "6小时"
            480 => "8小时"
            720 => "12小时"
            1440 => "日线"
            10080 => "周线"
            43200 => "月线"
            => str.tostring(tf_num) + "分钟"

format_timeframe(tf) =>
    tf == "" ? format_timeframe_value(timeframe.period) : format_timeframe_value(tf)

// 获取实际使用的时间周期
get_actual_timeframe(tf) =>
    tf == "" ? timeframe.period : tf

// === 时间周期设置 ===
tf_group = "时间周期设置"
timeframe1 = input.timeframe(title="较短时间周期", defval="240", tooltip="设置较短的时间周期，用于短期趋势判断。选择图表时间周期则使用当前图表的时间周期。", group=tf_group)
timeframe2 = input.timeframe(title="较长时间周期", defval="1440", tooltip="设置较长的时间周期，用于长期趋势判断。选择图表时间周期则使用当前图表的时间周期。", group=tf_group)

// === 短周期指标计算 ===
actual_tf1 = get_actual_timeframe(timeframe1)
ema_fast_tf1 = request.security(syminfo.tickerid, actual_tf1, ta.ema(close, ema_fast_input))
ema_slow_tf1 = request.security(syminfo.tickerid, actual_tf1, ta.ema(close, ema_slow_input))
ema_diff_tf1 = ema_fast_tf1 - ema_slow_tf1
ema_bull_tf1 = ema_diff_tf1 > atr_margin_input * request.security(syminfo.tickerid, actual_tf1, ta.atr(atr_length_input))
ema_bear_tf1 = ema_diff_tf1 < -atr_margin_input * request.security(syminfo.tickerid, actual_tf1, ta.atr(atr_length_input))
ema_over_tf1 = ema_fast_tf1 > ema_slow_tf1

// === 长周期指标计算 ===
actual_tf2 = get_actual_timeframe(timeframe2)
ema_fast_tf2 = request.security(syminfo.tickerid, actual_tf2, ta.ema(close, ema_fast_input))
ema_slow_tf2 = request.security(syminfo.tickerid, actual_tf2, ta.ema(close, ema_slow_input))
ema_diff_tf2 = ema_fast_tf2 - ema_slow_tf2
ema_bull_tf2 = ema_diff_tf2 > atr_margin_input * request.security(syminfo.tickerid, actual_tf2, ta.atr(atr_length_input))
ema_bear_tf2 = ema_diff_tf2 < -atr_margin_input * request.security(syminfo.tickerid, actual_tf2, ta.atr(atr_length_input))
ema_over_tf2 = ema_fast_tf2 > ema_slow_tf2

// === 颜色设置 ===
color bull = color.new(color(#00FF00), 0)      // 多头-绿色
color bull_fill = color.new(color(#00FF00), 50) // 多头填充-半透明绿色
color bear = color.new(color(#FF0000), 0)      // 空头-红色
color bear_fill = color.new(color(#FF0000), 50) // 空头填充-半透明红色
color neutral = color.new(color(#FFFFFF), 0)    // 中性-白色
color neutral_fill = color.new(color(#FFFFFF), 50) // 中性填充-半透明白色

// === 绘制EMA线和填充 ===
iff_1 = ema_bear_tf2 ? bear : neutral
ema_fast_plot = plot(ema_fast_tf2, title="快速EMA", linewidth=2, color=ema_bull_tf2 ? bull : iff_1)
iff_2 = ema_bear_tf2 ? bear : neutral
ema_slow_plot = plot(ema_slow_tf2, title="慢速EMA", linewidth=2, color=ema_bull_tf2 ? bull : iff_2)
iff_3 = ema_bear_tf2 ? bear_fill : neutral_fill
fill(ema_fast_plot, ema_slow_plot, color=ema_bull_tf2 ? bull_fill : iff_3, title="EMA填充")

// === 趋势状态判断 ===
var is_long = false
no_trend = not ema_bull_tf2 and not ema_bear_tf2
trend = ema_bull_tf2 or ema_bear_tf2
is_change = not is_long and no_trend
is_no_change = is_long and trend
is_long := is_change ? true : is_no_change ? false : is_long

// === 趋势警告信号 ===
warning_bull = ema_bull_tf2 and ema_bear_tf1  // 长周期多头但短周期空头
warning_bear = ema_bear_tf2 and ema_bull_tf1  // 长周期空头但短周期多头
warning = warning_bull or warning_bear

// === SMA计算和预测 ===
close_tf2 = request.security(syminfo.tickerid, actual_tf2, close)
sma = request.security(syminfo.tickerid, actual_tf2, ta.sma(close, sma_input))
plot(sma, title="SMA", color=color.new(color.white, 0), linewidth=2)

// SMA预测计算（1-9天）
sma_forecast_1_1 = request.security(syminfo.tickerid, actual_tf2, ((sma * sma_input) + (close_tf2 - close_tf2[sma_input])) / sma_input)
sma_forecast_1_2 = request.security(syminfo.tickerid, actual_tf2, ((sma * sma_input) + ((close_tf2 * 2) - close_tf2[sma_input] - close_tf2[sma_input - 1])) / sma_input)
sma_forecast_1_3 = request.security(syminfo.tickerid, actual_tf2, ((sma * sma_input) + ((close_tf2 * 3) - close_tf2[sma_input] - close_tf2[sma_input - 1] - close_tf2[sma_input - 2])) / sma_input)
sma_forecast_1_4 = request.security(syminfo.tickerid, actual_tf2, ((sma * sma_input) + ((close_tf2 * 4) - close_tf2[sma_input] - close_tf2[sma_input - 1] - close_tf2[sma_input - 2] - close_tf2[sma_input - 3])) / sma_input)
sma_forecast_1_5 = request.security(syminfo.tickerid, actual_tf2, ((sma * sma_input) + ((close_tf2 * 5) - close_tf2[sma_input] - close_tf2[sma_input - 1] - close_tf2[sma_input - 2] - close_tf2[sma_input - 3] - close_tf2[sma_input - 4])) / sma_input)
sma_forecast_1_6 = request.security(syminfo.tickerid, actual_tf2, ((sma * sma_input) + ((close_tf2 * 6) - close_tf2[sma_input] - close_tf2[sma_input - 1] - close_tf2[sma_input - 2] - close_tf2[sma_input - 3] - close_tf2[sma_input - 4] - close_tf2[sma_input - 5])) / sma_input)
sma_forecast_1_7 = request.security(syminfo.tickerid, actual_tf2, ((sma * sma_input) + ((close_tf2 * 7) - close_tf2[sma_input] - close_tf2[sma_input - 1] - close_tf2[sma_input - 2] - close_tf2[sma_input - 3] - close_tf2[sma_input - 4] - close_tf2[sma_input - 5] - close_tf2[sma_input - 6])) / sma_input)
sma_forecast_1_8 = request.security(syminfo.tickerid, actual_tf2, ((sma * sma_input) + ((close_tf2 * 8) - close_tf2[sma_input] - close_tf2[sma_input - 1] - close_tf2[sma_input - 2] - close_tf2[sma_input - 3] - close_tf2[sma_input - 4] - close_tf2[sma_input - 5] - close_tf2[sma_input - 6] - close_tf2[sma_input - 7])) / sma_input)
sma_forecast_1_9 = request.security(syminfo.tickerid, actual_tf2, ((sma * sma_input) + ((close_tf2 * 9) - close_tf2[sma_input] - close_tf2[sma_input - 1] - close_tf2[sma_input - 2] - close_tf2[sma_input - 3] - close_tf2[sma_input - 4] - close_tf2[sma_input - 5] - close_tf2[sma_input - 6] - close_tf2[sma_input - 7] - close_tf2[sma_input - 8])) / sma_input)

// === 绘制SMA预测线 ===
plot(sma_forecast_1_1, title="SMA 1_1", color=color.new(color.white, 0), linewidth=1, style=plot.style_circles, offset=1, show_last=1)
plot(sma_forecast_1_2, title="SMA 1_2", color=color.new(color.white, 0), linewidth=1, style=plot.style_circles, offset=2, show_last=1)
plot(sma_forecast_1_3, title="SMA 1_3", color=color.new(color.white, 0), linewidth=1, style=plot.style_circles, offset=3, show_last=1)
plot(sma_forecast_1_4, title="SMA 1_4", color=color.new(color.white, 0), linewidth=1, style=plot.style_circles, offset=4, show_last=1)
plot(sma_forecast_1_5, title="SMA 1_5", color=color.new(color.white, 0), linewidth=1, style=plot.style_circles, offset=5, show_last=1)
plot(sma_forecast_1_6, title="SMA 1_6", color=color.new(color.white, 0), linewidth=1, style=plot.style_circles, offset=6, show_last=1)
plot(sma_forecast_1_7, title="SMA 1_7", color=color.new(color.white, 0), linewidth=1, style=plot.style_circles, offset=7, show_last=1)
plot(sma_forecast_1_8, title="SMA 1_8", color=color.new(color.white, 0), linewidth=1, style=plot.style_circles, offset=8, show_last=1)
plot(sma_forecast_1_9, title="SMA 1_9", color=color.new(color.white, 0), linewidth=1, style=plot.style_circles, offset=9, show_last=1)

// === 表格绘制 ===
color green = color.new(color.green, 30)
color red = color.new(color.red, 30)
color gray = color.new(color.gray, 30)
var table t = table.new(position.bottom_right, 2, 4, border_width=2)

// 显示标题
table.cell(t, 0, 0, text="时间周期", width=12, text_color=color.white, bgcolor=color.new(color.gray, 60))
table.cell(t, 1, 0, text="趋势状态", width=12, text_color=color.white, bgcolor=color.new(color.gray, 60))

// 显示短周期状态
trend_text_tf1 = ema_bull_tf1 ? "强势多头" : ema_bear_tf1 ? "强势空头" : ema_over_tf1 ? "弱势多头" : "弱势空头"
trend_color_tf1 = ema_bull_tf1 ? green : 
                 ema_bear_tf1 ? red : 
                 ema_over_tf1 ? color.new(color.green, 60) : 
                 color.new(color.red, 60)
table.cell(t, 0, 1, text=format_timeframe(timeframe1), width=12, text_color=color.white, bgcolor=color.new(color.gray, 60))
table.cell(t, 1, 1, text=trend_text_tf1, width=12, text_color=color.white, bgcolor=trend_color_tf1)

// 显示长周期状态
trend_text_tf2 = ema_bull_tf2 ? "强势多头" : ema_bear_tf2 ? "强势空头" : ema_over_tf2 ? "弱势多头" : "弱势空头"
trend_color_tf2 = ema_bull_tf2 ? green : 
                 ema_bear_tf2 ? red : 
                 ema_over_tf2 ? color.new(color.green, 60) : 
                 color.new(color.red, 60)
table.cell(t, 0, 2, text=format_timeframe(timeframe2), width=12, text_color=color.white, bgcolor=color.new(color.gray, 60))
table.cell(t, 1, 2, text=trend_text_tf2, width=12, text_color=color.white, bgcolor=trend_color_tf2)

// 显示综合结论
var string conclusion = ""
if ema_bull_tf2 and ema_bull_tf1
    conclusion := "双周期强势共振看多"
else if ema_bear_tf2 and ema_bear_tf1
    conclusion := "双周期强势共振看空"
else if ema_bull_tf2 and ema_bear_tf1
    conclusion := "短期调整，长期看多"
else if ema_bear_tf2 and ema_bull_tf1
    conclusion := "短期反弹，长期看空"
else if (ema_bull_tf2 or ema_over_tf2) and (ema_bull_tf1 or ema_over_tf1)
    conclusion := "双周期弱势共振看多"
else if (ema_bear_tf2 or not ema_over_tf2) and (ema_bear_tf1 or not ema_over_tf1)
    conclusion := "双周期弱势共振看空"
else if ema_bull_tf2 or ema_over_tf2
    conclusion := "长期偏多"
else if ema_bear_tf2 or not ema_over_tf2
    conclusion := "长期偏空"
else
    conclusion := "趋势不明朗"

table.cell(t, 0, 3, text="综合结论", width=12, text_color=color.white, bgcolor=color.new(color.gray, 60))
bgcolor = ema_bull_tf2 and ema_bull_tf1 ? green : 
         ema_bear_tf2 and ema_bear_tf1 ? red : 
         (ema_bull_tf2 or ema_over_tf2) and (ema_bull_tf1 or ema_over_tf1) ? color.new(color.green, 60) :
         (ema_bear_tf2 or not ema_over_tf2) and (ema_bear_tf1 or not ema_over_tf1) ? color.new(color.red, 60) :
         color.new(color.gray, 30)
table.cell(t, 1, 3, text=conclusion, width=12, text_color=color.white, bgcolor=bgcolor)

// === 警告信号 ===
alertcondition(is_change, title="趋势变化", message="警告！趋势变化！")
alertcondition(warning, title="止盈", message="警告！趋势背离，建议止盈！")