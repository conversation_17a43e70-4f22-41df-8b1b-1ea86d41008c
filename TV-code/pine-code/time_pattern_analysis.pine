//@version=5
indicator("时间段趋势概率分析", overlay=false)

//=== 用户输入参数 ===
consecutiveBars    = input.int(5, "连续K线数量", minval=2)
minPriceMove       = input.float(0.001, "最小价格变动(绝对值)", step=0.0001)
daysToCheck        = input.int(5, "统计历史天数", minval=5, maxval=100)
highlightThreshold = input.float(0.7, "高亮显示概率阈值", step=0.01, minval=0, maxval=1)
flexibleTrend      = input.bool(false, "放宽趋势判断条件")

// 一天共288个5分钟时段（24*12=288）
var int TIMESLOTS_PER_DAY = 288

// 当前统计数据
var float[] occurrences = array.new_float(TIMESLOTS_PER_DAY, 0.0)
var bool[] dailyTrendFound = array.new_bool(TIMESLOTS_PER_DAY, false)

// 历史数据环形缓冲区
var bool[] historicalData = array.new_bool(TIMESLOTS_PER_DAY * daysToCheck, false)
var int historicalDataIndex = 0  // 指向最早的一天数据将被替换的位置
var int completedDays = 0

// 调试信息
var label debugLabel = na

//=== 函数：判断是否出现单边连续走势 ===
f_checkTrend(int condBars, float minMove, bool flexible) =>
    var bool validTrend = false
    var bool trendDir = false
    
    if bar_index >= condBars - 1
        float totalChange = close - close[condBars - 1]
        float firstChange = close - close[1]
        trendDir := firstChange > 0
        
        int diffCount = 0
        bool hasDirection = false
        
        for i = 0 to condBars - 2
            float change = close[i] - close[i+1]
            if math.abs(change) >= minMove
                hasDirection := true
                if (change > 0) != trendDir
                    diffCount += 1
        
        validTrend := hasDirection and math.abs(totalChange) >= minMove and (flexible ? diffCount <= 1 : diffCount == 0)
    
    [validTrend, trendDir]

//=== 时间和日切换检测 ===
currentY = year(time, "UTC")
currentM = month(time, "UTC")
currentD = dayofmonth(time, "UTC")
currentDayStr = str.format("{0}-{1}-{2}", str.tostring(currentY), str.tostring(currentM), str.tostring(currentD))
var string prevDayStr = currentDayStr

barHour   = hour(time, "UTC")
barMinute = minute(time, "UTC")
timeSlotIndex = barHour * 12 + math.floor(barMinute / 5)

//=== 日切换逻辑 ===
if currentDayStr != prevDayStr
    // 如果已达最大天数窗口，则先移除最早一天数据
    if completedDays == daysToCheck
        int startIdx = historicalDataIndex * TIMESLOTS_PER_DAY
        for i = 0 to TIMESLOTS_PER_DAY - 1
            if array.get(historicalData, startIdx + i)
                // 减去最早的一天数据
                array.set(occurrences, i, array.get(occurrences, i) - 1)
    
    // 将昨天的数据写入历史记录
    int newStartIdx = historicalDataIndex * TIMESLOTS_PER_DAY
    for i = 0 to TIMESLOTS_PER_DAY - 1
        array.set(historicalData, newStartIdx + i, array.get(dailyTrendFound, i))
    
    // 将昨天的数据加回 occurrences（若尚未达到daysToCheck天）
    if completedDays < daysToCheck
        for i = 0 to TIMESLOTS_PER_DAY - 1
            if array.get(dailyTrendFound, i)
                array.set(occurrences, i, array.get(occurrences, i) + 1)
        completedDays += 1
    else
        // 如果已达天数上限，则新的一天数据已写入同一位置，相当于替换了最早一天的数据
        for i = 0 to TIMESLOTS_PER_DAY - 1
            if array.get(dailyTrendFound, i)
                array.set(occurrences, i, array.get(occurrences, i) + 1)
    
    // 更新环形索引
    historicalDataIndex := (historicalDataIndex + 1) % daysToCheck
    
    // 重置当天数据
    array.fill(dailyTrendFound, false)
    prevDayStr := currentDayStr
    
    // 调试信息
    if barstate.islast
        float maxOcc = array.max(occurrences)
        float maxProb = completedDays > 0 ? maxOcc / completedDays : float(na)
        float currOcc = array.get(occurrences, timeSlotIndex)
        float currProb = completedDays > 0 ? currOcc / completedDays : float(na)
        
        label.delete(debugLabel)
        debugLabel := label.new(bar_index, high, 
                              str.format("统计天数: {0}/{1}\n当前时段: {2}/{3} = {4}%\n最高概率: {5}/{6} = {7}%", 
                              str.tostring(completedDays),
                              str.tostring(daysToCheck),
                              str.tostring(currOcc, "#.##"),
                              str.tostring(completedDays),
                              na(currProb) ? "N/A" : str.tostring(currProb * 100, "#.##"),
                              str.tostring(maxOcc, "#.##"),
                              str.tostring(completedDays),
                              na(maxProb) ? "N/A" : str.tostring(maxProb * 100, "#.##")),
                              color=color.new(color.blue, 80),
                              style=label.style_label_down,
                              textcolor=color.white)

//=== 检查当前bar是否出现趋势 ===
[isTrend, _] = f_checkTrend(consecutiveBars, minPriceMove, flexibleTrend)
if isTrend and not array.get(dailyTrendFound, timeSlotIndex)
    array.set(dailyTrendFound, timeSlotIndex, true)
    // 若当前已经有足够天数统计，则即时更新 occurrences
    if completedDays == daysToCheck
        array.set(occurrences, timeSlotIndex, array.get(occurrences, timeSlotIndex) + 1)

//=== 显示处理 ===
currentProb = (completedDays > 0) ? (array.get(occurrences, timeSlotIndex) / completedDays) : 0.0

// 绘制概率曲线和阈值线
plot(currentProb, title="概率", color=color.blue, linewidth=2)
hline(highlightThreshold, title="阈值", color=color.red, linestyle=hline.style_dashed)

// 在整点显示概率标签（仅显示高概率时段）
if barMinute == 0 and currentProb >= highlightThreshold and barstate.islast
    minuteStr = barMinute < 10 ? "0" + str.tostring(barMinute) : str.tostring(barMinute)
    probPercent = str.tostring(currentProb * 100, "#.#") + "%"
    label.new(bar_index, currentProb, 
             str.format("{0}:{1}\n{2}", str.tostring(barHour), minuteStr, probPercent), 
             style=label.style_label_down,
             textcolor=color.black,
             size=size.tiny,
             color=color.new(color.yellow, 20))