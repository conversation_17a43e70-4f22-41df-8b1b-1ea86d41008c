//@version=5
indicator("MCSI TD9 Test", overlay=false)

// === TD9计算 ===
var int TD = 0, TS = 0
TD := close > close[4] ? nz(TD[1])+1 : 0
TS := close < close[4] ? nz(TS[1])+1 : 0
TDUp = TD - ta.valuewhen(TD < TD[1], TD, 1)
TDDn = TS - ta.valuewhen(TS < TS[1], TS, 1)

// === TD9评分计算 ===
getTD9Score(count) => count == 0 or count <= 6 ? 0.0 : count == 7 ? 20.0 : count == 8 ? 50.0 : count == 9 or (count >= 13 and count <= 16) ? 100.0 : count >= 10 and count <= 12 ? 80.0 : 0.0

td9Score = TDDn > 0 ? getTD9Score(TDDn) : -getTD9Score(TDUp)

// === 显示效果 ===
bgcolor(td9Score != 0 ? color.new(td9Score > 0 ? color.green : color.red, 100 - math.abs(td9Score)) : na)
plot(td9Score, "TD9分数", color.yellow, 2)
hline(0, "零线", color.gray, hline.style_dotted)
hline(100, "上限", color.gray, hline.style_dashed)
hline(-100, "下限", color.gray, hline.style_dashed)
plotshape(TDUp > 0, "上升计数", shape.triangleup, location.bottom, color.green, 0)
plotshape(TDDn > 0, "下降计数", shape.triangledown, location.top, color.red, 0)

if barstate.islast
    label.new(bar_index + 5, 0, 
              "TD9分数: " + str.tostring(td9Score, "#.##") + "\n" + 
              (TDUp > 0 ? "上升计数: " + str.tostring(TDUp) : 
               TDDn > 0 ? "下降计数: " + str.tostring(TDDn) : ""),
              xloc.bar_index, yloc.price, 
              color.white, label.style_none, color.white, 
              size.normal, text.align_left)