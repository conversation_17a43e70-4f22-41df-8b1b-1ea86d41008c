// 
// Copyright (C) 2017-2020 CC BY, whentotrade / <PERSON>
// Source:
// Book: Decoding The Hidden Market Rhythm - Part 1: Dynamic Cycles
// Chapter 10: "Cycle Swing Indicator: Trading the swing of the dominant cycle"
// 
// Cycle Swing Indicator (CSI)
//
// Features: 
// adaptive momentum, ultra-smooth, zero-lag, ultra-sharp turning points & accurate divergence detection
//
// Includes TradingView Divergence Indicator code
//
// License: 
// This work is licensed under a Creative Commons Attribution 4.0 International License.
// You are free to share the material in any medium or format and remix, transform, and build upon the material for any purpose, 
// even commercially. You must give appropriate credit to the authors book and website, provide a link to the license, and indicate 
// if changes were made. You may do so in any reasonable manner, but not in any way that suggests the licensor endorses you or your use. 
//
// © WhenToTrade

//@version=4
study(title="Cycle Swing Momentum", overlay = false)

//===== Inputs
showDivergences = input(false, "Enable Divergences", input.bool)

Cycle1(i, waveThrottle, cycs) =>
    ret = 6.0*waveThrottle+1.0
    
    if (i==0) 
        ret := 1.0+waveThrottle
    else if (i==1) 
        ret := 1.0+waveThrottle*5.0
    else if (i==(cycs-1)) 
        ret := 1.0+waveThrottle
    else if (i==(cycs-2)) 
        ret := 1.0+waveThrottle*5.0
   
    ret


Cycle2(i, waveThrottle, cycs) =>
    ret = -4.0*waveThrottle 
    if (i==0) 
        ret := -2.0*waveThrottle
    else if (i==(cycs-1)) 
        ret := 0.0
    else if (i==(cycs-2)) 
        ret := -2.0*waveThrottle
   
    ret

Cycle3(i, waveThrottle, cycs) =>
    ret = waveThrottle
    
    if (i==(cycs-1)) 
        ret := 0.0
    else if (i==(cycs-2)) 
        ret := 0.0
   
    ret

iWTT_CSI_processor(CycleCount) =>
    wtt1=0.0
    wtt2=0.0
    wtt3=0.0
    wtt4=0.0
    wtt5=0.0
    _wtt1=0.0
    _wtt2=0.0
    _wtt3=0.0
    _wtt4=0.0
    _wtt5=0.0
    momentum=0.0
    acceleration=0.0
    swing=0.0
    cycs=50
    waveThrottle=float(160*CycleCount)
    currentVal=0.0  
    
    for i = 0 to cycs - 1 by 1
        swing:=Cycle1(i,waveThrottle,cycs)-wtt4*wtt1-_wtt5*_wtt2
        if(swing==0) 
            break
        momentum := Cycle2(i,waveThrottle,cycs)
        _wtt1 := wtt1
        wtt1 := (momentum-wtt4*wtt2)/swing

        acceleration:=Cycle3(i,waveThrottle,cycs)
        _wtt2 := wtt2
        wtt2 := acceleration/swing
 
        currentVal:=(close[49-i]-_wtt3*_wtt5-wtt3*wtt4)/swing
        _wtt3  := wtt3
        wtt3   := currentVal
        wtt4   := momentum-wtt5*_wtt1
        _wtt5  := wtt5
        wtt5   := acceleration

    currentVal

banding(CRSI, Period, Leveling) =>
    var percent = Leveling / 100.0
    periodMinusOne = Period-1
    maxima = -999999.0
    minima =  999999.0
    for i=0 to periodMinusOne
        crsi = nz(CRSI[i])
        if      crsi > maxima
            maxima := crsi
        else if crsi < minima
            minima := crsi
    stepFactor = (maxima - minima) / 100.0
    float lowBand = na
    for steps=0 to 100
        testValue = minima + stepFactor * steps
        below = 0
        for m=0 to periodMinusOne
            if CRSI[m] < testValue
                below := below + 1
        if below/Period >= percent
            lowBand := testValue
            break
    float highBand = na
    for steps=0 to 100
        testValue = maxima - stepFactor * steps
        above = 0
        for m=0 to periodMinusOne
            if CRSI[m] >= testValue
                above := above + 1
        if above/Period >= percent
            highBand := testValue
            break
    [highBand, lowBand]


//==== Vars
var leveling  = 10
var cyclicMemory = 34

//===== Calculations
thrust1=iWTT_CSI_processor(1)
thrust2=iWTT_CSI_processor(10)
      
CSIBuffer = thrust1-thrust2

[highBand, lowBand] = banding( CSIBuffer, cyclicMemory,  leveling)

highband = plot(highBand, "HighBand", color=color.aqua , editable=false)
lowband  = plot( lowBand,  "LowBand", color=color.aqua , editable=false)
fill( highband,  lowband, transp=90 , color=#CCCC77   )

plot(0, color=color.black, linewidth=1, transp=25)
//plot(CSIBuffer, color=color.fuchsia, linewidth=1)
plot(CSIBuffer, color = CSIBuffer > 0 ? color.green : color.red, linewidth = 1)


// ===== Divergence Section


//------------------------------------------------------------------------------
//------------------------------------------------------------------------------
// Divergence Indicator uSING crsi
//------------------------------------------------------------------------------
//------------------------------------------------------------------------------
lbR = input(title="Pivot Lookback Right", defval=5) // 5
lbL = input(title="Pivot Lookback Left", defval=5) // 25
rangeUpper = input(title="Max of Lookback Range", defval=60) // 80
rangeLower = input(title="Min of Lookback Range", defval=5) // 10

plotBull = input(title="Plot Bullish", defval=true)
plotHiddenBull = input(title="Plot Hidden Bullish", defval=true)
plotBear = input(title="Plot Bearish", defval=true)
plotHiddenBear = input(title="Plot Hidden Bearish", defval=true)

bearColor = color.red
bullColor = color.green
divergenceSmartColor = color.silver

hiddenBullColor = color.new(color.green, 50) // make transparent
hiddenBearColor = color.new(color.red, 50) // make transparent
textColor = color.white
noneColor = color.new(color.white, 100)

osc = CSIBuffer

plFound = na(pivotlow(osc, lbL, lbR)) ? false : true
phFound = na(pivothigh(osc, lbL, lbR)) ? false : true

_inRange(cond) =>
	bars = barssince(cond == true)
	rangeLower <= bars and bars <= rangeUpper
	
	

//------------------------------------------------------------------------------
// Regular Bullish
// Osc: Higher Low
oscHL = osc[lbR] > valuewhen(plFound, osc[lbR], 1) and _inRange(plFound[1])

// Price: Lower Low

priceLL = low[lbR] < valuewhen(plFound, low[lbR], 1)
bullCond = showDivergences and plotBull and priceLL and oscHL and plFound

plot(
     showDivergences and plFound and showDivergences ? osc[lbR] : na,
     offset=-lbR,
     title="Regular Bullish",
     linewidth=1,
     color=(bullCond ? divergenceSmartColor : noneColor),
     transp=0
     )

plotshape(bullCond ? osc[lbR] : na,"Bullish Circle",shape.circle,location.absolute,bullColor,0,offset=-lbR,size=size.tiny)


//------------------------------------------------------------------------------
// Hidden Bullish
// Osc: Lower Low

oscLL = osc[lbR] < valuewhen(plFound, osc[lbR], 1) and _inRange(plFound[1])

// Price: Higher Low

priceHL = low[lbR] > valuewhen(plFound, low[lbR], 1)
hiddenBullCond = showDivergences and plotHiddenBull and priceHL and oscLL and plFound

plot(
	 showDivergences and plFound ? osc[lbR] : na,
	 offset=-lbR,
	 title="Hidden Bullish",
	 linewidth=1,
	 color=(hiddenBullCond ? divergenceSmartColor : noneColor),
	 transp=0
	 )

plotshape(hiddenBullCond ? osc[lbR] : na,"Hidden Bullish Circle",shape.circle,location.absolute,hiddenBullColor,0,offset=-lbR,size=size.tiny)


//------------------------------------------------------------------------------
// Regular Bearish
// Osc: Lower High

oscLH = osc[lbR] < valuewhen(phFound, osc[lbR], 1) and _inRange(phFound[1])

// Price: Higher High

priceHH = high[lbR] > valuewhen(phFound, high[lbR], 1)

bearCond = showDivergences and plotBear and priceHH and oscLH and phFound

plot(
	 showDivergences and phFound ? osc[lbR] : na,
	 offset=-lbR,
	 title="Regular Bearish",
	 linewidth=1,
	 color=(bearCond ? divergenceSmartColor : noneColor),
	 transp=0
	 )

plotshape(bearCond ? osc[lbR] : na,"Bearish Circle",shape.circle,location.absolute,bearColor,0,offset=-lbR,size=size.tiny)


//------------------------------------------------------------------------------
// Hidden Bearish
// Osc: Higher High

oscHH = osc[lbR] > valuewhen(phFound, osc[lbR], 1) and _inRange(phFound[1])

// Price: Lower High

priceLH = high[lbR] < valuewhen(phFound, high[lbR], 1)

hiddenBearCond = showDivergences and plotHiddenBear and priceLH and oscHH and phFound

plot(
	 showDivergences and phFound ? osc[lbR] : na,
	 offset=-lbR,
	 title="Hidden Bearish",
	 linewidth=2,
	 color=(hiddenBearCond ? divergenceSmartColor : noneColor),
	 transp=0
	 )

plotshape(hiddenBearCond ? osc[lbR] : na,"Hidden Bearish Circle",shape.circle,location.absolute,hiddenBearColor,0,offset=-lbR,size=size.tiny)

