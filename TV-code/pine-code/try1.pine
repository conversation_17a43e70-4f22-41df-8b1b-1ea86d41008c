//@version=5
indicator('趋势变化指标', overlay=false)  // 改为独立显示

// 输入参数
ema_fast_input = input(30, title = "EMA 快线")
ema_slow_input = input(60, title = "EMA 慢线")
atr_length_input = input(60, title = "ATR 周期")
atr_margin_input = input(0.3, title = "ATR 边界")
sma_input = input(140, title = "SMA 周期")
select_timeframe = input.string(title="时间周期", defval="4H", options=["1H", "2H", "4H", "6H", "8H", "12H"])

// 时间周期转换
timeframe = select_timeframe == "1H" ? "60" : select_timeframe == "2H" ? "120" : select_timeframe == "4H" ? "240" : select_timeframe == "6H" ? "360" : select_timeframe == "8H" ? "480" : "720"

// 计算指标
ema_fast_4h = request.security(syminfo.tickerid, timeframe, ta.ema(close, ema_fast_input))
ema_slow_4h = request.security(syminfo.tickerid, timeframe, ta.ema(close, ema_slow_input))
ema_diff_4h = ema_fast_4h - ema_slow_4h
ema_bull_4h = ema_diff_4h > atr_margin_input * request.security(syminfo.tickerid, timeframe, ta.atr(atr_length_input))
ema_bear_4h = ema_diff_4h < -atr_margin_input * request.security(syminfo.tickerid, timeframe, ta.atr(atr_length_input))
ema_over_4h = ema_fast_4h > ema_slow_4h

ema_fast_1d = request.security(syminfo.tickerid, "1440", ta.ema(close, ema_fast_input))
ema_slow_1d = request.security(syminfo.tickerid, "1440", ta.ema(close, ema_slow_input))
ema_diff_1d = ema_fast_1d - ema_slow_1d
ema_bull_1d = ema_diff_1d > atr_margin_input * request.security(syminfo.tickerid, "1440", ta.atr(atr_length_input))
ema_bear_1d = ema_diff_1d < -atr_margin_input * request.security(syminfo.tickerid, "1440", ta.atr(atr_length_input))
ema_over_1d = ema_fast_1d > ema_slow_1d

// 定义颜色
color bull = color.new(color(#00FF00), 0)
color bull_fill = color.new(color(#00FF00), 80)  // 增加透明度
color bear = color.new(color(#FF0000), 0)
color bear_fill = color.new(color(#FF0000), 80)  // 增加透明度
color neutral = color.new(color(#FFFFFF), 0)
color neutral_fill = color.new(color(#FFFFFF), 90)

// 计算显示值（将价格差转换为百分比）
display_value = ((ema_fast_1d - ema_slow_1d) / ema_slow_1d) * 100

// 绘制主图表
plot(display_value, title="EMA差值", color=color.white, linewidth=2)
hline(0, "零线", color=color.gray, linestyle=hline.style_dotted)

// 添加背景色
bgcolor(ema_bull_1d ? bull_fill : ema_bear_1d ? bear_fill : neutral_fill)

// 趋势变化检测
var is_long = false
no_trend = not ema_bull_1d and not ema_bear_1d
trend = ema_bull_1d or ema_bear_1d
is_change = not is_long and no_trend
is_no_change = is_long and trend
is_long := is_change ? true : is_no_change ? false : is_long

// 警告条件
warning_bull = ema_bull_1d and ema_bear_4h
warning_bear = ema_bear_1d and ema_bull_4h
warning = warning_bull or warning_bear

// 表格显示
color green = color.new(color.green, 30)
color red = color.new(color.red, 30)
color gray = color.new(color.gray, 30)

var table t = table.new(position.bottom_right, 2, 14, border_width=2)
table.cell(t, 1, 1, text=ema_over_4h ? "看涨" : "看跌", width=12, text_color=color.white, bgcolor=ema_bear_4h ? red : ema_bull_4h ? green : gray)
table.cell(t, 1, 2, text=ema_over_1d ? "看涨" : "看跌", width=12, text_color=color.white, bgcolor=ema_bear_1d ? red : ema_bull_1d ? green : gray)
table.cell(t, 1, 3, text="", width=14, height=3, bgcolor=color.new(color.white, 100))

// 警报条件
alertcondition(is_change, title="趋势改变", message="警告！趋势发生改变！")
alertcondition(warning, title="获利提醒", message="警告！建议获利了结！")