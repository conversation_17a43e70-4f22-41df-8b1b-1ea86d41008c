//@version=5
indicator("MCSI MACD Test", overlay=false)

// === MACD参数设置 ===
fastLength = input.int(19, title="快线长度", minval=1)
slowLength = input.int(39, title="慢线长度", minval=1)
signalLength = input.int(9, title="信号长度", minval=1)
lookbackPeriod = input.int(20, title="动态阈值计算周期", minval=10)

// === MACD计算 ===
fastMA = ta.ema(close, fastLength)
slowMA = ta.ema(close, slowLength)
macd = fastMA - slowMA
signal = ta.sma(macd, signalLength)
hist = macd - signal

// === 动态阈值计算 ===
// 使用历史柱状图的标准差来计算动态阈值
histStdDev = ta.stdev(hist, lookbackPeriod)
dynamicThreshold = histStdDev * 1.5  // 可以调整这个乘数来改变灵敏度

// === 状态判断 ===
// 柱状图高度阈值判断（使用动态阈值）
histAboveThreshold = hist > dynamicThreshold
histBelowThreshold = hist < -dynamicThreshold
histNearZero = math.abs(hist) <= dynamicThreshold

// 柱状图颜色状态
histA_IsUp = hist > hist[1] and hist > 0     // 零线以上且上升（青色）
histA_IsDown = hist < hist[1] and hist > 0    // 零线以上且下降（蓝色）
histB_IsDown = hist < hist[1] and hist <= 0   // 零线以下且下降（红色）
histB_IsUp = hist > hist[1] and hist <= 0     // 零线以下且上升（栗色）

// 检测颜色变化
histColorChanged = (histA_IsUp[1] and not histA_IsUp) or    // 从青色变化
                  (histA_IsDown[1] and not histA_IsDown) or  // 从蓝色变化
                  (histB_IsDown[1] and not histB_IsDown) or  // 从红色变化
                  (histB_IsUp[1] and not histB_IsUp)         // 从栗色变化

// === 评分计算 ===
var float macdScore = 0.0

if histNearZero
    macdScore := 0  // 忽略阈值附近的信号
else if histAboveThreshold and histA_IsUp[1] and histA_IsDown  // 高位由上升转下降（卖出）
    // 使用相对值计算额外分数
    float relativeHeight = math.abs(hist / dynamicThreshold)
    float extraScore = math.min(50, (relativeHeight - 1) * 25)  // 将相对高度映射到0-50范围
    macdScore := -(50 + extraScore)  // 卖出信号为负分
else if histBelowThreshold and histB_IsDown[1] and histB_IsUp  // 低位由下降转上升（买入）
    // 使用相对值计算额外分数
    float relativeHeight = math.abs(hist / dynamicThreshold)
    float extraScore = math.min(50, (relativeHeight - 1) * 25)  // 将相对高度映射到0-50范围
    macdScore := 50 + extraScore  // 买入信号为正分
else if histColorChanged  // 其他颜色变化
    macdScore := 0
else
    macdScore := macdScore[1]  // 保持前一个分数

// === 绘制MACD ===
// 柱状图颜色设置
histColor = histA_IsUp ? color.aqua :
           histA_IsDown ? color.blue :
           histB_IsDown ? color.red :
           histB_IsUp ? color.maroon :
           color.gray

// MACD线和信号线颜色设置
macdColor = macd > signal ? color.lime : color.red
signalColor = color.yellow

// 绘制柱状图
plot(hist, title="柱状图", style=plot.style_columns, color=histColor, linewidth=4)

// 绘制MACD线和信号线
plot(macd, title="MACD", color=macdColor, linewidth=2)
plot(signal, title="Signal", color=signalColor, linewidth=2)

// 绘制零线和阈值线
hline(0, "零线", color=color.gray, linestyle=hline.style_dotted)
hline(20, "上阈值", color=color.gray, linestyle=hline.style_dashed)
hline(-20, "下阈值", color=color.gray, linestyle=hline.style_dashed)

// 绘制动态阈值线
plot(dynamicThreshold, title="上动态阈值", color=color.gray, linewidth=1, style=plot.style_circles)
plot(-dynamicThreshold, title="下动态阈值", color=color.gray, linewidth=1, style=plot.style_circles)

// 绘制分数线
plot(macdScore, title="MACD分数", color=color.white, linewidth=2, style=plot.style_line)

// === 显示分数标签 ===
var label scoreLabel = label.new(bar_index, 0, "", xloc.bar_index, yloc.price, color.white, label.style_none, color.white, size.normal, text.align_left)

if barstate.islast
    label.set_xy(scoreLabel, bar_index + 5, 0)
    label.set_text(scoreLabel, "MACD分数: " + str.tostring(macdScore, "#.##"))