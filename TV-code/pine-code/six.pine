// 本Pine Script™代码受Mozilla公共许可证2.0条款的约束，网址为https://mozilla.org/MPL/2.0/
// © smraikai

//@version=5
indicator("六个支柱", overlay=true)

// === 输入参数组 ===
displayGroup = "显示设置"
bgTransparency = input.int(80, "背景透明度", minval=0, maxval=100, tooltip="设置表格背景的透明度，0为完全不透明，100为完全透明", group=displayGroup)
comparisonTF = input.timeframe("D", "比较时间周期", group=displayGroup, tooltip="与当前时间周期进行比较的时间周期")

// 趋势支柱输入
trendGroup = "趋势支柱"
fastPeriod = input.int(8, "快速EMA周期", group=trendGroup, tooltip="用于趋势识别的短期指数移动平均周期")
slowPeriod = input.int(21, "慢速EMA周期", group=trendGroup, tooltip="用于趋势识别的长期指数移动平均周期")

// 动量支柱输入
momGroup = "动量支柱"
fastLength = input.int(12, "MACD快速长度", group=momGroup, tooltip="MACD计算的快速长度")
slowLength = input.int(26, "MACD慢速长度", group=momGroup, tooltip="MACD计算的慢速长度")
signalLength = input.int(9, "MACD信号线长度", group=momGroup, tooltip="MACD信号线长度")

// DM支柱输入
adxGroup = "DM支柱"
adxPeriod = input.int(14, "ADX周期", group=adxGroup, tooltip="平均趋向指数：衡量趋势强度")
adxThreshold = input.int(25, "ADX阈值", group=adxGroup, tooltip="ADX值高于此阈值时趋势被认为较强")

// 随机指标支柱输入
stoGroup = "随机指标支柱"
stoPeriod = input.int(13, "随机指标周期", group=stoGroup, tooltip="将收盘价与一段时间内的价格范围进行比较")

// 分形支柱输入
fractalGroup = "分形支柱"
fractalPeriod = input.int(3, "分形周期", group=fractalGroup, tooltip="识别市场中潜在的反转点")

// OBV支柱输入
obvGroup = "OBV支柱"
obv_short = input.int(3, "短期OBV EMA", group=obvGroup, tooltip="能量潮指标的短期EMA周期")
obv_medium = input.int(13, "中期OBV EMA", group=obvGroup, tooltip="能量潮指标的中期EMA周期")
obv_long = input.int(21, "长期OBV EMA", group=obvGroup, tooltip="能量潮指标的长期EMA周期")

// === 辅助函数 ===
// 获取时间周期趋势描述
get_timeframe_trend(sum, trend_state, mom_state, dm_state, sto_state, fractal_state, obv_state) =>
    trend = if sum > 4
        "强势多头"
    else if sum > 2
        "偏多头"
    else if sum > 0
        "弱势多头"
    else if sum == 0
        "盘整"
    else if sum > -2
        "弱势空头"
    else if sum > -4
        "偏空头"
    else
        "强势空头"
        
    details = array.new_string()
    
    if trend_state > 0
        array.push(details, "趋势↑")
    else if trend_state < 0
        array.push(details, "趋势↓")
        
    if mom_state > 0
        array.push(details, "动量↑")
    else if mom_state < 0
        array.push(details, "动量↓")
        
    if dm_state > 0
        array.push(details, "强度↑")
    else if dm_state < 0
        array.push(details, "强度↓")
        
    if sto_state > 0
        array.push(details, "超买")
    else if sto_state < 0
        array.push(details, "超卖")
        
    if fractal_state > 0
        array.push(details, "顶分形")
    else if fractal_state < 0
        array.push(details, "底分形")
        
    if obv_state > 0
        array.push(details, "资金流入")
    else if obv_state < 0
        array.push(details, "资金流出")
    
    detail_str = ""
    for i = 0 to array.size(details) - 1
        detail_str := detail_str + (i == 0 ? "" : ", ") + array.get(details, i)
    
    [trend, detail_str]

// === 变量计算 ===
// 趋势
fastEMA = ta.ema(close, fastPeriod)
slowEMA = ta.ema(close, slowPeriod)
trendState = if fastEMA > slowEMA * 1.05
    2
else if fastEMA > slowEMA
    1
else if fastEMA < slowEMA * 0.95
    -2
else if fastEMA < slowEMA
    -1
else
    0

// 动量（MACD）
[macdLine, signalLine, _] = ta.macd(close, fastLength, slowLength, signalLength)
macdHist = macdLine - signalLine
momState = if macdLine > signalLine * 1.1
    2
else if macdLine > signalLine
    1
else if macdLine < signalLine * 0.9
    -2
else if macdLine < signalLine
    -1
else
    0

// DM（平均趋向指数）
[diPlus, diMinus, adx] = ta.dmi(adxPeriod, adxPeriod)
dmState = if adx > adxThreshold * 1.2
    if diPlus > diMinus
        2
    else
        -2
else if adx > adxThreshold
    if diPlus > diMinus
        1
    else
        -1
else
    0

// 随机指标
stoK = ta.sma(ta.stoch(close, high, low, stoPeriod), 3)
stoState = if stoK > 80
    2
else if stoK > 60
    1
else if stoK < 20
    -2
else if stoK < 40
    -1
else
    0

// 分形
upperFractal = ta.highestbars(high, fractalPeriod) == 0
lowerFractal = ta.lowestbars(low, fractalPeriod) == 0
fractalState = if upperFractal and high[2] > high[3] * 1.01
    2
else if upperFractal
    1
else if lowerFractal and low[2] < low[3] * 0.99
    -2
else if lowerFractal
    -1
else
    0

// OBV指标
obv = ta.obv
obv_ema_short = ta.ema(obv, obv_short)
obv_ema_medium = ta.ema(obv, obv_medium)
obv_ema_long = ta.ema(obv, obv_long)
obvState = if obv_ema_short > obv_ema_medium * 1.02 and obv_ema_medium > obv_ema_long * 1.02
    2
else if obv_ema_short > obv_ema_medium and obv_ema_medium > obv_ema_long
    1
else if obv_ema_short < obv_ema_medium * 0.98 and obv_ema_medium < obv_ema_long * 0.98
    -2
else if obv_ema_short < obv_ema_medium and obv_ema_medium < obv_ema_long
    -1
else
    0

// 比较时间周期计算
trendState_tf = request.security(syminfo.tickerid, comparisonTF, trendState)
momState_tf = request.security(syminfo.tickerid, comparisonTF, momState)
dmState_tf = request.security(syminfo.tickerid, comparisonTF, dmState)
stoState_tf = request.security(syminfo.tickerid, comparisonTF, stoState)
fractalState_tf = request.security(syminfo.tickerid, comparisonTF, fractalState)
obvState_tf = request.security(syminfo.tickerid, comparisonTF, obvState)

// 检查所有支柱是否一致的函数
allPillarsAgree(targetState) =>
    trendState == targetState and momState == targetState and dmState == targetState and stoState == targetState and fractalState == targetState and obvState == targetState

// 强度计算
strengthMeter() =>
    // 计算当前时间周期的趋势方向
    current_direction = math.sign(trendState + momState + dmState + stoState + fractalState + obvState)
    // 计算比较时间周期的趋势方向
    alternate_direction = math.sign(trendState_tf + momState_tf + dmState_tf + stoState_tf + fractalState_tf + obvState_tf)
    
    // 统计同向的指标数量
    current_aligned = (math.sign(trendState) == current_direction ? 1 : 0) + (math.sign(momState) == current_direction ? 1 : 0) + (math.sign(dmState) == current_direction ? 1 : 0) + (math.sign(stoState) == current_direction ? 1 : 0) + (math.sign(fractalState) == current_direction ? 1 : 0) + (math.sign(obvState) == current_direction ? 1 : 0)
    alternate_aligned = (math.sign(trendState_tf) == current_direction ? 1 : 0) + (math.sign(momState_tf) == current_direction ? 1 : 0) + (math.sign(dmState_tf) == current_direction ? 1 : 0) + (math.sign(stoState_tf) == current_direction ? 1 : 0) + (math.sign(fractalState_tf) == current_direction ? 1 : 0) + (math.sign(obvState_tf) == current_direction ? 1 : 0)
    
    // 计算同向指标的平均强度（归一化到0-1范围）
    current_strength = (math.abs(trendState) + math.abs(momState) + math.abs(dmState) + math.abs(stoState) + math.abs(fractalState) + math.abs(obvState)) / 12
    alternate_strength = (math.abs(trendState_tf) + math.abs(momState_tf) + math.abs(dmState_tf) + math.abs(stoState_tf) + math.abs(fractalState_tf) + math.abs(obvState_tf)) / 12
    
    // 计算总对齐率（0-1范围）
    alignment_rate = (current_aligned + alternate_aligned) / 12
    
    // 如果两个时间周期的趋势方向不一致，降低分数
    direction_penalty = current_direction == alternate_direction ? 1.0 : 0.5
    
    // 计算最终得分：对齐率 * 平均强度 * 方向惩罚系数 * 100
    avg_strength = (current_strength + alternate_strength) / 2
    strength = alignment_rate * avg_strength * direction_penalty * 100
    
    // 确保分数在0-100之间
    math.max(0, math.min(100, math.round(strength)))

// === 表格样式变量 ===
var color tableBgColor = color.new(color.black, bgTransparency)    // 用户设置的透明度
headerTextColor = color.white
bullishColor = #22C55E
neutralColor = #3B82F6
bearishColor = #EF4444

spacingHeight = 0.5

// 根据支柱一致性确定蜡烛颜色
bullishCandle = allPillarsAgree(2) or allPillarsAgree(1)
bearishCandle = allPillarsAgree(-2) or allPillarsAgree(-1)

barcolor(bullishCandle ? bullishColor : bearishCandle ? bearishColor : na)

// === 表格显示 ===
// 计算总和
currentSum = trendState + momState + dmState + stoState + fractalState + obvState
tfSum = trendState_tf + momState_tf + dmState_tf + stoState_tf + fractalState_tf + obvState_tf

// 获取当前和比较时间周期的趋势描述
[currentTrend, currentDetails] = get_timeframe_trend(currentSum, trendState, momState, dmState, stoState, fractalState, obvState)
[tfTrend, tfDetails] = get_timeframe_trend(tfSum, trendState_tf, momState_tf, dmState_tf, stoState_tf, fractalState_tf, obvState_tf)

// 计算强度得分
strength = strengthMeter()

// 表格标题
var table myTable = table.new(position.top_right, 2, 10, tableBgColor, color.gray, 1, color.gray, 1)

// 更新表格
if barstate.islast
    // 清除表格
    table.clear(myTable, 0, 0, 1, 9)
    
    // 标题行
    table.cell(myTable, 0, 0, "六个支柱分析", text_color=headerTextColor, text_size=size.normal)
    table.cell(myTable, 1, 0, timeframe.period, text_color=headerTextColor, text_size=size.normal)
    
    // 当前时间周期趋势
    table.cell(myTable, 0, 1, "当前趋势", text_color=color.white)
    table.cell(myTable, 1, 1, currentTrend + " " + currentDetails, text_color = currentSum > 0 ? color.green : currentSum < 0 ? color.red : color.gray)
    
    // 比较时间周期趋势
    table.cell(myTable, 0, 2, comparisonTF + "趋势", text_color=color.white)
    table.cell(myTable, 1, 2, tfTrend + " " + tfDetails, text_color = tfSum > 0 ? color.green : tfSum < 0 ? color.red : color.gray)
    
    // 强度得分
    table.cell(myTable, 0, 3, "强度得分", text_color=color.white)
    table.cell(myTable, 1, 3, str.tostring(strength) + "%", text_color = strength > 70 ? color.green : strength > 30 ? color.orange : color.red)
    
    // 各个指标状态
    table.cell(myTable, 0, 4, "趋势状态", text_color=color.white)
    table.cell(myTable, 1, 4, str.tostring(trendState), text_color = trendState > 0 ? color.green : trendState < 0 ? color.red : color.gray)
    
    table.cell(myTable, 0, 5, "动量状态", text_color=color.white)
    table.cell(myTable, 1, 5, str.tostring(momState), text_color = momState > 0 ? color.green : momState < 0 ? color.red : color.gray)
    
    table.cell(myTable, 0, 6, "趋势强度", text_color=color.white)
    table.cell(myTable, 1, 6, str.tostring(dmState), text_color = dmState > 0 ? color.green : dmState < 0 ? color.red : color.gray)
    
    table.cell(myTable, 0, 7, "超买超卖", text_color=color.white)
    table.cell(myTable, 1, 7, str.tostring(stoState), text_color = stoState > 0 ? color.green : stoState < 0 ? color.red : color.gray)
    
    table.cell(myTable, 0, 8, "分形状态", text_color=color.white)
    table.cell(myTable, 1, 8, str.tostring(fractalState), text_color = fractalState > 0 ? color.green : fractalState < 0 ? color.red : color.gray)
    
    table.cell(myTable, 0, 9, "资金流向", text_color=color.white)
    table.cell(myTable, 1, 9, str.tostring(obvState), text_color = obvState > 0 ? color.green : obvState < 0 ? color.red : color.gray)