//@version=5
indicator("MCSI RSI Test", overlay=false)

// === RSI相关函数和变量 ===
domcycle = input.int(14, title="Dominant Cycle Length", minval=10)
cyclelen = domcycle / 2
vibration = 10
torque = 2.0 / (vibration + 1)
phasingLag = (vibration - 1) / 2.0
leveling = 10.0
cyclicmemory = domcycle * 2

calculate_crsi(src) =>
    var crsi = 0.0
    up = ta.rma(math.max(ta.change(src), 0), cyclelen)
    down = ta.rma(-math.min(ta.change(src), 0), cyclelen)
    rsi = down == 0 ? 100 : up == 0 ? 0 : 100 - 100 / (1 + up / down)
    crsi := torque * (2 * rsi - rsi[phasingLag]) + (1 - torque) * nz(crsi[1], 0)

    var lmax = -999999.0
    var lmin = 999999.0
    lmax := math.max(nz(crsi, lmax), lmax)
    lmin := math.min(nz(crsi, lmin), lmin)

    var db = 0.0
    var ub = 0.0
    mstep = (lmax - lmin) / 100
    aperc = leveling / 100

    for steps = 0 to 100
        testvalue = lmin + mstep * steps
        below = 0.0
        for i = 0 to cyclicmemory - 1
            below := below + (crsi[i] < testvalue ? 1 : 0)
        if below / cyclicmemory >= aperc
            db := testvalue
            break

    for steps = 0 to 100
        testvalue = lmax - mstep * steps
        above = 0.0
        for i = 0 to cyclicmemory - 1
            above := above + (crsi[i] >= testvalue ? 1 : 0)
        if above / cyclicmemory >= aperc
            ub := testvalue
            break

    [crsi, db, ub]

// 获取RSI相关数据
// 当前时间框架的RSI
[crsi_daily, db_daily, ub_daily] = calculate_crsi(close)
// 周线RSI
[crsi_weekly, db_weekly, ub_weekly] = request.security(syminfo.tickerid, "W", calculate_crsi(close))

// === 日线信号判断 ===
var string dailySignalType = "neutral"

// 日线多头信号判断
if crsi_daily < db_daily
    if crsi_daily > crsi_daily[1]  // 上升趋势
        if crsi_daily[1] < db_daily[1] and crsi_daily > db_daily[1]
            dailySignalType := "strongest_bull"  // 最强日线转多信号（突破后上升确认）
        else
            dailySignalType := "rising_bull"     // 上升中的转多信号
    else
        dailySignalType := "basic_bull"      // 基础转多信号
else if crsi_daily > ub_daily
    if crsi_daily < crsi_daily[1]  // 下降趋势
        if crsi_daily[1] > ub_daily[1] and crsi_daily < ub_daily[1]
            dailySignalType := "strongest_bear"  // 最强日线转空信号（突破后下降确认）
        else
            dailySignalType := "falling_bear"    // 下降中的转空信号
    else
        dailySignalType := "basic_bear"      // 基础转空信号
else
    dailySignalType := "neutral"           // 中性

// === 周线信号判断 ===
var string weeklySignalType = "neutral"

if crsi_weekly < db_weekly
    if crsi_weekly > crsi_weekly[1]  // 上升趋势
        if crsi_weekly[1] < db_weekly[1]
            weeklySignalType := "confirmed_bull"  // 转多反转确认信号
        else
            weeklySignalType := "rising_bull"     // 上升中的多头信号
    else
        weeklySignalType := "basic_bull"      // 基础多头信号
else if crsi_weekly > ub_weekly
    if crsi_weekly < crsi_weekly[1]  // 下降趋势
        if crsi_weekly[1] > ub_weekly[1]
            weeklySignalType := "confirmed_bear"  // 转空反转确认信号
        else
            weeklySignalType := "falling_bear"    // 下降中的空头信号
    else
        weeklySignalType := "basic_bear"      // 基础空头信号
else
    weeklySignalType := "neutral"           // 中性

// === 绘制信号 ===
// 判断周线状态
isWeeklyBullish = crsi_weekly < db_weekly  // 周线在下轨之下
isWeeklyBearish = crsi_weekly > ub_weekly  // 周线在上轨之上

// 日线信号背景色
bgcolor(
     // 相交信号 - 根据周线状态调整透明度
     crsi_daily[1] < db_daily[1] and crsi_daily > db_daily ? 
         (isWeeklyBullish ? color.new(color.green, 30) : color.new(color.green, 70)) :    // 突破下轨
     crsi_daily[1] > ub_daily[1] and crsi_daily < ub_daily ? 
         (isWeeklyBearish ? color.new(color.red, 30) : color.new(color.red, 70)) :        // 跌破上轨
     
     // 多头信号 - 根据周线状态调整透明度
     dailySignalType == "strongest_bull" ? 
         (isWeeklyBullish ? color.new(color.green, 50) : color.new(color.green, 80)) :    // 最强多头确认
     dailySignalType == "rising_bull" ? 
         (isWeeklyBullish ? color.new(color.lime, 65) : color.new(color.lime, 85)) :      // 上升中的多头
     dailySignalType == "basic_bull" ? 
         (isWeeklyBullish ? color.new(color.lime, 80) : color.new(color.lime, 93)) :      // 基础多头
     
     // 空头信号 - 根据周线状态调整透明度
     dailySignalType == "strongest_bear" ? 
         (isWeeklyBearish ? color.new(color.red, 50) : color.new(color.red, 80)) :        // 最强空头确认
     dailySignalType == "falling_bear" ? 
         (isWeeklyBearish ? color.new(color.maroon, 65) : color.new(color.maroon, 85)) :  // 下降中的空头
     dailySignalType == "basic_bear" ? 
         (isWeeklyBearish ? color.new(color.maroon, 80) : color.new(color.maroon, 93)) :  // 基础空头
     na)

// 周线状态显示 - 在轨道外的状态
plotshape(crsi_weekly < db_weekly, "周线在下轨之下", shape.circle, location.bottom, color.green, 0, size=size.tiny)
plotshape(crsi_weekly > ub_weekly, "周线在上轨之上", shape.circle, location.top, color.red, 0, size=size.tiny)

// 周线相交点标记
plotshape(crsi_weekly[1] < db_weekly[1] and crsi_weekly > db_weekly, "周线突破下轨", shape.triangleup, location.bottom, color.green, 0, size=size.small)
plotshape(crsi_weekly[1] > ub_weekly[1] and crsi_weekly < ub_weekly, "周线跌破上轨", shape.triangledown, location.top, color.red, 0, size=size.small)

// === 绘制RSI线和轨道 ===
// 当前时间框架RSI - 红色细线
plot(crsi_daily, "当前时间框架RSI", color=color.red, linewidth=1)
// 当前时间框架轨道 - 蓝色细线
plot(db_daily, "当前时间框架下轨", color=color.blue, linewidth=1)
plot(ub_daily, "当前时间框架上轨", color=color.blue, linewidth=1)

// 周线RSI - 红色粗线
plot(crsi_weekly, "周线RSI", color=color.red, linewidth=3)
// 周线轨道 - 蓝色粗线
plot(db_weekly, "周线下轨", color=color.blue, linewidth=2)
plot(ub_weekly, "周线上轨", color=color.blue, linewidth=2)

// === 显示调试信息 ===
var label debugLabel = label.new(bar_index, 0, "", xloc.bar_index, yloc.price, color.white, label.style_none, color.white, size.normal, text.align_left)

if barstate.islast
    label.set_xy(debugLabel, bar_index + 5, 50)
    label.set_text(debugLabel, 
                  "当前时间框架信号: " + dailySignalType + "\n" +
                  "周线信号: " + weeklySignalType + "\n" +
                  "当前RSI: " + str.tostring(crsi_daily, "#.##") + "\n" +
                  "当前下轨: " + str.tostring(db_daily, "#.##") + "\n" +
                  "当前上轨: " + str.tostring(ub_daily, "#.##") + "\n" +
                  "周RSI: " + str.tostring(crsi_weekly, "#.##") + "\n" +
                  "周下轨: " + str.tostring(db_weekly, "#.##") + "\n" +
                  "周上轨: " + str.tostring(ub_weekly, "#.##"))