//@version=5
indicator(title="RSI cyclic smoothed with Divergence", shorttitle="cRSI with Div", overlay=false)

// === 输入参数 ===
domcycle = input.int(14, minval=10, title="Dominant Cycle Length")
cyclelen = domcycle / 2
vibration = 10
torque = 2.0 / (vibration + 1)
phasingLag = (vibration - 1) / 2.0
leveling = 10.0
cyclicmemory = domcycle * 2

// === 背离检测参数 ===
showDivergences = input.bool(false, title="启用背离检测")
lbR = input.int(title="Pivot Lookback Right", defval=5)
lbL = input.int(title="Pivot Lookback Left", defval=5)
rangeUpper = input.int(title="Max of Lookback Range", defval=60)
rangeLower = input.int(title="Min of Lookback Range", defval=5)
plotBull = input.bool(title="绘制看涨背离", defval=true)
plotHiddenBull = input.bool(title="绘制隐藏看涨背离", defval=true)
plotBear = input.bool(title="绘制看跌背离", defval=true)
plotHiddenBear = input.bool(title="绘制隐藏看跌背离", defval=true)

// === RSI计算函数 ===
calculate_crsi(src) =>
    var crsi = 0.0
    up = ta.rma(math.max(ta.change(src), 0), cyclelen)
    down = ta.rma(-math.min(ta.change(src), 0), cyclelen)
    rsi = down == 0 ? 100 : up == 0 ? 0 : 100 - 100 / (1 + up / down)
    crsi := torque * (2 * rsi - rsi[phasingLag]) + (1 - torque) * nz(crsi[1], 0)

    var lmax = -999999.0
    var lmin = 999999.0
    lmax := math.max(nz(crsi, lmax), lmax)
    lmin := math.min(nz(crsi, lmin), lmin)

    var db = 0.0
    var ub = 0.0
    mstep = (lmax - lmin) / 100
    aperc = leveling / 100

    for steps = 0 to 100
        testvalue = lmin + mstep * steps
        below = 0.0
        for i = 0 to cyclicmemory - 1
            below := below + (crsi[i] < testvalue ? 1 : 0)
        if below / cyclicmemory >= aperc
            db := testvalue
            break

    for steps = 0 to 100
        testvalue = lmax - mstep * steps
        above = 0.0
        for i = 0 to cyclicmemory - 1
            above := above + (crsi[i] >= testvalue ? 1 : 0)
        if above / cyclicmemory >= aperc
            ub := testvalue
            break

    [crsi, db, ub]

// === 获取RSI数据 ===
[crsi_daily, db_daily, ub_daily] = calculate_crsi(close)
[crsi_weekly, db_weekly, ub_weekly] = request.security(syminfo.tickerid, "W", calculate_crsi(close))

// === 信号判断 ===
// 当前时间框架信号判断
var string dailySignalType = "neutral"

if crsi_daily < db_daily
    if crsi_daily > crsi_daily[1]
        if crsi_daily[1] < db_daily[1] and crsi_daily > db_daily[1]
            dailySignalType := "strongest_bull"
        else
            dailySignalType := "rising_bull"
    else
        dailySignalType := "basic_bull"
else if crsi_daily > ub_daily
    if crsi_daily < crsi_daily[1]
        if crsi_daily[1] > ub_daily[1] and crsi_daily < ub_daily[1]
            dailySignalType := "strongest_bear"
        else
            dailySignalType := "falling_bear"
    else
        dailySignalType := "basic_bear"
else
    dailySignalType := "neutral"

// 周线状态判断
isWeeklyBullish = crsi_weekly < db_weekly
isWeeklyBearish = crsi_weekly > ub_weekly

// === 绘制信号 ===
// 信号背景色
bgcolor(
     crsi_daily[1] < db_daily[1] and crsi_daily > db_daily ? 
         (isWeeklyBullish ? color.new(color.green, 30) : color.new(color.green, 70)) :
     crsi_daily[1] > ub_daily[1] and crsi_daily < ub_daily ? 
         (isWeeklyBearish ? color.new(color.red, 30) : color.new(color.red, 70)) :
     
     dailySignalType == "strongest_bull" ? 
         (isWeeklyBullish ? color.new(color.green, 50) : color.new(color.green, 80)) :
     dailySignalType == "rising_bull" ? 
         (isWeeklyBullish ? color.new(color.lime, 65) : color.new(color.lime, 85)) :
     dailySignalType == "basic_bull" ? 
         (isWeeklyBullish ? color.new(color.lime, 80) : color.new(color.lime, 93)) :
     
     dailySignalType == "strongest_bear" ? 
         (isWeeklyBearish ? color.new(color.red, 50) : color.new(color.red, 80)) :
     dailySignalType == "falling_bear" ? 
         (isWeeklyBearish ? color.new(color.maroon, 65) : color.new(color.maroon, 85)) :
     dailySignalType == "basic_bear" ? 
         (isWeeklyBearish ? color.new(color.maroon, 80) : color.new(color.maroon, 93)) :
     na)

// 周线状态显示
plotshape(crsi_weekly < db_weekly, "周线在下轨之下", shape.circle, location.bottom, color.green, 0, size=size.tiny)
plotshape(crsi_weekly > ub_weekly, "周线在上轨之上", shape.circle, location.top, color.red, 0, size=size.tiny)

// 周线相交点标记
plotshape(crsi_weekly[1] < db_weekly[1] and crsi_weekly > db_weekly, "周线突破下轨", shape.triangleup, location.bottom, color.green, 0, size=size.small)
plotshape(crsi_weekly[1] > ub_weekly[1] and crsi_weekly < ub_weekly, "周线跌破上轨", shape.triangledown, location.top, color.red, 0, size=size.small)

// === 绘制RSI线和轨道 ===
plot(crsi_daily, "当前时间框架RSI", color=color.red, linewidth=1)
plot(db_daily, "当前时间框架下轨", color=color.blue, linewidth=1)
plot(ub_daily, "当前时间框架上轨", color=color.blue, linewidth=1)
plot(crsi_weekly, "周线RSI", color=color.red, linewidth=3)
plot(db_weekly, "周线下轨", color=color.blue, linewidth=2)
plot(ub_weekly, "周线上轨", color=color.blue, linewidth=2)

// === 背离检测逻辑 ===
// 使用振荡器
osc = crsi_daily

// 寻找枢轴点
plFound = na(ta.pivotlow(osc, lbL, lbR)) ? false : true
phFound = na(ta.pivothigh(osc, lbL, lbR)) ? false : true

_inRange(cond) =>
    bars = ta.barssince(cond == true)
    rangeLower <= bars and bars <= rangeUpper

// 计算背离条件
// 常规看涨背离
oscHL = osc[lbR] > ta.valuewhen(plFound, osc[lbR], 1) and _inRange(plFound[1])
priceLL = low[lbR] < ta.valuewhen(plFound, low[lbR], 1)
bullCond = showDivergences and plotBull and priceLL and oscHL and plFound

// 隐藏看涨背离
oscLL = osc[lbR] < ta.valuewhen(plFound, osc[lbR], 1) and _inRange(plFound[1])
priceHL = low[lbR] > ta.valuewhen(plFound, low[lbR], 1)
hiddenBullCond = showDivergences and plotHiddenBull and priceHL and oscLL and plFound

// 常规看跌背离
oscLH = osc[lbR] < ta.valuewhen(phFound, osc[lbR], 1) and _inRange(phFound[1])
priceHH = high[lbR] > ta.valuewhen(phFound, high[lbR], 1)
bearCond = showDivergences and plotBear and priceHH and oscLH and phFound

// 隐藏看跌背离
oscHH = osc[lbR] > ta.valuewhen(phFound, osc[lbR], 1) and _inRange(phFound[1])
priceLH = high[lbR] < ta.valuewhen(phFound, high[lbR], 1)
hiddenBearCond = showDivergences and plotHiddenBear and priceLH and oscHH and phFound

// === 绘制背离 ===
// 常规看涨背离
plot(bullCond ? osc[lbR] : na, offset=-lbR, title="常规看涨背离", linewidth=1, color=color.new(color.silver, 0))
plotshape(bullCond ? osc[lbR] : na, "看涨背离标记", shape.circle, location.absolute, color.green, size=size.tiny, offset=-lbR)

// 隐藏看涨背离
plot(hiddenBullCond ? osc[lbR] : na, offset=-lbR, title="隐藏看涨背离", linewidth=1, color=color.new(color.silver, 0))
plotshape(hiddenBullCond ? osc[lbR] : na, "隐藏看涨背离标记", shape.circle, location.absolute, color.new(color.green, 50), size=size.tiny, offset=-lbR)

// 常规看跌背离
plot(bearCond ? osc[lbR] : na, offset=-lbR, title="常规看跌背离", linewidth=1, color=color.new(color.silver, 0))
plotshape(bearCond ? osc[lbR] : na, "看跌背离标记", shape.circle, location.absolute, color.red, size=size.tiny, offset=-lbR)

// 隐藏看跌背离
plot(hiddenBearCond ? osc[lbR] : na, offset=-lbR, title="隐藏看跌背离", linewidth=2, color=color.new(color.silver, 0))
plotshape(hiddenBearCond ? osc[lbR] : na, "隐藏看跌背离标记", shape.circle, location.absolute, color.new(color.red, 50), size=size.tiny, offset=-lbR)
