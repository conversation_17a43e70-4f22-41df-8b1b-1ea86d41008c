#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCSI RSI指标
从Pine Script转换为Python实现
"""

import numpy as np
from typing import Dict, List, Tuple
import logging

class MCSIRSIIndicator:
    """MCSI RSI技术指标"""
    
    def __init__(self, dom_cycle=14, vibration=10, leveling=10.0):
        """
        初始化MCSI RSI指标
        
        Args:
            dom_cycle: 主导周期长度，默认14
            vibration: 振动参数，默认10
            leveling: 水平化参数，默认10.0
        """
        self.dom_cycle = dom_cycle
        self.cycle_len = dom_cycle // 2
        self.vibration = vibration
        self.torque = 2.0 / (vibration + 1)
        self.phasing_lag = (vibration - 1) / 2.0
        self.leveling = leveling
        self.cyclic_memory = dom_cycle * 2
        self.logger = logging.getLogger(__name__)

        
    def calculate_rma(self, data: np.ndarray, period: int) -> np.ndarray:
        """计算RMA (<PERSON>'s移动平均)"""
        if len(data) < period:
            return np.full(len(data), np.nan)
            
        alpha = 1.0 / period
        rma = np.zeros(len(data))
        
        # 初始值使用SMA
        rma[period-1] = np.mean(data[:period])
        
        # 后续使用RMA公式
        for i in range(period, len(data)):
            rma[i] = alpha * data[i] + (1 - alpha) * rma[i-1]
            
        return rma
    
    def calculate_crsi(self, close_prices: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """计算CRSI和动态轨道"""
        # 计算价格变化
        changes = np.diff(close_prices, prepend=close_prices[0])
        
        # 计算上涨和下跌
        ups = np.maximum(changes, 0)
        downs = np.maximum(-changes, 0)
        
        # 计算RMA
        up_rma = self.calculate_rma(ups, self.cycle_len)
        down_rma = self.calculate_rma(downs, self.cycle_len)
        
        # 计算RSI
        rsi = np.zeros(len(close_prices))
        for i in range(len(close_prices)):
            if down_rma[i] == 0:
                rsi[i] = 100
            elif up_rma[i] == 0:
                rsi[i] = 0
            else:
                rsi[i] = 100 - 100 / (1 + up_rma[i] / down_rma[i])
        
        # 计算CRSI
        crsi = np.zeros(len(close_prices))
        for i in range(len(close_prices)):
            phasing_idx = max(0, i - int(self.phasing_lag))
            rsi_phased = 2 * rsi[i] - rsi[phasing_idx]
            
            if i == 0:
                crsi[i] = rsi_phased
            else:
                crsi[i] = self.torque * rsi_phased + (1 - self.torque) * crsi[i-1]
        
        # 计算动态轨道
        db, ub = self._calculate_dynamic_bands(crsi)
        
        return crsi, db, ub
    
    def _calculate_dynamic_bands(self, crsi: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """计算动态轨道（严格按照Pine Script逻辑）"""
        db = np.full(len(crsi), np.nan)
        ub = np.full(len(crsi), np.nan)

        # 全局最大最小值（按照Pine Script的var lmax/lmin逻辑）
        lmax = -999999.0
        lmin = 999999.0

        for i in range(len(crsi)):
            if not np.isnan(crsi[i]):
                # 更新全局极值（对应Pine Script的lmax := math.max(nz(crsi, lmax), lmax)）
                lmax = max(crsi[i], lmax)
                lmin = min(crsi[i], lmin)

            if i >= self.cyclic_memory - 1:
                # 防止除零错误
                if lmax == lmin:
                    db[i] = lmin
                    ub[i] = lmax
                    continue

                # 步长计算（对应Pine Script的mstep = (lmax - lmin) / 100）
                mstep = (lmax - lmin) / 100
                aperc = self.leveling / 100

                # 获取最近cyclicmemory周期的数据
                start_idx = max(0, i - self.cyclic_memory + 1)
                period_data = crsi[start_idx:i+1]
                period_data = period_data[~np.isnan(period_data)]

                if len(period_data) == 0:
                    continue

                # 计算下轨（对应Pine Script的下轨计算逻辑）
                for steps in range(101):
                    testvalue = lmin + mstep * steps
                    below_count = np.sum(period_data < testvalue)
                    if below_count / len(period_data) >= aperc:
                        db[i] = testvalue
                        break

                # 计算上轨（对应Pine Script的上轨计算逻辑）
                for steps in range(101):
                    testvalue = lmax - mstep * steps
                    above_count = np.sum(period_data >= testvalue)
                    if above_count / len(period_data) >= aperc:
                        ub[i] = testvalue
                        break

        return db, ub
    
    def calculate_weekly_data(self, close_prices: np.ndarray, period_ratio: int = 5) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """模拟周线数据计算（每5个交易日为一周）"""
        # 重采样为周线数据
        weekly_closes = []
        for i in range(0, len(close_prices), period_ratio):
            week_data = close_prices[i:i+period_ratio]
            if len(week_data) > 0:
                weekly_closes.append(week_data[-1])  # 使用周末收盘价
        
        weekly_closes = np.array(weekly_closes)
        
        # 计算周线CRSI
        weekly_crsi, weekly_db, weekly_ub = self.calculate_crsi(weekly_closes)
        
        # 扩展回日线长度
        daily_weekly_crsi = np.full(len(close_prices), np.nan)
        daily_weekly_db = np.full(len(close_prices), np.nan)
        daily_weekly_ub = np.full(len(close_prices), np.nan)
        
        for i in range(len(close_prices)):
            week_idx = min(i // period_ratio, len(weekly_crsi) - 1)
            if week_idx >= 0:
                daily_weekly_crsi[i] = weekly_crsi[week_idx]
                daily_weekly_db[i] = weekly_db[week_idx]
                daily_weekly_ub[i] = weekly_ub[week_idx]
        
        return daily_weekly_crsi, daily_weekly_db, daily_weekly_ub

    def calculate_weekly_data_enhanced(self, data_dict: Dict) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        使用真实周线数据计算周线CRSI（增强版）

        Args:
            data_dict: 包含完整OHLC和日期数据的字典

        Returns:
            (周线CRSI, 周线下轨, 周线上轨)
        """
        try:
            # 检查是否有真实周线数据
            if 'weekly_data' in data_dict and data_dict['weekly_data'] is not None:
                weekly_data = data_dict['weekly_data']

                # 验证周线数据的有效性
                if (isinstance(weekly_data, dict) and
                    'close' in weekly_data and
                    len(weekly_data['close']) > 0):

                    self.logger.info(f"使用真实周线数据: {len(weekly_data['close'])}条")

                    # 使用真实周线数据计算CRSI
                    weekly_close = np.array(weekly_data['close'])

                    # 确保周线数据足够长
                    if len(weekly_close) < self.dom_cycle:
                        self.logger.warning(f"周线数据不足: 需要{self.dom_cycle}条，实际{len(weekly_close)}条，回退到模拟数据")
                        return self.calculate_weekly_data(np.array(data_dict['close']))

                    # 计算周线CRSI
                    crsi_weekly_raw, db_weekly_raw, ub_weekly_raw = self.calculate_crsi(weekly_close)

                    # 将周线数据映射回日线长度
                    daily_length = len(data_dict['close'])
                    crsi_weekly = self._map_weekly_to_daily_simple(crsi_weekly_raw, daily_length)
                    db_weekly = self._map_weekly_to_daily_simple(db_weekly_raw, daily_length)
                    ub_weekly = self._map_weekly_to_daily_simple(ub_weekly_raw, daily_length)

                    self.logger.info(f"真实周线数据计算完成: {len(weekly_close)}周 -> {daily_length}日")
                    return crsi_weekly, db_weekly, ub_weekly
                else:
                    self.logger.warning("周线数据格式无效，使用模拟周线数据")
                    return self.calculate_weekly_data(np.array(data_dict['close']))
            else:
                self.logger.debug("未提供真实周线数据，使用模拟周线数据")
                return self.calculate_weekly_data(np.array(data_dict['close']))

        except Exception as e:
            self.logger.error(f"真实周线数据计算失败: {e}")
            # 回退到模拟周线数据
            return self.calculate_weekly_data(np.array(data_dict['close']))
    
    def _map_weekly_to_daily_simple(self, weekly_values: np.ndarray, daily_length: int) -> np.ndarray:
        """
        改进的周线到日线映射（确保不产生全NaN结果）

        Args:
            weekly_values: 周线数据数组
            daily_length: 日线数据长度

        Returns:
            映射后的日线数据数组
        """
        if len(weekly_values) == 0:
            # 如果没有周线数据，返回全0而不是全NaN
            self.logger.warning("周线数据为空，返回全0数组")
            return np.zeros(daily_length)

        # 过滤掉NaN值
        valid_weekly = weekly_values[~np.isnan(weekly_values)]
        if len(valid_weekly) == 0:
            # 如果所有周线数据都是NaN，返回全0
            self.logger.warning("周线数据全为NaN，返回全0数组")
            return np.zeros(daily_length)

        # 简化策略：将周线数据均匀分布到日线
        daily_values = np.full(daily_length, np.nan)

        # 计算每个周线点对应的日线范围
        days_per_week = max(1, daily_length // len(weekly_values))

        for i, weekly_val in enumerate(weekly_values):
            if not np.isnan(weekly_val):
                start_idx = i * days_per_week
                end_idx = min((i + 1) * days_per_week, daily_length)
                daily_values[start_idx:end_idx] = weekly_val

        # 前向填充空值
        last_valid_value = None
        for i in range(len(daily_values)):
            if not np.isnan(daily_values[i]):
                last_valid_value = daily_values[i]
            elif last_valid_value is not None:
                daily_values[i] = last_valid_value

        # 如果开头有NaN，用第一个有效值填充
        first_valid_idx = np.where(~np.isnan(daily_values))[0]
        if len(first_valid_idx) > 0:
            first_valid_value = daily_values[first_valid_idx[0]]
            daily_values[:first_valid_idx[0]] = first_valid_value
        else:
            # 如果仍然全是NaN，使用周线数据的平均值
            mean_weekly = np.nanmean(weekly_values)
            if not np.isnan(mean_weekly):
                daily_values[:] = mean_weekly
            else:
                daily_values[:] = 0.0

        return daily_values

    def calculate_signals(self, crsi_daily: np.ndarray, db_daily: np.ndarray, ub_daily: np.ndarray,
                         crsi_weekly: np.ndarray, db_weekly: np.ndarray, ub_weekly: np.ndarray) -> Dict:
        """计算信号类型"""
        daily_signals = []
        weekly_signals = []
        
        for i in range(1, len(crsi_daily)):
            # 日线信号判断
            daily_signal = "neutral"
            
            if not np.isnan(db_daily[i]) and crsi_daily[i] < db_daily[i]:
                if crsi_daily[i] > crsi_daily[i-1]:  # 上升趋势
                    if (not np.isnan(db_daily[i-1]) and 
                        crsi_daily[i-1] < db_daily[i-1] and crsi_daily[i] > db_daily[i-1]):
                        daily_signal = "strongest_bull"
                    else:
                        daily_signal = "rising_bull"
                else:
                    daily_signal = "basic_bull"
            elif not np.isnan(ub_daily[i]) and crsi_daily[i] > ub_daily[i]:
                if crsi_daily[i] < crsi_daily[i-1]:  # 下降趋势
                    if (not np.isnan(ub_daily[i-1]) and 
                        crsi_daily[i-1] > ub_daily[i-1] and crsi_daily[i] < ub_daily[i-1]):
                        daily_signal = "strongest_bear"
                    else:
                        daily_signal = "falling_bear"
                else:
                    daily_signal = "basic_bear"
            
            daily_signals.append(daily_signal)
            
            # 周线信号判断
            weekly_signal = "neutral"
            
            if not np.isnan(db_weekly[i]) and crsi_weekly[i] < db_weekly[i]:
                if crsi_weekly[i] > crsi_weekly[i-1]:  # 上升趋势
                    if (not np.isnan(db_weekly[i-1]) and 
                        crsi_weekly[i-1] < db_weekly[i-1]):
                        weekly_signal = "confirmed_bull"
                    else:
                        weekly_signal = "rising_bull"
                else:
                    weekly_signal = "basic_bull"
            elif not np.isnan(ub_weekly[i]) and crsi_weekly[i] > ub_weekly[i]:
                if crsi_weekly[i] < crsi_weekly[i-1]:  # 下降趋势
                    if (not np.isnan(ub_weekly[i-1]) and 
                        crsi_weekly[i-1] > ub_weekly[i-1]):
                        weekly_signal = "confirmed_bear"
                    else:
                        weekly_signal = "falling_bear"
                else:
                    weekly_signal = "basic_bear"
            
            weekly_signals.append(weekly_signal)
        
        # 在开头添加第一个元素
        daily_signals.insert(0, "neutral")
        weekly_signals.insert(0, "neutral")
        
        return {
            'daily_signals': daily_signals,
            'weekly_signals': weekly_signals
        }
    
    def calculate(self, data) -> Dict:
        """
        计算MCSI RSI指标

        Args:
            data: 包含OHLC数据的字典或收盘价数组（向后兼容）

        Returns:
            包含RSI相关数据的字典
        """
        try:
            # 向后兼容：如果传入的是数组，转换为字典格式
            if isinstance(data, np.ndarray):
                close_prices = data
                data_dict = {
                    'close': close_prices,
                    'open': close_prices,  # 简化处理
                    'high': close_prices,
                    'low': close_prices,
                    'date': [f"2024-01-{i+1:02d}" for i in range(len(close_prices))]  # 虚拟日期
                }
            else:
                close_prices = np.array(data['close'])
                data_dict = data

            # 数据验证（简化版）
            # WeeklyDataAggregator已删除，简化数据验证

            if len(close_prices) < max(self.dom_cycle, self.cyclic_memory):
                self.logger.warning("数据长度不足以计算MCSI RSI")
                return self._empty_result(len(close_prices))

            # 计算日线CRSI
            crsi_daily, db_daily, ub_daily = self.calculate_crsi(close_prices)

            # 尝试使用真实周线数据计算周线CRSI
            try:
                if isinstance(data, dict) and 'date' in data:
                    crsi_weekly, db_weekly, ub_weekly = self.calculate_weekly_data_enhanced(data_dict)
                    self.logger.debug("使用真实周线数据计算CRSI")
                else:
                    crsi_weekly, db_weekly, ub_weekly = self.calculate_weekly_data(close_prices)
                    self.logger.debug("使用简化周线数据计算CRSI")
            except Exception as e:
                self.logger.warning(f"周线数据计算失败，回退到原方法: {e}")
                crsi_weekly, db_weekly, ub_weekly = self.calculate_weekly_data(close_prices)
            
            # 计算信号
            signals = self.calculate_signals(
                crsi_daily, db_daily, ub_daily,
                crsi_weekly, db_weekly, ub_weekly
            )
            
            # 计算周线状态
            is_weekly_bullish = crsi_weekly < db_weekly
            is_weekly_bearish = crsi_weekly > ub_weekly

            # 计算RSI评分 (严格按照Pine Script逻辑)
            rsi_score = self._calculate_rsi_score(crsi_daily, db_daily, ub_daily, crsi_weekly, db_weekly, ub_weekly)

            # 计算背景色数据
            background_colors = self._calculate_background_colors(signals['daily_signals'], is_weekly_bullish, is_weekly_bearish)

            return {
                'crsi_daily': crsi_daily,
                'db_daily': db_daily,
                'ub_daily': ub_daily,
                'crsi_weekly': crsi_weekly,
                'db_weekly': db_weekly,
                'ub_weekly': ub_weekly,
                'daily_signals': signals['daily_signals'],
                'weekly_signals': signals['weekly_signals'],
                'is_weekly_bullish': is_weekly_bullish,
                'is_weekly_bearish': is_weekly_bearish,
                'rsi_score': rsi_score,
                'background_colors': background_colors
            }
            
        except Exception as e:
            self.logger.error(f"MCSI RSI计算失败: {str(e)}")
            return self._empty_result(len(close_prices))
    
    def _calculate_rsi_score(self, crsi_daily: np.ndarray, db_daily: np.ndarray, ub_daily: np.ndarray,
                           crsi_weekly: np.ndarray, db_weekly: np.ndarray, ub_weekly: np.ndarray) -> np.ndarray:
        """计算RSI评分 (严格按照Pine Script逻辑)"""
        length = len(crsi_daily)
        rsi_score = np.zeros(length)
        daily_score = np.zeros(length)
        weekly_score = np.zeros(length)
        signal_duration = np.zeros(length, dtype=int)
        weekly_signal_duration = np.zeros(length, dtype=int)

        for i in range(1, length):
            # 日线信号分数计算 (严格按照Pine Script逻辑)
            if (crsi_daily[i-1] < db_daily[i-1] and crsi_daily[i] > db_daily[i-1] and
                crsi_daily[i] > crsi_daily[i-1]):
                # 突破下轨且上升
                daily_score[i] = 67
                signal_duration[i] = 2
            elif (crsi_daily[i-1] > ub_daily[i-1] and crsi_daily[i] < ub_daily[i-1] and
                  crsi_daily[i] < crsi_daily[i-1]):
                # 跌破上轨且下降
                daily_score[i] = -67
                signal_duration[i] = 2
            elif crsi_daily[i] < db_daily[i]:
                # 在下轨之下
                daily_score[i] = 27 if crsi_daily[i] > crsi_daily[i-1] else 13
                signal_duration[i] = 2
            elif crsi_daily[i] > ub_daily[i]:
                # 在上轨之上
                daily_score[i] = -27 if crsi_daily[i] < crsi_daily[i-1] else -13
                signal_duration[i] = 2
            elif signal_duration[i-1] > 0:
                # 信号持续期间
                signal_duration[i] = signal_duration[i-1] - 1
                daily_score[i] = daily_score[i-1]
            else:
                daily_score[i] = 0
                signal_duration[i] = 0

            # 周线信号分数计算 (严格按照Pine Script逻辑)
            if (crsi_weekly[i-1] < db_weekly[i-1] and crsi_weekly[i] > db_weekly[i-1] and
                crsi_weekly[i] > crsi_weekly[i-1]):
                # 突破下轨且上升
                weekly_score[i] = 67
                weekly_signal_duration[i] = 2
            elif (crsi_weekly[i-1] > ub_weekly[i-1] and crsi_weekly[i] < ub_weekly[i-1] and
                  crsi_weekly[i] < crsi_weekly[i-1]):
                # 跌破上轨且下降
                weekly_score[i] = -67
                weekly_signal_duration[i] = 2
            elif crsi_weekly[i] < db_weekly[i]:
                # 在下轨之下 (注意：Pine Script中周线这里都是33)
                weekly_score[i] = 33  # Pine Script: crsi_weekly > crsi_weekly[1] ? 33 : 33
                weekly_signal_duration[i] = 2
            elif crsi_weekly[i] > ub_weekly[i]:
                # 在上轨之上 (注意：Pine Script中周线这里都是-33)
                weekly_score[i] = -33  # Pine Script: crsi_weekly < crsi_weekly[1] ? -33 : -33
                weekly_signal_duration[i] = 2
            elif weekly_signal_duration[i-1] > 0:
                # 信号持续期间
                weekly_signal_duration[i] = weekly_signal_duration[i-1] - 1
                weekly_score[i] = weekly_score[i-1]
            else:
                weekly_score[i] = 0
                weekly_signal_duration[i] = 0

            # 计算RSI最终分数 (严格按照Pine Script逻辑)
            rsi_score[i] = max(-100, min(100, daily_score[i] + weekly_score[i]))

        return rsi_score

    def _calculate_background_colors(self, daily_signals: List[str],
                                   is_weekly_bullish: np.ndarray, is_weekly_bearish: np.ndarray) -> List[str]:
        """计算背景色"""
        length = len(daily_signals)
        bg_colors = []

        for i in range(length):
            signal = daily_signals[i]

            if 'bull' in signal:
                # 多头信号背景色
                if is_weekly_bullish[i]:
                    if signal == 'strongest_bull':
                        bg_colors.append('rgba(0, 255, 0, 0.5)')  # 强绿色
                    elif signal == 'rising_bull':
                        bg_colors.append('rgba(50, 205, 50, 0.35)')  # 中绿色
                    else:
                        bg_colors.append('rgba(50, 205, 50, 0.2)')  # 淡绿色
                else:
                    bg_colors.append('rgba(50, 205, 50, 0.15)')  # 很淡的绿色
            elif 'bear' in signal:
                # 空头信号背景色
                if is_weekly_bearish[i]:
                    if signal == 'strongest_bear':
                        bg_colors.append('rgba(255, 0, 0, 0.5)')  # 强红色
                    elif signal == 'falling_bear':
                        bg_colors.append('rgba(128, 0, 0, 0.35)')  # 中红色
                    else:
                        bg_colors.append('rgba(128, 0, 0, 0.2)')  # 淡红色
                else:
                    bg_colors.append('rgba(128, 0, 0, 0.15)')  # 很淡的红色
            else:
                bg_colors.append('transparent')

        return bg_colors

    def _empty_result(self, length: int) -> Dict:
        """返回空结果"""
        return {
            'crsi_daily': np.full(length, np.nan),
            'db_daily': np.full(length, np.nan),
            'ub_daily': np.full(length, np.nan),
            'crsi_weekly': np.full(length, np.nan),
            'db_weekly': np.full(length, np.nan),
            'ub_weekly': np.full(length, np.nan),
            'daily_signals': ['neutral'] * length,
            'weekly_signals': ['neutral'] * length,
            'is_weekly_bullish': np.full(length, False),
            'is_weekly_bearish': np.full(length, False),
            'rsi_score': np.full(length, 0),
            'background_colors': ['transparent'] * length
        }
