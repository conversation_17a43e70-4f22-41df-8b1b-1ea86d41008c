#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI市场分析系统 - 主启动脚本
基于新架构的股票趋势分析系统
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置日志配置"""
    log_dir = project_root / 'logs'
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='[%(asctime)s] %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / 'system.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def main():
    """主函数"""
    setup_logging()
    
    print("=" * 60)
    print("🚀 AI市场分析系统 v2.0")
    print("=" * 60)
    print()
    
    try:
        # 导入并启动Web应用
        from web.app import app, init_scorer
        
        # 初始化评分器
        if init_scorer():
            logging.info("✅ 评分器初始化成功")
            print("📊 评分器初始化成功")
            print("🌐 启动Web服务器...")
            print("📱 本地访问: http://127.0.0.1:50505")
            print("📱 网络访问: http://**********:50505")
            print("=" * 60)

            # 启动Flask应用
            app.run(
                host='0.0.0.0',
                port=50505,
                debug=False
            )
        else:
            logging.error("❌ 评分器初始化失败")
            print("❌ 评分器初始化失败，请检查配置和数据")
            sys.exit(1)
            
    except ImportError as e:
        logging.error(f"导入错误: {e}")
        print(f"❌ 导入错误: {e}")
        sys.exit(1)
    except Exception as e:
        logging.error(f"启动失败: {e}")
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
