#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新的Web应用
基于新架构的Web界面，支持分组配置和权重调整
"""

from flask import Flask, render_template, jsonify, request, send_from_directory
from flask_cors import CORS
import json
import logging
from datetime import datetime
import pandas as pd
import numpy as np
from core.composite.scorer import NewCompositeScorer
from core.data.data_loader import DataLoader
from core.data.csv_data_loader import CSVDataLoader
from core.data.hybrid_data_loader import HybridDataLoader
from core.indicators import MCSIMACDIndicator, MCSIMMTIndicator, MCSIRSIIndicator, MCSITD9Indicator
from core.config.market_config import market_detector, get_market_aggregator, analyze_market_data
from config.settings import settings


# 导入MCSI统一接口（Step 1-2 完成的权威版本）
from core.scoring_units.mcsi_macd_scoring import MCSIMACDScoringUnit
from core.scoring_units.mcsi_mmt_scoring import MCSIMMTScoringUnit
from core.scoring_units.mcsi_rsi_scoring import MCSIRSIScoringUnit
from core.scoring_units.mcsi_ttm_scoring import MCSITTMScoringUnit

# 导入MCSI权威版本单元（TV-code/py-code直接实现）
from core.scoring_units.mcsi_authority_units import (
    AuthorityMCSIMACDUnit, AuthorityMCSIRSIUnit, 
    AuthorityMCSIMMTUnit, AuthorityMCSITTMUnit,
    AUTHORITY_MCSI_AVAILABLE
)

# 实例化MCSI统一接口单元
logging.info("✅ MCSI统一接口已导入到Web服务 (权威版本集成)")
mcsi_macd_unit = MCSIMACDScoringUnit()
mcsi_mmt_unit = MCSIMMTScoringUnit()
mcsi_rsi_unit = MCSIRSIScoringUnit()
mcsi_ttm_unit = MCSITTMScoringUnit()

# 实例化MCSI权威版本单元
logging.info("🔧 MCSI权威版本已导入到Web服务 (TV-code/py-code直接集成)")
authority_macd_unit = AuthorityMCSIMACDUnit()
authority_mmt_unit = AuthorityMCSIMMTUnit()
authority_rsi_unit = AuthorityMCSIRSIUnit()
authority_ttm_unit = AuthorityMCSITTMUnit()

# 验证所有MCSI单元是否可用
try:
    # 简单的可用性测试
    import pandas as pd
    test_data = pd.DataFrame({
        'date': pd.date_range('2023-01-01', periods=150),  # 增加数据长度以满足权威版本需求
        'open': [100 + i for i in range(150)],
        'high': [105 + i for i in range(150)],
        'low': [95 + i for i in range(150)],
        'close': [102 + i for i in range(150)],
        'volume': [1000 + i*10 for i in range(150)]
    })
    
    # 测试统一接口单元
    mcsi_macd_unit.calculate_score(test_data)
    mcsi_mmt_unit.calculate_score(test_data)
    mcsi_rsi_unit.calculate_score(test_data)
    mcsi_ttm_unit.calculate_score(test_data)
    
    MCSI_UNIFIED_AVAILABLE = True
    logging.info("🎉 所有MCSI统一接口单元已验证可用")
    
    # 测试权威版本单元
    authority_macd_unit.calculate_score(test_data)
    authority_mmt_unit.calculate_score(test_data)  
    authority_rsi_unit.calculate_score(test_data)
    authority_ttm_unit.calculate_score(test_data)
    
    logging.info("🔧 所有MCSI权威版本单元已验证可用")
    
except Exception as e:
    logging.error(f"❌ MCSI单元验证失败: {e}")
    MCSI_UNIFIED_AVAILABLE = False
    # 设置为None但不抛出异常，让系统降级使用其他指标
    mcsi_macd_unit = None
    mcsi_mmt_unit = None 
    mcsi_rsi_unit = None
    mcsi_ttm_unit = None
    authority_macd_unit = None
    authority_mmt_unit = None
    authority_rsi_unit = None
    authority_ttm_unit = None

# 所有回退逻辑已移除 - 只使用MCSI统一接口直接实现

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s - %(message)s'
)

app = Flask(__name__, template_folder='templates')
CORS(app)

# 全局变量
scorer = None
hybrid_data_loader = None  # 混合数据加载器（优先数据库，备用CSV）
analysis_status = {
    'is_running': False,
    'progress': 0,
    'total': 0,
    'current_stock': '',
    'message': '就绪'
}

def init_scorer():
    """初始化评分器和混合数据加载器"""
    global scorer, hybrid_data_loader
    try:
        scorer = NewCompositeScorer()
        hybrid_data_loader = HybridDataLoader(prefer_database=True)
        # 设置评分器使用混合数据加载器
        scorer.set_hybrid_data_loader(hybrid_data_loader)

        # 记录数据源状态
        status = hybrid_data_loader.get_data_source_status()
        logging.info(f"混合数据加载器初始化成功，可用数据源: {status['active_sources']}")
        logging.info(f"数据库可用: {status['database_available']}, CSV可用: {status['csv_available']}")
        return True
    except Exception as e:
        logging.error(f"初始化失败: {str(e)}")
        return False

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/professional_chart.html')
def professional_chart():
    """专业图表页面"""
    return render_template('professional_chart.html')

@app.route('/simple_debug.html')
def simple_debug():
    """简单调试页面"""
    return render_template('simple_debug.html')

@app.route('/test_chart.html')
def test_chart():
    """图表测试页面"""
    return render_template('test_chart.html')

@app.route('/debug_chart.html')
def debug_chart():
    """图表调试页面"""
    return render_template('debug_chart.html')

@app.route('/sync_test.html')
def sync_test():
    """时间轴同步测试页面"""
    return render_template('sync_test.html')

@app.route('/old_system_chart.html')
def old_system_chart():
    """旧系统图表页面"""
    return render_template('old_system_chart.html')

@app.route('/test_results.html')
def test_results():
    """测试结果页面"""
    return send_from_directory('.', 'test_results.html')

@app.route('/debug_results.html')
def debug_results():
    """调试结果页面"""
    return send_from_directory('.', 'debug_results.html')

@app.route('/api/groups')
def get_groups():
    """获取所有分组信息"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500
        
        groups = scorer.list_groups()
        weights = scorer.get_group_weights()
        
        return jsonify({
            'groups': groups,
            'weights': weights,
            'status': 'success'
        })
    except Exception as e:
        logging.error(f"获取分组信息失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/groups/weights', methods=['POST'])
def update_group_weights():
    """更新分组权重"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500
        
        data = request.get_json()
        weights = data.get('weights', {})
        
        scorer.update_group_weights(weights)
        scorer.save_config()
        
        return jsonify({
            'message': '分组权重更新成功',
            'weights': scorer.get_group_weights(),
            'status': 'success'
        })
    except Exception as e:
        logging.error(f"更新分组权重失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/groups/create', methods=['POST'])
def create_custom_group():
    """创建自定义分组"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500
        
        data = request.get_json()
        group_id = data.get('group_id')
        name = data.get('name')
        description = data.get('description', '')
        weight = data.get('weight', 1.0)
        custom_config = data.get('config', {})
        
        if not group_id or not name:
            return jsonify({'error': '分组ID和名称不能为空'}), 400
        
        success = scorer.create_custom_group(
            group_id, name, description, weight, custom_config
        )
        
        if success:
            scorer.save_config()
            return jsonify({
                'message': f'自定义分组 {name} 创建成功',
                'group_id': group_id,
                'status': 'success'
            })
        else:
            return jsonify({'error': '创建自定义分组失败'}), 500
            
    except Exception as e:
        logging.error(f"创建自定义分组失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/groups/<group_id>', methods=['DELETE'])
def remove_group(group_id):
    """删除分组"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500
        
        success = scorer.remove_group(group_id)
        
        if success:
            scorer.save_config()
            return jsonify({
                'message': f'分组 {group_id} 删除成功',
                'status': 'success'
            })
        else:
            return jsonify({'error': '删除分组失败'}), 500
            
    except Exception as e:
        logging.error(f"删除分组失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/scoring_units')
def get_scoring_units():
    """获取可用的计分单元类型"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500
        
        units = scorer.get_available_scoring_units()
        
        return jsonify({
            'scoring_units': units,
            'status': 'success'
        })
    except Exception as e:
        logging.error(f"获取计分单元失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/analyze', methods=['POST'])
def start_analysis():
    """开始分析"""
    global analysis_status

    try:
        # 确保评分器已初始化
        global scorer
        if not scorer:
            logging.info("评分器未初始化，尝试重新初始化...")
            if not init_scorer():
                return jsonify({'error': '评分器初始化失败'}), 500

        if analysis_status['is_running']:
            return jsonify({'error': '分析正在进行中'}), 400

        data = request.get_json() or {}

        # 获取分析参数
        data_limit = data.get('data_limit', 500)
        force_refresh = data.get('force_refresh', True)

        logging.info(f"开始分析 - 数据量: {data_limit}天, 强制刷新: {force_refresh}")

        # 获取股票列表
        stock_list = data.get('stocks', [])

        if not stock_list:
            # 使用混合数据加载器获取股票列表
            global hybrid_data_loader
            if not hybrid_data_loader:
                hybrid_data_loader = HybridDataLoader(prefer_database=True)

            all_stocks = hybrid_data_loader.get_stock_list()
            # 包含所有股票类型，包括中国指数
            stock_files = all_stocks

            # 验证数据有效性，过滤掉空表
            logging.info(f"开始验证 {len(stock_files)} 只股票的数据有效性...")
            valid_stock_files = []

            for i, stock_info in enumerate(stock_files):
                table_name = stock_info['table_name']

                # 使用混合数据加载器验证数据
                try:
                    test_data = hybrid_data_loader.get_stock_data(table_name, limit=1)
                    if test_data is not None and not test_data.empty:
                        valid_stock_files.append(stock_info)
                    else:
                        logging.debug(f"过滤空表: {stock_info['symbol']} - {stock_info['name']}")
                except Exception as e:
                    logging.debug(f"过滤错误表: {stock_info['symbol']} - {stock_info['name']}: {str(e)}")

                # 每10个股票输出一次进度
                if (i + 1) % 10 == 0:
                    logging.info(f"已验证 {i + 1}/{len(stock_files)} 只股票，有效: {len(valid_stock_files)} 只")

            logging.info(f"数据验证完成：{len(stock_files)} 只股票中有 {len(valid_stock_files)} 只有效")

            # 使用验证后的股票列表
            stock_list = [{'code': stock_info['symbol'], 'name': stock_info['name'], 'table_name': stock_info['table_name']} for stock_info in valid_stock_files]

        if not stock_list:
            return jsonify({'error': '没有找到可分析的股票'}), 400

        # 开始分析前清空旧结果
        scorer.clear_history()

        # 开始分析
        analysis_status['is_running'] = True
        analysis_status['total'] = len(stock_list)
        analysis_status['progress'] = 0
        analysis_status['message'] = f'开始分析 {len(stock_list)} 只股票，数据量: {data_limit}天...'

        # 批量评分（传递数据量参数）- 添加进度更新
        results = []
        for i, stock_info in enumerate(stock_list):
            stock_code = stock_info.get('code', '')
            stock_name = stock_info.get('name', '')
            table_name = stock_info.get('table_name', '')

            if stock_code and table_name:
                # 更新进度
                analysis_status['progress'] = i
                analysis_status['current_stock'] = stock_code
                analysis_status['message'] = f'正在分析 {stock_code} ({i+1}/{len(stock_list)})'

                # 使用table_name进行评分，确保与验证阶段使用相同的查找逻辑
                result = scorer.score_stock(table_name, stock_name, data_limit=data_limit)
                # 但在结果中保留原始的stock_code用于显示
                if result:
                    result.stock_code = stock_code  # 覆盖为显示用的代码
                results.append(result)

        # 更新状态
        analysis_status['is_running'] = False
        analysis_status['progress'] = analysis_status['total']
        analysis_status['message'] = f'分析完成，共处理 {len(results)} 只股票 (数据量: {data_limit}天)'
        
        return jsonify({
            'message': '分析完成',
            'results_count': len(results),
            'status': 'success'
        })
        
    except Exception as e:
        analysis_status['is_running'] = False
        analysis_status['message'] = f'分析失败: {str(e)}'
        logging.error(f"分析失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/results')
def get_results():
    """获取分析结果"""
    try:
        # 确保评分器已初始化
        global scorer
        if not scorer:
            logging.info("评分器未初始化，尝试重新初始化...")
            if not init_scorer():
                return jsonify({'error': '评分器初始化失败'}), 500
        
        results = scorer.get_latest_results(100)  # 获取最新100个结果

        # 获取混合数据加载器来解析分类信息
        global hybrid_data_loader
        if not hybrid_data_loader:
            hybrid_data_loader = HybridDataLoader(prefer_database=True)

        # 转换为字典格式并添加分组分数
        results_data = []
        for result in results:
            result_dict = result.to_dict()

            # 提取分组分数
            group_results = result_dict.get('group_results', {})
            trend_score = 0.0
            oscillation_score = 0.0

            if 'trend_group' in group_results:
                trend_score = group_results['trend_group'].get('weighted_score', 0.0)

            if 'oscillation_group' in group_results:
                oscillation_score = group_results['oscillation_group'].get('weighted_score', 0.0)

            # 添加分组分数到结果中
            result_dict['trend_score'] = trend_score
            result_dict['oscillation_score'] = oscillation_score

            # 获取股票分类信息 - 使用混合数据加载器
            stock_code = result_dict['stock_code']
            all_stocks = hybrid_data_loader.get_stock_list()
            category = '其他'

            # 查找对应的股票信息
            for stock_info in all_stocks:
                if stock_info['symbol'] == stock_code or stock_info['table_name'] == stock_code:
                    category = stock_info['category']
                    break

            # 添加旧系统兼容字段
            result_dict['symbol'] = result_dict['stock_code']  # 旧系统期望symbol字段
            result_dict['name'] = result_dict['stock_name']    # 旧系统期望name字段
            result_dict['category'] = category  # 添加正确的分类字段
            result_dict['trend_grade'] = result_dict['grade']  # 旧系统期望trend_grade字段
            result_dict['rsi_score'] = oscillation_score  # RSI分数映射到震荡分数
            result_dict['rsi_value'] = 50.0  # 默认RSI值
            result_dict['macd_score'] = trend_score  # MACD分数映射到趋势分数
            result_dict['macd_value'] = 0.0  # 默认MACD值
            result_dict['data_count'] = 250  # 默认数据点数量

            results_data.append(result_dict)

        # 按分数排序
        results_data.sort(key=lambda x: x['composite_score'], reverse=True)
        
        # 兼容旧系统的API格式
        return jsonify({
            'success': True,
            'data': {
                'items': results_data,  # 旧系统期望的是items字段
                'total': len(results_data),
                'page': 1,
                'size': len(results_data),
                'pages': 1,
                'pagination': {
                    'page': 1,
                    'pages': 1,
                    'per_page': len(results_data),
                    'total': len(results_data)
                }
            }
        })
        
    except Exception as e:
        logging.error(f"获取结果失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/status')
def get_status():
    """获取分析状态"""
    return jsonify(analysis_status)

@app.route('/api/stock_chart/<path:stock_code>')
def get_stock_chart_data(stock_code):
    """获取股票图表数据 - 完全兼容旧系统格式"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500

        # URL解码股票代码
        from urllib.parse import unquote
        stock_code = unquote(stock_code)
        logging.info(f"请求股票图表数据: {stock_code}")

        # 获取limit参数，默认500
        limit = request.args.get('limit', 500, type=int)

        # 获取股票数据 - 使用混合数据加载器
        global hybrid_data_loader
        if not hybrid_data_loader:
            hybrid_data_loader = HybridDataLoader(prefer_database=True)

        # 首先获取所有可用的股票
        all_stocks = hybrid_data_loader.get_stock_list()

        # 查找匹配的股票 - 支持多种匹配方式
        stock_info = None
        for info in all_stocks:
            # 1. 精确匹配symbol
            if info['symbol'] == stock_code:
                stock_info = info
                break
            # 2. 精确匹配table_name
            elif info['table_name'] == stock_code:
                stock_info = info
                break

        if not stock_info:
            # 如果还没找到，尝试直接使用stock_code查找
            if hybrid_data_loader.check_stock_exists(stock_code):
                logging.info(f"直接使用 {stock_code} 查找股票数据")
                stock_data = hybrid_data_loader.get_stock_data(stock_code, limit=limit)
            else:
                logging.warning(f"股票 {stock_code} 未找到，可用股票: {[s['symbol'] for s in all_stocks[:5]]}")
                logging.warning(f"可用表名: {[s['table_name'] for s in all_stocks[:5]]}")
                return jsonify({'error': f'股票 {stock_code} 未找到'}), 404
        else:
            # 使用找到的股票信息获取数据
            stock_data = hybrid_data_loader.get_stock_data(stock_info['table_name'], limit=limit)

        if stock_data is None or stock_data.empty:
            return jsonify({'error': '股票数据不存在'}), 404

        # 使用新系统的计分单元计算指标
        from core.scoring_units.macd_scoring_unit import MACDCalculator
        from core.scoring_units.rsi_scoring_unit import RSICalculator

        # 计算技术指标
        macd_series = MACDCalculator.calculate_macd(stock_data['close'])
        rsi_series = RSICalculator.calculate_rsi(stock_data['close'])

        # 🔒 优化的滑动窗口MCSI计算 - 减少计算负载
        def calculate_daily_window_scores(data, macd_unit, mmt_unit, window_size=100):
            """
            高分辨率滑动窗口MCSI计算 - 日线级别精度
            - 每日计算评分，确保最高时间分辨率
            - 优化计算性能以支持日线级别计算
            - 确保评分变化与技术指标图表同步
            """
            data_length = len(data)
            
            # 🔧 确保窗口大小适合MCSI计算需求
            min_required = 50  # 降低最小数据要求以适应更多场景
            if data_length < 100:
                window_size = min(max(min_required, data_length), data_length)
            
            # ✅ 日线级别计算：每天都计算评分，无稀疏采样
            macd_scores = [0.0] * data_length
            mmt_scores = [0.0] * data_length
            
            # ⚡ 性能优化：减少重复计算和内存分配
            logging.info(f"📈 开始日线级别MCSI计算，数据长度: {data_length}")
            
            # 预缓存数据以减少重复访问
            cached_data = data.copy()
            
            # 批量处理优化：每10天输出一次进度
            progress_interval = max(10, data_length // 20)
            
            for i in range(data_length):
                # 确保有足够的历史数据
                if i < min_required - 1:
                    macd_scores[i] = 0.0
                    mmt_scores[i] = 0.0
                    continue
                
                # ⚡ 优化数据切片，避免重复copy
                start_idx = max(0, i + 1 - window_size)
                end_idx = i + 1
                window_data = cached_data.iloc[start_idx:end_idx]  # 移除不必要的copy()
                
                try:
                    macd_result = macd_unit.score_with_validation(window_data) if macd_unit else None
                    mmt_result = mmt_unit.score_with_validation(window_data) if mmt_unit else None
                    
                    macd_scores[i] = macd_result.score if macd_result else 0.0
                    mmt_scores[i] = mmt_result.score if mmt_result else 0.0
                    
                except Exception as e:
                    # 使用前一天的值作为回退
                    if i > 0:
                        macd_scores[i] = macd_scores[i-1]
                        mmt_scores[i] = mmt_scores[i-1]
                    else:
                        macd_scores[i] = 0.0
                        mmt_scores[i] = 0.0
                    
                    if i % 50 == 0:  # 减少日志输出频率
                        logging.warning(f"🔒 第{i+1}天MCSI计算失败: {e}")
                
                # ⚡ 进度输出优化
                if i % progress_interval == 0 and i > 0:
                    progress = (i / data_length) * 100
                    logging.info(f"📊 MACD/MMT计算进度: {progress:.1f}% ({i}/{data_length})")
            
            logging.info(f"✅ 日线级别MCSI计算完成，共{data_length}个评分点")
            return macd_scores, mmt_scores
        
        mcsi_macd_score = None
        mcsi_mmt_score = None
        
        # 直接使用MCSI统一接口（已验证可用）
        if mcsi_macd_unit and mcsi_mmt_unit:
            try:
                logging.info("🔒 开始滑动窗口MCSI计算...")
                
                # 使用日线级别滑动窗口计算每天的评分
                macd_scores, mmt_scores = calculate_daily_window_scores(
                    stock_data, mcsi_macd_unit, mcsi_mmt_unit, window_size=100
                )
                
                mcsi_macd_score = macd_scores
                mcsi_mmt_score = mmt_scores
                
                logging.info(f"✅ 滑动窗口MCSI计算完成: {len(macd_scores)}个评分点")
                logging.info(f"   MACD评分范围: {min(macd_scores):.2f} ~ {max(macd_scores):.2f}")
                logging.info(f"   MMT评分范围: {min(mmt_scores):.2f} ~ {max(mmt_scores):.2f}")
                
            except Exception as e:
                logging.error(f"🔒 滑动窗口MCSI计算失败: {e}")
                mcsi_macd_score = 0.0
                mcsi_mmt_score = 0.0
        else:
            logging.warning("🔒 高级MCSI单元不可用，为保护知识产权，不提供MCSI指标数据")
            mcsi_macd_score = 0.0
            mcsi_mmt_score = 0.0
        # 准备完整的数据字典给RSI指标
        try:
            # 检查是否有timestamp或date列，优先使用实际日期数据
            if 'timestamp' in stock_data.columns:
                # 数据库数据，使用timestamp列
                dates = stock_data['timestamp'].dt.strftime('%Y-%m-%d').tolist()
            elif 'date' in stock_data.columns:
                # CSV数据，使用date列  
                dates = stock_data['date'].tolist()
            else:
                # 创建虚拟日期序列（回退方案）
                dates = [f"2024-01-{i+1:02d}" for i in range(len(stock_data))]

            # 暂时跳过市场检测以修复图表显示问题
            # TODO: 修复analyze_market_data函数中的时间差转换问题
            # market_analysis = analyze_market_data(dates, stock_code)
            # market_type = market_analysis['recommended_type']
            market_type = 'stock'  # 默认使用股票类型

            app.logger.info(f"图表数据生成: {stock_code} (类型: {market_type})")

            full_data = {
                'date': dates,
                'open': stock_data['open'].values,
                'high': stock_data['high'].values,
                'low': stock_data['low'].values,
                'close': stock_data['close'].values,
                'volume': stock_data.get('volume', [1] * len(stock_data)).values if 'volume' in stock_data.columns else [1] * len(stock_data),
                'market_type': market_type  # 添加市场类型信息
            }
        except Exception as e:
            app.logger.warning(f"数据驱动检测失败，使用简化模式: {e}")
            app.logger.warning(f"  - 异常类型: {type(e).__name__}")
            app.logger.warning(f"  - 股票代码: {stock_code}")
            app.logger.warning(f"  - 数据列: {list(stock_data.columns) if hasattr(stock_data, 'columns') else 'N/A'}")
            app.logger.warning(f"  - 数据形状: {stock_data.shape if hasattr(stock_data, 'shape') else 'N/A'}")
            # 回退到原来的方式
            full_data = stock_data['close'].values

        # 🔒 优化的滑动窗口RSI/TTM计算
        def calculate_daily_window_rsi_ttm(data, rsi_unit, ttm_unit, window_size=100):
            """高分辨率滑动窗口RSI/TTM计算 - 日线级别精度"""
            data_length = len(data)
            
            # 确保满足RSI/TTM最低数据要求
            min_required = 50  # 降低最小数据要求以适应更多场景
            if data_length < 100:
                window_size = min(max(min_required, data_length), data_length)
            
            # ✅ 日线级别计算：每天都计算评分，无稀疏采样
            rsi_scores = [0.0] * data_length
            ttm_scores = [0.0] * data_length
            
            logging.info(f"📈 开始日线级别RSI/TTM计算，数据长度: {data_length}")
            
            # ⚡ 性能优化：缓存数据和批量处理
            cached_data = data.copy()
            progress_interval = max(10, data_length // 20)
            
            # 计算每一天的评分
            for i in range(data_length):
                # 确保有足够的历史数据
                if i < min_required - 1:
                    rsi_scores[i] = 0.0
                    ttm_scores[i] = 0.0
                    continue
                    
                # ⚡ 优化数据切片性能
                start_idx = max(0, i + 1 - window_size)
                end_idx = i + 1
                window_data = cached_data.iloc[start_idx:end_idx]  # 移除不必要的copy()
                
                try:
                    rsi_result = rsi_unit.score_with_validation(window_data) if rsi_unit else None
                    ttm_result = ttm_unit.score_with_validation(window_data) if ttm_unit else None
                    
                    rsi_scores[i] = rsi_result.score if rsi_result else 0.0
                    ttm_scores[i] = ttm_result.score if ttm_result else 0.0
                    
                except Exception as e:
                    # 使用前一天的值作为回退
                    if i > 0:
                        rsi_scores[i] = rsi_scores[i-1]
                        ttm_scores[i] = ttm_scores[i-1]
                    else:
                        rsi_scores[i] = 0.0
                        ttm_scores[i] = 0.0
                    
                    if i % 50 == 0:  # 减少日志输出频率
                        logging.warning(f"🔒 第{i+1}天RSI/TTM计算失败: {e}")
                
                # ⚡ 进度输出优化
                if i % progress_interval == 0 and i > 0:
                    progress = (i / data_length) * 100
                    logging.info(f"📊 RSI/TTM计算进度: {progress:.1f}% ({i}/{data_length})")
            
            logging.info(f"✅ 日线级别RSI/TTM计算完成，共{data_length}个评分点")
            return rsi_scores, ttm_scores
        
        mcsi_rsi_score = None
        mcsi_ttm_score = None
        
        # 直接使用MCSI统一接口（已验证可用）
        if mcsi_rsi_unit and mcsi_ttm_unit:
            try:
                logging.info("🔒 开始滑动窗口MCSI RSI/TTM计算...")
                
                rsi_scores, ttm_scores = calculate_daily_window_rsi_ttm(
                    stock_data, mcsi_rsi_unit, mcsi_ttm_unit, window_size=100
                )
                
                mcsi_rsi_score = rsi_scores
                mcsi_ttm_score = ttm_scores
                
                logging.info(f"✅ 滑动窗口RSI/TTM计算完成: {len(rsi_scores)}个评分点")
                logging.info(f"   RSI评分范围: {min(rsi_scores):.2f} ~ {max(rsi_scores):.2f}")
                logging.info(f"   TTM评分范围: {min(ttm_scores):.2f} ~ {max(ttm_scores):.2f}")
                
            except Exception as e:
                logging.error(f"🔒 滑动窗口RSI/TTM计算失败: {e}")
                mcsi_rsi_score = 0.0
                mcsi_ttm_score = 0.0
        else:
            logging.warning("🔒 高级MCSI RSI/TTM单元不可用，为保护知识产权，不提供指标数据")
            mcsi_rsi_score = 0.0
            mcsi_ttm_score = 0.0

        # 🚀 权威版本MCSI计算 - 与统一接口并行计算进行对比分析
        authority_macd_score = None
        authority_mmt_score = None
        authority_rsi_score = None
        authority_ttm_score = None
        
        # 🎨 权威版本完整数据（Pine Script原生元素）
        authority_macd_full_data = None
        authority_mmt_full_data = None
        authority_rsi_full_data = None
        authority_ttm_full_data = None
        
        if authority_macd_unit and authority_mmt_unit and authority_rsi_unit and authority_ttm_unit:
            try:
                logging.info("🔧 开始权威版本MCSI计算...")
                
                # 使用相同的滑动窗口方式，但调用权威版本
                def calculate_authority_daily_window_scores(data, auth_macd, auth_mmt, auth_rsi, auth_ttm, window_size=100):
                    """权威版本日线级别滑动窗口计算"""
                    data_length = len(data)
                    min_required = 50
                    
                    if data_length < 100:
                        window_size = min(max(min_required, data_length), data_length)
                    
                    # ✅ 日线级别计算：每天都计算权威版本评分
                    auth_macd_scores = [0.0] * data_length
                    auth_mmt_scores = [0.0] * data_length
                    auth_rsi_scores = [0.0] * data_length
                    auth_ttm_scores = [0.0] * data_length
                    
                    logging.info(f"🔧 开始日线级别权威版本计算，数据长度: {data_length}")
                    
                    # ⚡ 权威版本性能优化
                    cached_data = data.copy()
                    progress_interval = max(10, data_length // 20)
                    
                    # 计算每一天的评分
                    for i in range(data_length):
                        # 确保有足够的历史数据
                        if i < min_required - 1:
                            auth_macd_scores[i] = 0.0
                            auth_mmt_scores[i] = 0.0
                            auth_rsi_scores[i] = 0.0
                            auth_ttm_scores[i] = 0.0
                            continue
                            
                        # ⚡ 优化数据切片性能
                        start_idx = max(0, i + 1 - window_size)
                        end_idx = i + 1
                        window_data = cached_data.iloc[start_idx:end_idx]  # 移除不必要的copy()
                        
                        try:
                            # 调用权威版本计算
                            auth_macd_result = auth_macd.calculate_score(window_data) if auth_macd else None
                            auth_mmt_result = auth_mmt.calculate_score(window_data) if auth_mmt else None
                            auth_rsi_result = auth_rsi.calculate_score(window_data) if auth_rsi else None
                            auth_ttm_result = auth_ttm.calculate_score(window_data) if auth_ttm else None
                            
                            auth_macd_scores[i] = auth_macd_result.score if auth_macd_result else 0.0
                            auth_mmt_scores[i] = auth_mmt_result.score if auth_mmt_result else 0.0
                            auth_rsi_scores[i] = auth_rsi_result.score if auth_rsi_result else 0.0
                            auth_ttm_scores[i] = auth_ttm_result.score if auth_ttm_result else 0.0
                            
                        except Exception as e:
                            # 使用前一天的值作为回退
                            if i > 0:
                                auth_macd_scores[i] = auth_macd_scores[i-1]
                                auth_mmt_scores[i] = auth_mmt_scores[i-1]
                                auth_rsi_scores[i] = auth_rsi_scores[i-1]
                                auth_ttm_scores[i] = auth_ttm_scores[i-1]
                            else:
                                auth_macd_scores[i] = 0.0
                                auth_mmt_scores[i] = 0.0
                                auth_rsi_scores[i] = 0.0
                                auth_ttm_scores[i] = 0.0
                            
                            if i % 50 == 0:  # 减少日志输出频率
                                logging.warning(f"🔧 第{i+1}天权威版本计算失败: {e}")
                        
                        # ⚡ 进度输出优化
                        if i % progress_interval == 0 and i > 0:
                            progress = (i / data_length) * 100
                            logging.info(f"🔧 权威版本计算进度: {progress:.1f}% ({i}/{data_length})")
                    
                    logging.info(f"✅ 日线级别权威版本计算完成，共{data_length}个评分点")
                    return auth_macd_scores, auth_mmt_scores, auth_rsi_scores, auth_ttm_scores
                
                # 使用权威版本日线级别计算
                auth_macd_scores, auth_mmt_scores, auth_rsi_scores, auth_ttm_scores = calculate_authority_daily_window_scores(
                    stock_data, authority_macd_unit, authority_mmt_unit, authority_rsi_unit, authority_ttm_unit, window_size=100
                )
                
                authority_macd_score = auth_macd_scores
                authority_mmt_score = auth_mmt_scores
                authority_rsi_score = auth_rsi_scores
                authority_ttm_score = auth_ttm_scores
                
                # 🎨 获取权威版本完整数据（Pine Script原生元素）
                try:
                    logging.info("🎨 获取权威版本完整数据（Pine Script原生元素）...")
                    
                    # 为最后150个数据点获取完整数据（避免性能问题）
                    calc_data = stock_data.tail(150).copy()
                    
                    authority_macd_full_data = authority_macd_unit.calculate_full_data(calc_data)
                    authority_mmt_full_data = authority_mmt_unit.calculate_full_data(calc_data)
                    authority_rsi_full_data = authority_rsi_unit.calculate_full_data(calc_data)
                    authority_ttm_full_data = authority_ttm_unit.calculate_full_data(calc_data)
                    
                    # 🔧 将numpy数组转换为Python列表以便JSON序列化
                    import numpy as np
                    
                    def convert_numpy_arrays(data_dict):
                        """递归转换numpy数组为Python列表"""
                        if not data_dict:
                            return None
                        converted = {}
                        for key, value in data_dict.items():
                            if isinstance(value, np.ndarray):
                                # 转换numpy数组为列表，处理NaN值
                                converted[key] = [float(x) if not np.isnan(x) else None for x in value.tolist()]
                            elif isinstance(value, (np.integer, np.floating)):
                                converted[key] = float(value)
                            elif isinstance(value, dict):
                                converted[key] = convert_numpy_arrays(value)
                            else:
                                converted[key] = value
                        return converted
                    
                    authority_macd_full_data = convert_numpy_arrays(authority_macd_full_data)
                    authority_mmt_full_data = convert_numpy_arrays(authority_mmt_full_data)
                    authority_rsi_full_data = convert_numpy_arrays(authority_rsi_full_data)
                    authority_ttm_full_data = convert_numpy_arrays(authority_ttm_full_data)
                    
                    if authority_macd_full_data:
                        logging.info(f"✅ 权威MACD完整数据键: {list(authority_macd_full_data.keys())}")
                    if authority_mmt_full_data:
                        logging.info(f"✅ 权威MMT完整数据键: {list(authority_mmt_full_data.keys())}")
                    if authority_rsi_full_data:
                        logging.info(f"✅ 权威RSI完整数据键: {list(authority_rsi_full_data.keys())}")
                    if authority_ttm_full_data:
                        logging.info(f"✅ 权威TTM完整数据键: {list(authority_ttm_full_data.keys())}")
                        
                except Exception as e:
                    logging.error(f"🎨 权威版本完整数据获取失败: {e}")
                    authority_macd_full_data = None
                    authority_mmt_full_data = None
                    authority_rsi_full_data = None
                    authority_ttm_full_data = None
                
                logging.info(f"✅ 权威版本MCSI计算完成: {len(auth_macd_scores)}个评分点")
                logging.info(f"   权威MACD评分范围: {min(auth_macd_scores):.2f} ~ {max(auth_macd_scores):.2f}")
                logging.info(f"   权威MMT评分范围: {min(auth_mmt_scores):.2f} ~ {max(auth_mmt_scores):.2f}")
                logging.info(f"   权威RSI评分范围: {min(auth_rsi_scores):.2f} ~ {max(auth_rsi_scores):.2f}")
                logging.info(f"   权威TTM评分范围: {min(auth_ttm_scores):.2f} ~ {max(auth_ttm_scores):.2f}")
                
            except Exception as e:
                logging.error(f"🔧 权威版本MCSI计算失败: {e}")
                authority_macd_score = [0.0] * len(stock_data)
                authority_mmt_score = [0.0] * len(stock_data)
                authority_rsi_score = [0.0] * len(stock_data)
                authority_ttm_score = [0.0] * len(stock_data)
        else:
            logging.warning("🔧 权威版本MCSI单元不可用")
            authority_macd_score = [0.0] * len(stock_data)
            authority_mmt_score = [0.0] * len(stock_data)
            authority_rsi_score = [0.0] * len(stock_data)
            authority_ttm_score = [0.0] * len(stock_data)

        # 计算移动平均线
        ma5 = stock_data['close'].rolling(window=5).mean()
        ma20 = stock_data['close'].rolling(window=20).mean()
        ma50 = stock_data['close'].rolling(window=50).mean()
        ma200 = stock_data['close'].rolling(window=200).mean()

        # 准备旧系统格式的数据
        dates = []
        prices = {
            'open': [],
            'high': [],
            'low': [],
            'close': []
        }
        ma = {
            'ma5': [],
            'ma20': [],
            'ma50': [],
            'ma200': []
        }
        macd = {
            'macd': [],
            'signal': [],
            'histogram': []
        }
        rsi = []

        # 填充数据
        for i in range(len(stock_data)):
            # 日期 - 智能处理时间列（支持timestamp或date列）
            if 'date' in stock_data.columns:
                real_timestamp = stock_data.iloc[i]['date']
            elif 'timestamp' in stock_data.columns:
                real_timestamp = stock_data.iloc[i]['timestamp']
            else:
                # 如果没有时间列，使用索引生成虚拟日期
                real_timestamp = f"2024-01-{i+1:02d}"

            if isinstance(real_timestamp, str):
                # 如果是字符串，转换为datetime
                real_timestamp = pd.to_datetime(real_timestamp)

            if hasattr(real_timestamp, 'strftime'):
                dates.append(real_timestamp.strftime('%Y-%m-%d'))
            else:
                dates.append(str(real_timestamp))

            # 价格数据
            prices['open'].append(float(stock_data.iloc[i]['open']))
            prices['high'].append(float(stock_data.iloc[i]['high']))
            prices['low'].append(float(stock_data.iloc[i]['low']))
            prices['close'].append(float(stock_data.iloc[i]['close']))

            # MA数据
            ma['ma5'].append(float(ma5.iloc[i]) if not pd.isna(ma5.iloc[i]) else 0)
            ma['ma20'].append(float(ma20.iloc[i]) if not pd.isna(ma20.iloc[i]) else 0)
            ma['ma50'].append(float(ma50.iloc[i]) if not pd.isna(ma50.iloc[i]) else 0)
            ma['ma200'].append(float(ma200.iloc[i]) if not pd.isna(ma200.iloc[i]) else 0)

            # MACD数据
            if i < len(macd_series['macd']):
                macd['macd'].append(float(macd_series['macd'].iloc[i]) if not pd.isna(macd_series['macd'].iloc[i]) else 0)
                macd['signal'].append(float(macd_series['signal'].iloc[i]) if not pd.isna(macd_series['signal'].iloc[i]) else 0)
                macd['histogram'].append(float(macd_series['histogram'].iloc[i]) if not pd.isna(macd_series['histogram'].iloc[i]) else 0)
            else:
                macd['macd'].append(0)
                macd['signal'].append(0)
                macd['histogram'].append(0)

            # RSI数据
            if i < len(rsi_series):
                rsi.append(float(rsi_series.iloc[i]) if not pd.isna(rsi_series.iloc[i]) else 50)
            else:
                rsi.append(50)

        # 🔒 安全的MCSI指标数据 - 仅返回评分时间序列，保护算法细节
        def safe_score_array(score_data, data_length):
            """安全处理评分数据，支持单值和数组"""
            if score_data is None:
                return [0.0] * data_length
            
            # 如果是数组/列表，直接使用（但验证长度）
            if hasattr(score_data, '__len__') and not isinstance(score_data, str):
                try:
                    score_list = list(score_data)
                    # 确保长度匹配
                    if len(score_list) == data_length:
                        return [float(x) if x is not None else 0.0 for x in score_list]
                    elif len(score_list) > data_length:
                        return [float(x) if x is not None else 0.0 for x in score_list[:data_length]]
                    else:
                        # 长度不足，用最后一个值填充
                        result = [float(x) if x is not None else 0.0 for x in score_list]
                        last_value = result[-1] if result else 0.0
                        result.extend([last_value] * (data_length - len(result)))
                        return result
                except (TypeError, ValueError):
                    pass
            
            # 如果是单个数值，复制到所有时间点
            try:
                single_score = float(score_data)
                return [single_score] * data_length
            except (TypeError, ValueError):
                return [0.0] * data_length

        data_length = len(stock_data)
        
        # 🔒 构建8个MCSI指标响应 - 统一接口版本 + 权威版本并行显示
        mcsi_indicators = {
            # 统一接口版本（Step 1-2 完成）
            'mcsi_macd': {
                # 🔒 只返回评分时间序列，不暴露MACD线、信号线、柱状图等算法细节
                'macd_score': safe_score_array(mcsi_macd_score, data_length),
                'available': mcsi_macd_score is not None,
                'version': 'unified_interface',
                'description': 'MCSI MACD统一接口版本'
            },
            'mcsi_mmt': {
                # 🔒 只返回评分时间序列，不暴露CSI缓冲区、轨道线、背离标记等算法细节
                'mmt_score': safe_score_array(mcsi_mmt_score, data_length),
                'available': mcsi_mmt_score is not None,
                'version': 'unified_interface',
                'description': 'MCSI MMT统一接口版本'
            },
            'mcsi_rsi': {
                # 🔒 只返回评分时间序列，不暴露日周线RSI、上下轨、交易信号等算法细节
                'rsi_score': safe_score_array(mcsi_rsi_score, data_length),
                'available': mcsi_rsi_score is not None,
                'version': 'unified_interface',
                'description': 'MCSI RSI统一接口版本'
            },
            'mcsi_ttm': {
                # 🔒 只返回评分时间序列，不暴露形态识别、计数器、信号等算法细节
                'ttm_score': safe_score_array(mcsi_ttm_score, data_length),
                'available': mcsi_ttm_score is not None,
                'version': 'unified_interface',
                'description': 'MCSI TTM统一接口版本'
            },
            
            # 权威版本（TV-code/py-code直接实现）- 包含Pine Script原生元素
            'authority_mcsi_macd': {
                # 🔧 权威版本评分时间序列 - Pine Script完全一致的算分逻辑
                'macd_score': safe_score_array(authority_macd_score, data_length),
                'available': authority_macd_score is not None,
                'version': 'authority',
                'description': '权威MCSI MACD（TV-code/py-code）',
                # 🎨 Pine Script原生元素 - MACD线、信号线、柱状图、动态阈值
                'pine_data': authority_macd_full_data
            },
            'authority_mcsi_mmt': {
                # 🔧 权威版本评分时间序列 - Pine Script完全一致的算分逻辑  
                'mmt_score': safe_score_array(authority_mmt_score, data_length),
                'available': authority_mmt_score is not None,
                'version': 'authority',
                'description': '权威MCSI MMT（TV-code/py-code）',
                # 🎨 Pine Script原生元素 - CSI缓冲区、上下轨道、背离信号  
                'pine_data': authority_mmt_full_data
            },
            'authority_mcsi_rsi': {
                # 🔧 权威版本评分时间序列 - Pine Script完全一致的算分逻辑
                'rsi_score': safe_score_array(authority_rsi_score, data_length),
                'available': authority_rsi_score is not None,
                'version': 'authority',
                'description': '权威MCSI RSI（TV-code/py-code）',
                # 🎨 Pine Script原生元素 - 日周线RSI、上下带、信号标记
                'pine_data': authority_rsi_full_data
            },
            'authority_mcsi_ttm': {
                # 🔧 权威版本评分时间序列 - Pine Script完全一致的算分逻辑
                'ttm_score': safe_score_array(authority_ttm_score, data_length),
                'available': authority_ttm_score is not None,
                'version': 'authority',
                'description': '权威MCSI TTM（TV-code/py-code）',
                # 🎨 Pine Script原生元素 - TD序列、形态识别、信号标记
                'pine_data': authority_ttm_full_data
            }
        }

        # 返回旧系统兼容格式 - 数据嵌套在data字段中
        return jsonify({
            'success': True,
            'stock_code': stock_code,
            'data_points': len(stock_data),
            'dates': dates,
            'prices': prices,
            'ma': ma,
            'macd': macd,
            'rsi': rsi,
            'mcsi_indicators': mcsi_indicators
        })

    except Exception as e:
        logging.error(f"获取图表数据失败: {str(e)}")
        return jsonify({'success': False, 'message': f'获取图表数据失败: {str(e)}'})

@app.route('/api/config')
def get_config():
    """获取配置信息"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500
        
        config = scorer.get_config()
        groups = scorer.list_groups()
        weights = scorer.get_group_weights()
        
        return jsonify({
            'config': config,
            'groups': groups,
            'weights': weights,
            'status': 'success'
        })
    except Exception as e:
        logging.error(f"获取配置失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/config', methods=['POST'])
def update_config():
    """更新配置"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500
        
        data = request.get_json()
        config = data.get('config', {})
        
        scorer.update_config(config)
        scorer.save_config()
        
        return jsonify({
            'message': '配置更新成功',
            'config': scorer.get_config(),
            'status': 'success'
        })
    except Exception as e:
        logging.error(f"更新配置失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/group-weights', methods=['GET', 'POST'])
def handle_group_weights():
    """处理分组权重配置"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500

        if request.method == 'GET':
            # 获取当前分组权重
            group_weights = scorer.group_manager.group_weights
            return jsonify({
                'success': True,
                'weights': group_weights
            })

        elif request.method == 'POST':
            # 更新分组权重
            data = request.get_json()
            new_weights = data.get('weights', {})

            # 验证权重总和
            total_weight = sum(new_weights.values())
            if abs(total_weight - 1.0) > 0.01:
                return jsonify({
                    'success': False,
                    'message': f'权重总和必须为1.0，当前为{total_weight:.2f}'
                })

            # 更新权重（自动保存）
            scorer.group_manager.set_group_weights(new_weights, auto_save=True)

            # 重新加载配置以确保生效
            scorer.group_manager.load_config()

            return jsonify({
                'success': True,
                'message': '分组权重已更新'
            })

    except Exception as e:
        logging.error(f"处理分组权重失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'处理失败: {str(e)}'
        })

@app.route('/api/unit-weights', methods=['GET', 'POST'])
def handle_unit_weights():
    """处理计分单元权重配置"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500

        if request.method == 'GET':
            # 获取当前计分单元权重
            unit_weights = {}
            for group_id, group in scorer.group_manager.groups.items():
                unit_weights[group_id] = group.get_unit_weights()

            return jsonify({
                'success': True,
                'unit_weights': unit_weights
            })

        elif request.method == 'POST':
            data = request.get_json()
            action = data.get('action')

            if action == 'add':
                # 添加计分单元到分组
                group_id = data.get('group_id')
                unit_id = data.get('unit_id')
                weight = data.get('weight', 1.0)

                if group_id not in scorer.group_manager.groups:
                    return jsonify({
                        'success': False,
                        'message': f'分组 {group_id} 不存在'
                    })

                # 创建计分单元实例
                unit_instance = create_scoring_unit(unit_id)
                if not unit_instance:
                    return jsonify({
                        'success': False,
                        'message': f'无法创建计分单元 {unit_id}'
                    })

                # 添加到分组
                group = scorer.group_manager.groups[group_id]
                group.add_scoring_unit(unit_instance, weight)

                # 保存配置
                scorer.group_manager.save_config()

                return jsonify({
                    'success': True,
                    'message': f'计分单元 {unit_id} 已添加到分组 {group_id}'
                })

            elif action == 'remove':
                # 从分组中移除计分单元
                group_id = data.get('group_id')
                unit_id = data.get('unit_id')

                if group_id in scorer.group_manager.groups:
                    group = scorer.group_manager.groups[group_id]
                    group.remove_scoring_unit(unit_id)
                    scorer.group_manager.save_config()

                return jsonify({
                    'success': True,
                    'message': f'计分单元 {unit_id} 已从分组 {group_id} 移除'
                })

            elif action == 'update_all':
                # 批量更新计分单元权重
                unit_weights = data.get('unit_weights', {})

                for group_id, weights in unit_weights.items():
                    if group_id in scorer.group_manager.groups:
                        group = scorer.group_manager.groups[group_id]
                        group.set_unit_weights(weights, notify_manager=True)

                # 重新加载配置以确保生效
                scorer.group_manager.load_config()

                return jsonify({
                    'success': True,
                    'message': '计分单元权重已更新'
                })

            else:
                return jsonify({
                    'success': False,
                    'message': f'未知操作: {action}'
                })

    except Exception as e:
        logging.error(f"处理计分单元权重失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'处理失败: {str(e)}'
        })

@app.route('/api/weight-config-info')
def get_weight_config_info():
    """获取权重配置信息"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500

        # 获取权重配置管理器信息
        config_info = scorer.group_manager.weight_config_manager.get_config_info()

        return jsonify({
            'success': True,
            'config_info': config_info
        })

    except Exception as e:
        logging.error(f"获取权重配置信息失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取失败: {str(e)}'
        })

@app.route('/api/weight-config-history')
def get_weight_config_history():
    """获取权重配置历史记录"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500

        # 获取配置历史记录
        history = scorer.group_manager.weight_config_manager.config_history

        return jsonify({
            'success': True,
            'history': history[-10:]  # 返回最近10条记录
        })

    except Exception as e:
        logging.error(f"获取权重配置历史失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取失败: {str(e)}'
        })

@app.route('/api/weight-config-restore', methods=['POST'])
def restore_weight_config():
    """恢复权重配置"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500

        data = request.get_json()
        history_index = data.get('index', -1)

        # 恢复配置
        success = scorer.group_manager.weight_config_manager.restore_from_history(history_index)

        if success:
            # 重新加载配置
            scorer.group_manager.load_config()

            return jsonify({
                'success': True,
                'message': '权重配置已恢复'
            })
        else:
            return jsonify({
                'success': False,
                'message': '恢复权重配置失败'
            })

    except Exception as e:
        logging.error(f"恢复权重配置失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'恢复失败: {str(e)}'
        })

def create_scoring_unit(unit_id):
    """创建计分单元实例"""
    try:
        if unit_id == 'rsi_unit':
            from core.scoring_units.rsi_scoring_unit import RSIScoringUnit
            return RSIScoringUnit()
        elif unit_id == 'macd_unit':
            from core.scoring_units.macd_scoring_unit import MACDScoringUnit
            return MACDScoringUnit()
        elif unit_id == 'trend_unit':
            from core.scoring_units.trend_scoring_unit import TrendScoringUnit
            return TrendScoringUnit()
        elif unit_id == 'wave_unit':
            from core.scoring_units.wave_scoring_unit import WaveScoringUnit
            return WaveScoringUnit()
        elif unit_id == 'mcsi_macd_unit':
            # 使用已经实例化的MCSI统一接口单元
            return mcsi_macd_unit
        elif unit_id == 'mcsi_mmt_unit':
            # 使用已经实例化的MCSI统一接口单元
            return mcsi_mmt_unit
        elif unit_id == 'mcsi_rsi_unit':
            # 使用已经实例化的MCSI统一接口单元
            return mcsi_rsi_unit
        elif unit_id == 'mcsi_ttm_unit':
            # 使用已经实例化的MCSI统一接口单元（TTM替换TD9）
            return mcsi_ttm_unit
        else:
            return None
    except Exception as e:
        logging.error(f"创建计分单元失败: {str(e)}")
        return None

if __name__ == '__main__':
    # 初始化评分器
    if init_scorer():
        logging.info("启动新的Web应用...")
        app.run(
            host='0.0.0.0',
            port=50506,
            debug=True
        )
    else:
        logging.error("评分器初始化失败，无法启动Web应用")
