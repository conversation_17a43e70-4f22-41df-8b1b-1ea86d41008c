<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI市场分析系统 - 新架构</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .bg-gradient-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .bg-gradient-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .form-range::-webkit-slider-thumb {
            background: #667eea;
        }
        .form-range::-moz-range-thumb {
            background: #667eea;
            border: none;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .btn-group .btn {
            border-radius: 0.375rem !important;
            margin-left: 0.25rem;
        }
        .modal-content {
            border: none;
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
        }
        .alert {
            border: none;
            border-radius: 0.5rem;
        }
        .badge {
            font-size: 0.875em;
        }
        .border-primary {
            border-color: #667eea !important;
        }
        .border-secondary {
            border-color: #6c757d !important;
        }
        .text-primary {
            color: #667eea !important;
        }
        .bg-light {
            background-color: #f8f9fa !important;
        }
    </style>
    <style>
        .group-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        .group-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .weight-slider {
            width: 100%;
        }
        .score-badge {
            font-size: 1.1em;
            font-weight: bold;
        }
        .grade-A { background-color: #28a745; }
        .grade-B { background-color: #17a2b8; }
        .grade-C { background-color: #ffc107; color: #000; }
        .grade-D { background-color: #fd7e14; }
        .grade-E { background-color: #dc3545; }
        .grade-F { background-color: #6c757d; }
        .config-panel {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .unit-item {
            background-color: #fff;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line me-2"></i>
                AI市场分析系统 (新架构)
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text" id="status-text">就绪</span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4" style="max-width: 100%; padding-left: 15px; padding-right: 15px;">
        <!-- 控制面板 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            系统控制面板
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <button id="start-analysis" class="btn btn-success btn-lg">
                                    <i class="fas fa-play me-2"></i>
                                    开始分析
                                </button>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="progress" style="height: 25px; display: none;" id="progress-bar">
                                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 评分系统配置 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-gradient-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            评分系统配置
                        </h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="row">
                            <!-- 分组配置区域 -->
                            <div class="col-lg-8">
                                <div class="d-flex align-items-center justify-content-between mb-3">
                                    <div class="d-flex align-items-center">
                                        <h6 class="text-muted mb-0 me-3">
                                            <i class="fas fa-layer-group me-2"></i>
                                            分组配置
                                        </h6>
                                        <small class="text-muted">配置评分分组和权重</small>
                                    </div>
                                    <button class="btn btn-outline-primary btn-sm" onclick="showAddGroupModal()" title="添加新的评分分组">
                                        <i class="fas fa-plus me-1"></i>
                                        添加分组
                                    </button>
                                </div>
                                <div id="groups-container" class="mb-4">
                                    <!-- 分组配置将在这里显示 -->
                                </div>
                            </div>

                            <!-- 可用计分单元区域 -->
                            <div class="col-lg-4">
                                <div class="d-flex align-items-center mb-3">
                                    <h6 class="text-muted mb-0 me-3">
                                        <i class="fas fa-puzzle-piece me-2"></i>
                                        可用计分单元
                                    </h6>
                                    <small class="text-muted">拖拽到分组中使用</small>
                                </div>
                                <div id="scoring-units-container">
                                    <!-- 计分单元将在这里显示 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选和搜索控制区 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-gradient-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>
                            筛选和搜索
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label fw-bold">
                                    <i class="fas fa-search me-1"></i>
                                    搜索股票
                                </label>
                                <input type="text" id="search-input" class="form-control"
                                       placeholder="输入股票代码或名称">
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    在分析结果中搜索
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label fw-bold">
                                    <i class="fas fa-layer-group me-1"></i>
                                    市场分类
                                </label>
                                <select id="category-filter" class="form-select">
                                    <option value="">全部分类</option>
                                    <option value="A股">A股</option>
                                    <option value="港股">港股</option>
                                    <option value="美股">美股</option>
                                    <option value="中国指数">中国指数</option>
                                    <option value="申万行业指数">申万行业指数</option>
                                    <option value="美国指数">美国指数</option>
                                    <option value="ETF基金">ETF基金</option>
                                    <option value="加密货币">加密货币</option>
                                    <option value="商品">商品</option>
                                    <option value="其他">其他</option>
                                </select>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    筛选分析结果
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label fw-bold">
                                    <i class="fas fa-database me-1"></i>
                                    数据量
                                </label>
                                <select id="data-limit-filter" class="form-select">
                                    <option value="100">100天 (约3个月)</option>
                                    <option value="250">250天 (约1年)</option>
                                    <option value="500" selected>500天 (约2年)</option>
                                    <option value="1000">1000天 (约4年)</option>
                                    <option value="2000">2000天 (约8年)</option>
                                </select>
                                <div class="form-text">
                                    <i class="fas fa-exclamation-triangle me-1 text-warning"></i>
                                    更改数据量需要重新分析
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分析结果 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            分析结果
                        </h5>
                        <div>
                            <select id="sort-select" class="form-select form-select-sm">
                                <option value="score">按综合分数排序</option>
                                <option value="trend">按趋势分数排序</option>
                                <option value="oscillation">按震荡分数排序</option>
                                <option value="grade">按等级排序</option>
                                <option value="confidence">按置信度排序</option>
                                <option value="time">按时间排序</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <span class="badge bg-primary fs-6" id="results-count">共 0 条结果</span>
                            </div>
                            <div class="text-muted small">
                                <i class="fas fa-info-circle me-1"></i>
                                使用上方筛选功能可以快速查找特定股票
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>股票代码</th>
                                        <th>股票名称</th>
                                        <th>综合分数</th>
                                        <th>趋势分数</th>
                                        <th>震荡分数</th>
                                        <th>等级</th>
                                        <th>置信度</th>
                                        <th>分析时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="results-table">
                                    <!-- 结果将在这里动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 自定义分组模态框 -->
    <div class="modal fade" id="customGroupModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建自定义分组</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="custom-group-form">
                        <div class="mb-3">
                            <label for="group-id" class="form-label">分组ID</label>
                            <input type="text" class="form-control" id="group-id" required>
                        </div>
                        <div class="mb-3">
                            <label for="group-name" class="form-label">分组名称</label>
                            <input type="text" class="form-control" id="group-name" required>
                        </div>
                        <div class="mb-3">
                            <label for="group-description" class="form-label">分组描述</label>
                            <textarea class="form-control" id="group-description" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="group-weight" class="form-label">分组权重</label>
                            <input type="number" class="form-control" id="group-weight" value="1.0" step="0.1" min="0">
                        </div>
                        <div class="mb-3">
                            <label for="scoring-method" class="form-label">评分方法</label>
                            <select class="form-select" id="scoring-method">
                                <option value="weighted_average">加权平均</option>
                                <option value="max">最大值</option>
                                <option value="min">最小值</option>
                                <option value="median">中位数</option>
                                <option value="consensus">共识方法</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="create-group-btn">创建分组</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加分组模态框 -->
    <div class="modal fade" id="addGroupModal" tabindex="-1" aria-labelledby="addGroupModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="addGroupModalLabel">
                        <i class="fas fa-plus me-2"></i>
                        添加新分组
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>说明:</strong> 创建新的评分分组，可以自由组合计分单元。新分组将在分析结果表中显示为独立的一列。
                    </div>

                    <form id="addGroupForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="group-id" class="form-label fw-bold">
                                        <i class="fas fa-tag me-1"></i>
                                        分组ID
                                    </label>
                                    <input type="text" class="form-control" id="group-id" placeholder="例如: custom_group_1" required>
                                    <div class="form-text">分组的唯一标识符，只能包含字母、数字和下划线</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="group-name" class="form-label fw-bold">
                                        <i class="fas fa-signature me-1"></i>
                                        分组名称
                                    </label>
                                    <input type="text" class="form-control" id="group-name" placeholder="例如: 自定义分组1" required>
                                    <div class="form-text">分组的显示名称</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="group-description" class="form-label fw-bold">
                                <i class="fas fa-align-left me-1"></i>
                                分组描述
                            </label>
                            <textarea class="form-control" id="group-description" rows="2" placeholder="描述这个分组的用途和特点"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-percentage me-1"></i>
                                        初始权重
                                    </label>
                                    <div class="d-flex align-items-center">
                                        <input type="range" class="form-range me-3" id="group-weight-slider" min="0.1" max="1.0" step="0.1" value="0.5" oninput="updateGroupWeightDisplay()">
                                        <span class="badge bg-primary fs-6" id="group-weight-display">0.5</span>
                                    </div>
                                    <div class="form-text">设置分组在整体评分中的权重</div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-primary" onclick="createGroupConfirm()">
                        <i class="fas fa-plus me-1"></i>创建分组
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 分组权重配置模态框 -->
    <div class="modal fade" id="groupWeightModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">分组权重配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">趋势分组权重</label>
                        <div class="d-flex align-items-center">
                            <input type="range" class="form-range me-3" id="trendGroupWeight" min="0" max="100" value="70" oninput="updateWeights('trend')">
                            <span class="badge bg-primary" id="trendWeightDisplay">70%</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">震荡分组权重</label>
                        <div class="d-flex align-items-center">
                            <input type="range" class="form-range me-3" id="oscillationGroupWeight" min="0" max="100" value="30" oninput="updateWeights('oscillation')">
                            <span class="badge bg-secondary" id="oscillationWeightDisplay">30%</span>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <small><i class="fas fa-info-circle me-1"></i>权重总和必须为100%。调整一个分组权重时，另一个会自动调整。</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveGroupWeights()">保存配置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 计分单元权重配置模态框 -->
    <div class="modal fade" id="unitWeightModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-gradient-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-sliders-h me-2"></i>
                        计分单元权重配置
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>说明：</strong>每个分组内计分单元权重总和为100%，调整一个单元权重时，其他单元权重会自动调整。
                    </div>

                    <div class="row">
                        <!-- 趋势分组 -->
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">
                                            <i class="fas fa-chart-line me-2"></i>
                                            趋势分组
                                        </h6>
                                        <span class="badge bg-light text-primary" id="trend-total-weight">100%</span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div id="trend-units-config" class="mb-3">
                                        <!-- 趋势分组的计分单元权重配置 -->
                                    </div>
                                    <button class="btn btn-outline-primary btn-sm w-100" onclick="addUnitToGroup('trend_group')">
                                        <i class="fas fa-plus me-1"></i>添加计分单元
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 震荡分组 -->
                        <div class="col-md-6">
                            <div class="card border-secondary">
                                <div class="card-header bg-secondary text-white">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">
                                            <i class="fas fa-wave-square me-2"></i>
                                            震荡分组
                                        </h6>
                                        <span class="badge bg-light text-secondary" id="oscillation-total-weight">100%</span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div id="oscillation-units-config" class="mb-3">
                                        <!-- 震荡分组的计分单元权重配置 -->
                                    </div>
                                    <button class="btn btn-outline-secondary btn-sm w-100" onclick="addUnitToGroup('oscillation_group')">
                                        <i class="fas fa-plus me-1"></i>添加计分单元
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-primary" onclick="saveUnitWeights()">
                        <i class="fas fa-save me-1"></i>保存配置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加计分单元模态框 -->
    <div class="modal fade" id="addUnitModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-gradient-success text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>
                        添加计分单元到分组
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">
                                    <i class="fas fa-layer-group me-1"></i>
                                    选择目标分组
                                </label>
                                <select class="form-select form-select-lg" id="target-group">
                                    <option value="trend_group">
                                        <i class="fas fa-chart-line"></i> 趋势分组
                                    </option>
                                    <option value="oscillation_group">
                                        <i class="fas fa-wave-square"></i> 震荡分组
                                    </option>
                                </select>
                                <div class="form-text">选择要添加计分单元的分组</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">
                                    <i class="fas fa-puzzle-piece me-1"></i>
                                    选择计分单元
                                </label>
                                <select class="form-select form-select-lg" id="available-units">
                                    <optgroup label="传统计分单元">
                                        <option value="rsi_unit">RSI计分单元 - 相对强弱指标</option>
                                        <option value="macd_unit">MACD计分单元 - 移动平均收敛发散</option>
                                        <option value="trend_unit">趋势计分单元 - 趋势分析</option>
                                        <option value="wave_unit">波段计分单元 - 波段分析</option>
                                    </optgroup>
                                    <optgroup label="MCSI增强计分单元">
                                        <option value="mcsi_macd_unit">MCSI MACD计分单元 - 增强版MACD信号</option>
                                        <option value="mcsi_mmt_unit">MCSI MMT计分单元 - 多重复合动量</option>
                                        <option value="mcsi_rsi_unit">MCSI RSI计分单元 - 增强版RSI信号</option>
                                        <option value="mcsi_td9_unit">MCSI TD9计分单元 - TD序列计数</option>
                                    </optgroup>
                                </select>
                                <div class="form-text">选择要添加的计分单元类型</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label fw-bold">
                                    <i class="fas fa-percentage me-1"></i>
                                    初始权重百分比
                                </label>
                                <div class="d-flex align-items-center">
                                    <input type="range" class="form-range me-3" id="unit-weight-slider" min="10" max="100" step="5" value="25" oninput="updateWeightDisplay()">
                                    <span class="badge bg-primary fs-6" id="weight-display">25%</span>
                                </div>
                                <div class="form-text">设置该计分单元在分组中的初始权重，其他单元权重会自动调整</div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>注意：</strong>添加新计分单元后，该分组内所有计分单元的权重会重新分配以保持总和为100%。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-success" onclick="addUnitToGroupConfirm()">
                        <i class="fas fa-plus me-1"></i>添加到分组
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let groups = {};
        let weights = {};
        let results = [];
        let analysisInterval = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadGroups();
            loadScoringUnits();
            // 不在页面初始化时自动加载结果，避免显示旧数据
            // loadResults();

            // 显示初始状态
            showInitialState();
            
            // 绑定事件
            document.getElementById('start-analysis').addEventListener('click', startAnalysis);

            // 绑定筛选和搜索事件
            document.getElementById('search-input').addEventListener('input', filterResults);
            document.getElementById('category-filter').addEventListener('change', filterResults);
            document.getElementById('data-limit-filter').addEventListener('change', onDataLimitChange);
            document.getElementById('refresh-results').addEventListener('click', loadResults);
            document.getElementById('debug-results').addEventListener('click', debugResults);
            document.getElementById('test-render').addEventListener('click', testRender);
            document.getElementById('save-config').addEventListener('click', saveConfig);
            document.getElementById('add-custom-group').addEventListener('click', showCustomGroupModal);
            document.getElementById('create-group-btn').addEventListener('click', createCustomGroup);
            document.getElementById('sort-select').addEventListener('change', sortResults);
        });

        // 加载分组信息
        async function loadGroups() {
            try {
                const response = await fetch('/api/groups');
                const data = await response.json();
                
                if (data.status === 'success') {
                    groups = data.groups;
                    weights = data.weights;
                    renderGroups();
                }
            } catch (error) {
                console.error('加载分组失败:', error);
            }
        }

        // 渲染分组
        function renderGroups() {
            const container = document.getElementById('groups-container');
            container.innerHTML = '';

            groups.forEach(group => {
                const groupCard = document.createElement('div');
                groupCard.className = 'group-card p-3';
                groupCard.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">${group.name}</h6>
                        <div>
                            <button class="btn btn-sm btn-outline-primary me-2" onclick="showGroupUnitConfig('${group.group_id}')" title="配置此分组的计分单元权重">
                                <i class="fas fa-cogs me-1"></i>
                                单元配置
                            </button>
                            <span class="badge ${group.enabled ? 'bg-success' : 'bg-secondary'}">
                                ${group.enabled ? '启用' : '禁用'}
                            </span>
                            ${group.group_id.includes('custom') ?
                                `<button class="btn btn-sm btn-outline-danger ms-2" onclick="removeGroup('${group.group_id}')">
                                    <i class="fas fa-trash"></i>
                                </button>` : ''
                            }
                        </div>
                    </div>
                    <p class="text-muted small mb-2">${group.description}</p>
                    <div class="mb-2">
                        <label class="form-label small">权重: <span id="weight-${group.group_id}">${weights[group.group_id] || 1.0}</span></label>
                        <input type="range" class="weight-slider"
                               min="0" max="1" step="0.1"
                               value="${weights[group.group_id] || 1.0}"
                               onchange="updateWeight('${group.group_id}', this.value)">
                    </div>
                    <div class="small">
                        <strong>计分单元:</strong> ${Object.keys(group.unit_weights || {}).join(', ') || '无'}
                    </div>
                `;
                container.appendChild(groupCard);
            });
        }

        // 更新权重
        async function updateWeight(groupId, weight) {
            weights[groupId] = parseFloat(weight);
            document.getElementById(`weight-${groupId}`).textContent = weight;
            
            try {
                await fetch('/api/groups/weights', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({weights: weights})
                });
            } catch (error) {
                console.error('更新权重失败:', error);
            }
        }

        // 加载计分单元
        async function loadScoringUnits() {
            try {
                const response = await fetch('/api/scoring_units');
                const data = await response.json();
                
                if (data.status === 'success') {
                    renderScoringUnits(data.scoring_units);
                }
            } catch (error) {
                console.error('加载计分单元失败:', error);
            }
        }

        // 渲染计分单元
        function renderScoringUnits(units) {
            const container = document.getElementById('scoring-units-container');
            container.innerHTML = '';

            units.forEach(unit => {
                const unitItem = document.createElement('div');
                unitItem.className = 'unit-item';
                unitItem.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="fw-bold">${unit}</span>
                        <span class="badge bg-info">可用</span>
                    </div>
                `;
                container.appendChild(unitItem);
            });
        }

        // 显示初始状态
        function showInitialState() {
            const tbody = document.getElementById('results-table');
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" class="text-center text-muted py-5">
                            <i class="fas fa-chart-line fa-3x mb-3 text-secondary"></i>
                            <h5>欢迎使用AI市场分析系统</h5>
                            <p>点击上方"开始分析"按钮开始股票分析</p>
                        </td>
                    </tr>
                `;
            }
        }

        // 开始分析
        async function startAnalysis() {
            try {
                // 防止重复点击
                const button = document.getElementById('start-analysis');
                if (button.disabled) {
                    console.log('分析正在进行中，忽略重复点击');
                    return;
                }

                button.disabled = true;
                document.getElementById('progress-bar').style.display = 'block';

                // 清除之前的定时器
                if (analysisInterval) {
                    clearInterval(analysisInterval);
                    analysisInterval = null;
                }

                // 收集分析参数
                const analysisParams = {
                    data_limit: parseInt(document.getElementById('data-limit-filter').value),
                    force_refresh: true  // 默认强制刷新
                };

                showAlert(`开始分析，数据量：${analysisParams.data_limit}天`, 'info');

                // 清除表格内容，显示分析中状态
                const tbody = document.getElementById('results-table');
                if (tbody) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="9" class="text-center text-muted py-5">
                                <i class="fas fa-spinner fa-spin fa-2x mb-3 text-primary"></i>
                                <h5>正在分析中...</h5>
                                <p>请稍候，系统正在分析股票数据</p>
                            </td>
                        </tr>
                    `;
                }

                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(analysisParams)
                });

                const data = await response.json();

                if (data.status === 'success') {
                    // 开始轮询状态
                    analysisInterval = setInterval(checkAnalysisStatus, 1000);
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                console.error('开始分析失败:', error);
                showAlert('开始分析失败: ' + error.message, 'error');
                document.getElementById('start-analysis').disabled = false;
                document.getElementById('progress-bar').style.display = 'none';
            }
        }

        // 检查分析状态
        async function checkAnalysisStatus() {
            try {
                const response = await fetch('/api/status');
                const status = await response.json();

                document.getElementById('status-text').textContent = status.message;

                if (status.total > 0) {
                    const progress = (status.progress / status.total) * 100;
                    document.querySelector('.progress-bar').style.width = progress + '%';
                }

                if (!status.is_running) {
                    // 清除定时器
                    if (analysisInterval) {
                        clearInterval(analysisInterval);
                        analysisInterval = null;
                    }

                    // 恢复按钮状态
                    document.getElementById('start-analysis').disabled = false;
                    document.getElementById('progress-bar').style.display = 'none';

                    // 显示完成消息
                    showAlert('分析完成！正在加载结果...', 'success');

                    // 加载结果，增加延迟并添加重试机制
                    setTimeout(async () => {
                        console.log('分析完成，开始加载结果...');

                        // 尝试多次加载结果，确保数据已准备好
                        let retryCount = 0;
                        const maxRetries = 3;

                        while (retryCount < maxRetries) {
                            try {
                                await loadResults();

                                // 检查是否成功加载了结果
                                if (results && results.length > 0) {
                                    console.log(`成功加载 ${results.length} 个结果`);
                                    showAlert(`分析完成！共获得 ${results.length} 个结果`, 'success');
                                    break;
                                } else {
                                    console.log(`第 ${retryCount + 1} 次尝试，未获得结果，等待重试...`);
                                    retryCount++;
                                    if (retryCount < maxRetries) {
                                        await new Promise(resolve => setTimeout(resolve, 1000));
                                    }
                                }
                            } catch (error) {
                                console.error(`第 ${retryCount + 1} 次加载结果失败:`, error);
                                retryCount++;
                                if (retryCount < maxRetries) {
                                    await new Promise(resolve => setTimeout(resolve, 1000));
                                }
                            }
                        }

                        if (retryCount >= maxRetries && (!results || results.length === 0)) {
                            showAlert('分析完成，但未能加载结果。请手动点击"刷新结果"按钮。', 'warning');
                        }
                    }, 1000); // 增加延迟到1秒
                }
            } catch (error) {
                console.error('检查状态失败:', error);
                // 出错时也要恢复按钮状态
                if (analysisInterval) {
                    clearInterval(analysisInterval);
                    analysisInterval = null;
                }
                document.getElementById('start-analysis').disabled = false;
                document.getElementById('progress-bar').style.display = 'none';
            }
        }

        // 加载结果
        async function loadResults() {
            try {
                console.log('开始加载结果...');
                const response = await fetch('/api/results');
                console.log('响应状态:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                console.log('API响应:', data);
                console.log('数据状态:', data.status || data.success);

                let newResults = [];

                if (data.success && data.data && data.data.items) {
                    // 兼容旧系统API格式
                    newResults = data.data.items;
                    console.log(`使用旧系统格式，获得 ${newResults.length} 个结果`);
                } else if (data.status === 'success' && data.results) {
                    // 新系统API格式
                    newResults = data.results;
                    console.log(`使用新系统格式，获得 ${newResults.length} 个结果`);
                } else {
                    console.error('API返回未知格式:', data);
                    throw new Error('API返回数据格式不正确');
                }

                // 更新全局results变量
                results = newResults;
                console.log(`设置results变量，长度: ${results.length}`);

                // 渲染结果
                console.log('调用renderResults...');
                renderResults();
                console.log('renderResults调用完成');

                return results.length; // 返回结果数量用于检查

            } catch (error) {
                console.error('加载结果失败:', error);
                showAlert(`加载结果失败: ${error.message}`, 'error');
                throw error; // 重新抛出错误以便上层处理
            }
        }

        // 调试函数
        function debugResults() {
            console.log('=== 调试信息 ===');
            console.log('results变量:', results);
            console.log('results类型:', typeof results);
            console.log('results长度:', results ? results.length : 'undefined');

            if (results && results.length > 0) {
                console.log('第一个结果:', results[0]);
            }

            const tbody = document.getElementById('results-table');
            console.log('tbody元素:', tbody);
            console.log('tbody子元素数量:', tbody ? tbody.children.length : 'tbody不存在');

            // 手动调用API
            fetch('/api/results')
                .then(response => response.json())
                .then(data => {
                    console.log('手动API调用结果:', data);

                    // 手动设置结果并渲染
                    if (data.success && data.data && data.data.items) {
                        console.log('手动设置results变量');
                        results = data.data.items;
                        console.log('调用renderResults');
                        renderResults();
                        alert(`成功加载 ${data.data.items.length} 个结果并尝试渲染`);
                    } else {
                        alert(`API状态: ${data.success}, 结果数量: ${data.data ? data.data.items?.length || 0 : 0}`);
                    }
                })
                .catch(error => {
                    console.error('手动API调用失败:', error);
                    alert('API调用失败: ' + error.message);
                });
        }

        // 测试函数 - 添加一行测试数据
        function testRender() {
            const tbody = document.getElementById('results-table');
            if (tbody) {
                tbody.innerHTML = '<tr><td colspan="10" style="background-color: yellow;">测试行 - 如果你看到这个，说明JavaScript和DOM操作正常</td></tr>';
                alert('测试数据已添加到表格中');
            } else {
                alert('找不到results-table元素');
            }
        }

        // 分组分数的颜色样式函数
        function getScoreClass(score) {
            if (score >= 2) return 'text-success fw-bold';
            if (score >= 1) return 'text-success';
            if (score <= -2) return 'text-danger fw-bold';
            if (score <= -1) return 'text-danger';
            return 'text-muted';
        }

        // 渲染结果
        function renderResults() {
            console.log('renderResults开始执行');
            console.log('results变量:', results);
            console.log('results长度:', results ? results.length : 'undefined');

            const tbody = document.getElementById('results-table');
            console.log('tbody元素:', tbody);

            if (!tbody) {
                console.error('找不到results-table元素！');
                return;
            }

            tbody.innerHTML = '';

            if (!results || results.length === 0) {
                console.log('没有结果，显示空状态');
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td colspan="9" class="text-center text-muted">
                        <i class="fas fa-info-circle me-2"></i>
                        暂无分析结果，请点击"开始分析"按钮
                    </td>
                `;
                tbody.appendChild(row);
                return;
            }

            console.log('开始渲染', results.length, '个结果');

            results.forEach(result => {
                try {
                    const row = document.createElement('tr');

                    // 格式化趋势分数 - 显示具体指标分数
                    let trendScoreDisplay = (result.trend_score !== undefined && result.trend_score !== null)
                        ? result.trend_score.toFixed(2) : '0.00';

                    // 如果有分组结果，显示详细信息
                    if (result.group_results && result.group_results.trend_group && result.group_results.trend_group.unit_results) {
                        const unitResults = result.group_results.trend_group.unit_results;
                        const unitScores = [];
                        Object.entries(unitResults).forEach(([unitName, unitData]) => {
                            if (unitData.score !== undefined) {
                                const score = unitData.score.toFixed(1);
                                const shortName = unitName.replace('_unit', '').toUpperCase();
                                unitScores.push(shortName + ':' + score);
                            }
                        });
                        if (unitScores.length > 0) {
                            trendScoreDisplay += '<br><small class="text-muted">(' + unitScores.join(' ') + ')</small>';
                        }
                    }

                    // 格式化震荡分数 - 显示具体指标分数
                    let oscillationScoreDisplay = (result.oscillation_score !== undefined && result.oscillation_score !== null)
                        ? result.oscillation_score.toFixed(2) : '0.00';

                    // 如果有分组结果，显示详细信息
                    if (result.group_results && result.group_results.oscillation_group && result.group_results.oscillation_group.unit_results) {
                        const unitResults = result.group_results.oscillation_group.unit_results;
                        const unitScores = [];
                        Object.entries(unitResults).forEach(([unitName, unitData]) => {
                            if (unitData.score !== undefined) {
                                const score = unitData.score.toFixed(1);
                                const shortName = unitName.replace('_unit', '').toUpperCase();
                                unitScores.push(shortName + ':' + score);
                            }
                        });
                        if (unitScores.length > 0) {
                            oscillationScoreDisplay += '<br><small class="text-muted">(' + unitScores.join(' ') + ')</small>';
                        }
                    }

                    // 兼容新旧字段名
                    const stockCode = result.symbol || result.stock_code || '未知';
                    const stockName = result.name || result.stock_name || '未知';
                    const grade = result.trend_grade || result.grade || 'N/A';

                    row.innerHTML = `
                        <td>${stockCode}</td>
                        <td>${stockName}</td>
                        <td class="fw-bold">${(result.composite_score || 0).toFixed(2)}</td>
                        <td class="${getScoreClass(result.trend_score || 0)}">${trendScoreDisplay}</td>
                        <td class="${getScoreClass(result.oscillation_score || result.rsi_score || 0)}">${oscillationScoreDisplay}</td>
                        <td><span class="badge score-badge grade-${grade.replace(/\+/g, 'plus')}">${grade}</span></td>
                        <td>${((result.confidence || 0) * 100).toFixed(1)}%</td>
                        <td>${result.timestamp ? new Date(result.timestamp).toLocaleString() : '未知'}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewChart('${stockCode}', '${stockName}')">
                                <i class="fas fa-chart-line"></i>
                            </button>
                        </td>
                    `;
                    tbody.appendChild(row);
                } catch (error) {
                    console.error('渲染结果行失败:', error, result);
                }
            });

            // 更新结果数量显示
            updateResultsCount(results.length, results.length);
        }

        // 排序结果
        function sortResults() {
            const sortBy = document.getElementById('sort-select').value;
            
            results.sort((a, b) => {
                switch (sortBy) {
                    case 'score':
                        return b.composite_score - a.composite_score;
                    case 'trend':
                        return (b.trend_score || 0) - (a.trend_score || 0);
                    case 'oscillation':
                        return (b.oscillation_score || 0) - (a.oscillation_score || 0);
                    case 'grade':
                        return a.grade.localeCompare(b.grade);
                    case 'confidence':
                        return b.confidence - a.confidence;
                    case 'time':
                        return new Date(b.timestamp) - new Date(a.timestamp);
                    default:
                        return 0;
                }
            });
            
            renderResults();
        }

        // 显示自定义分组模态框
        function showCustomGroupModal() {
            const modal = new bootstrap.Modal(document.getElementById('customGroupModal'));
            modal.show();
        }

        // 创建自定义分组
        async function createCustomGroup() {
            const groupId = document.getElementById('group-id').value;
            const name = document.getElementById('group-name').value;
            const description = document.getElementById('group-description').value;
            const weight = parseFloat(document.getElementById('group-weight').value);
            const scoringMethod = document.getElementById('scoring-method').value;

            if (!groupId || !name) {
                alert('分组ID和名称不能为空');
                return;
            }

            try {
                const response = await fetch('/api/groups/create', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        group_id: groupId,
                        name: name,
                        description: description,
                        weight: weight,
                        config: {
                            scoring_method: scoringMethod
                        }
                    })
                });

                const data = await response.json();

                if (data.status === 'success') {
                    alert('自定义分组创建成功');
                    bootstrap.Modal.getInstance(document.getElementById('customGroupModal')).hide();
                    document.getElementById('custom-group-form').reset();
                    loadGroups();
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                console.error('创建自定义分组失败:', error);
                alert('创建失败: ' + error.message);
            }
        }

        // 删除分组
        async function removeGroup(groupId) {
            if (!confirm('确定要删除这个分组吗？删除后分析结果表中对应的列也会消失。')) {
                return;
            }

            try {
                const response = await fetch(`/api/groups/${groupId}`, {
                    method: 'DELETE'
                });

                const data = await response.json();

                if (data.message) {
                    showAlert('分组删除成功！', 'success');
                    loadGroups();
                } else {
                    throw new Error(data.error || '删除失败');
                }
            } catch (error) {
                console.error('删除分组失败:', error);
                showAlert('删除失败: ' + error.message, 'error');
            }
        }

        // 保存配置
        async function saveConfig() {
            try {
                // 收集当前所有权重配置
                const currentWeights = {};

                // 收集分组权重
                Object.keys(groups).forEach(groupName => {
                    if (groups[groupName].enabled) {
                        currentWeights[groupName] = weights[groupName] || 50; // 默认权重50%
                    }
                });

                // 收集计分单元权重
                Object.keys(groups).forEach(groupName => {
                    if (groups[groupName].enabled && groups[groupName].units) {
                        groups[groupName].units.forEach(unit => {
                            const weightKey = `${groupName}_${unit.name}`;
                            currentWeights[weightKey] = unit.weight || 1.0; // 默认权重1.0
                        });
                    }
                });

                console.log('保存配置:', currentWeights);

                const response = await fetch('/api/config', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        config: {
                            weights: currentWeights
                        }
                    })
                });

                const data = await response.json();

                if (data.status === 'success') {
                    showAlert('配置保存成功！请重新分析以查看权重变化效果。', 'success');
                    // 更新全局权重变量
                    weights = currentWeights;
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                console.error('保存配置失败:', error);
                showAlert('保存失败: ' + error.message, 'error');
            }
        }

        // 查看图表
        function viewChart(stockCode, stockName) {
            const url = `/professional_chart.html?stock=${stockCode}&name=${encodeURIComponent(stockName)}`;
            window.open(url, '_blank');
        }

        // 显示分组权重配置
        function showGroupWeightConfig() {
            // 加载当前权重配置
            loadCurrentGroupWeights();
            const modal = new bootstrap.Modal(document.getElementById('groupWeightModal'));
            modal.show();
        }

        // 加载当前分组权重
        async function loadCurrentGroupWeights() {
            try {
                const response = await fetch('/api/group-weights');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        const trendWeight = Math.round((data.weights.trend_group || 0.7) * 100);
                        const oscillationWeight = Math.round((data.weights.oscillation_group || 0.3) * 100);

                        document.getElementById('trendGroupWeight').value = trendWeight;
                        document.getElementById('oscillationGroupWeight').value = oscillationWeight;
                        document.getElementById('trendWeightDisplay').textContent = trendWeight + '%';
                        document.getElementById('oscillationWeightDisplay').textContent = oscillationWeight + '%';
                    }
                }
            } catch (error) {
                console.error('加载分组权重失败:', error);
                // 使用默认值
                document.getElementById('trendGroupWeight').value = 70;
                document.getElementById('oscillationGroupWeight').value = 30;
                document.getElementById('trendWeightDisplay').textContent = '70%';
                document.getElementById('oscillationWeightDisplay').textContent = '30%';
            }
        }

        // 更新权重显示
        function updateWeights(changedGroup) {
            const trendSlider = document.getElementById('trendGroupWeight');
            const oscillationSlider = document.getElementById('oscillationGroupWeight');
            const trendDisplay = document.getElementById('trendWeightDisplay');
            const oscillationDisplay = document.getElementById('oscillationWeightDisplay');

            if (changedGroup === 'trend') {
                const trendValue = parseInt(trendSlider.value);
                const oscillationValue = 100 - trendValue;
                oscillationSlider.value = oscillationValue;
                trendDisplay.textContent = trendValue + '%';
                oscillationDisplay.textContent = oscillationValue + '%';
            } else {
                const oscillationValue = parseInt(oscillationSlider.value);
                const trendValue = 100 - oscillationValue;
                trendSlider.value = trendValue;
                trendDisplay.textContent = trendValue + '%';
                oscillationDisplay.textContent = oscillationValue + '%';
            }
        }

        // 保存分组权重
        async function saveGroupWeights() {
            try {
                const trendWeight = parseInt(document.getElementById('trendGroupWeight').value) / 100;
                const oscillationWeight = parseInt(document.getElementById('oscillationGroupWeight').value) / 100;

                const response = await fetch('/api/group-weights', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        weights: {
                            trend_group: trendWeight,
                            oscillation_group: oscillationWeight
                        }
                    })
                });

                const data = await response.json();
                if (data.success) {
                    // 更新全局权重变量
                    weights.trend_group = trendWeight;
                    weights.oscillation_group = oscillationWeight;

                    showAlert('分组权重配置已保存！请重新分析以查看权重变化效果。', 'success');

                    // 高亮显示开始分析按钮
                    highlightAnalysisButton();

                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('groupWeightModal'));
                    modal.hide();
                    // 重新加载分组配置
                    loadGroups();
                } else {
                    showAlert('保存失败: ' + (data.message || '未知错误'), 'error');
                }
            } catch (error) {
                console.error('保存分组权重失败:', error);
                alert('保存失败: ' + error.message);
            }
        }

        // 显示计分单元权重配置
        function showUnitWeightConfig() {
            loadUnitWeights();
            const modal = new bootstrap.Modal(document.getElementById('unitWeightModal'));
            modal.show();
        }

        // 显示特定分组的单元配置
        function showGroupUnitConfig(groupId) {
            // 加载权重配置
            loadUnitWeights();

            // 打开模态框
            const modal = new bootstrap.Modal(document.getElementById('unitWeightModal'));
            modal.show();

            // 滚动到对应分组
            setTimeout(() => {
                const targetContainer = groupId === 'trend_group' ?
                    document.getElementById('trend-units-config') :
                    document.getElementById('oscillation-units-config');

                if (targetContainer) {
                    targetContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    // 高亮显示目标分组
                    const card = targetContainer.closest('.card');
                    if (card) {
                        card.style.boxShadow = '0 0 20px rgba(102, 126, 234, 0.5)';
                        setTimeout(() => {
                            card.style.boxShadow = '';
                        }, 2000);
                    }
                }
            }, 300);
        }

        // 显示添加分组模态框
        function showAddGroupModal() {
            // 重置表单
            document.getElementById('addGroupForm').reset();
            document.getElementById('group-weight-slider').value = '0.5';
            updateGroupWeightDisplay();

            const modal = new bootstrap.Modal(document.getElementById('addGroupModal'));
            modal.show();
        }

        // 更新分组权重显示
        function updateGroupWeightDisplay() {
            const slider = document.getElementById('group-weight-slider');
            const display = document.getElementById('group-weight-display');
            display.textContent = slider.value;
        }

        // 确认创建分组
        async function createGroupConfirm() {
            try {
                const groupId = document.getElementById('group-id').value.trim();
                const groupName = document.getElementById('group-name').value.trim();
                const description = document.getElementById('group-description').value.trim();
                const weight = parseFloat(document.getElementById('group-weight-slider').value);

                // 验证输入
                if (!groupId || !groupName) {
                    showAlert('分组ID和名称不能为空！', 'error');
                    return;
                }

                // 验证分组ID格式
                if (!/^[a-zA-Z0-9_]+$/.test(groupId)) {
                    showAlert('分组ID只能包含字母、数字和下划线！', 'error');
                    return;
                }

                const response = await fetch('/api/groups/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        group_id: groupId,
                        name: groupName,
                        description: description,
                        weight: weight,
                        config: {
                            scoring_method: 'weighted_average',
                            score_multiplier: 1.0,
                            signal_threshold: 0.5
                        }
                    })
                });

                const data = await response.json();
                if (data.status === 'success') {
                    showAlert(`分组 "${groupName}" 创建成功！`, 'success');

                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addGroupModal'));
                    modal.hide();

                    // 重新加载分组列表
                    loadGroups();
                } else {
                    showAlert('创建分组失败: ' + (data.error || '未知错误'), 'error');
                }
            } catch (error) {
                console.error('创建分组失败:', error);
                showAlert('创建分组失败: ' + error.message, 'error');
            }
        }

        // 数据量变化处理
        function onDataLimitChange() {
            const dataLimit = document.getElementById('data-limit-filter').value;
            showAlert(`数据量已更改为 ${dataLimit} 天，需要重新分析才能生效`, 'warning');
        }

        // 筛选结果
        function filterResults() {
            const searchTerm = document.getElementById('search-input').value.toLowerCase();
            const categoryFilter = document.getElementById('category-filter').value;

            const tbody = document.getElementById('results-table');
            const rows = tbody.querySelectorAll('tr');

            rows.forEach(row => {
                const stockCode = row.cells[0].textContent.toLowerCase();
                const stockName = row.cells[1].textContent.toLowerCase();

                // 获取分类信息 - 从结果数据中获取
                let category = '';
                if (results && results.length > 0) {
                    const result = results.find(r =>
                        (r.symbol || r.stock_code || '').toLowerCase() === stockCode ||
                        (r.name || r.stock_name || '').toLowerCase() === stockName.toLowerCase()
                    );
                    if (result) {
                        category = result.category || '';
                    }
                }

                let showRow = true;

                // 搜索筛选
                if (searchTerm && !stockCode.includes(searchTerm) && !stockName.includes(searchTerm)) {
                    showRow = false;
                }

                // 市场分类筛选
                if (categoryFilter && category !== categoryFilter) {
                    showRow = false;
                }

                row.style.display = showRow ? '' : 'none';
            });

            // 更新显示的结果数量
            const visibleRows = Array.from(rows).filter(row => row.style.display !== 'none');
            updateResultsCount(visibleRows.length, rows.length);
        }

        // 排序结果
        function sortResults() {
            const sortBy = document.getElementById('sort-filter').value;
            const tbody = document.getElementById('results-table');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            rows.sort((a, b) => {
                let aValue, bValue;

                switch (sortBy) {
                    case 'score-desc':
                        aValue = parseFloat(a.cells[2].textContent);
                        bValue = parseFloat(b.cells[2].textContent);
                        return bValue - aValue;
                    case 'score-asc':
                        aValue = parseFloat(a.cells[2].textContent);
                        bValue = parseFloat(b.cells[2].textContent);
                        return aValue - bValue;
                    case 'trend-desc':
                        aValue = parseFloat(a.cells[3].textContent);
                        bValue = parseFloat(b.cells[3].textContent);
                        return bValue - aValue;
                    case 'oscillation-desc':
                        aValue = parseFloat(a.cells[4].textContent);
                        bValue = parseFloat(b.cells[4].textContent);
                        return bValue - aValue;
                    case 'symbol-asc':
                        aValue = a.cells[0].textContent;
                        bValue = b.cells[0].textContent;
                        return aValue.localeCompare(bValue);
                    case 'name-asc':
                        aValue = a.cells[1].textContent;
                        bValue = b.cells[1].textContent;
                        return aValue.localeCompare(bValue);
                    default:
                        return 0;
                }
            });

            // 重新排列行
            rows.forEach(row => tbody.appendChild(row));
        }

        // 更新结果数量显示
        function updateResultsCount(visible, total) {
            const countElement = document.getElementById('results-count');
            if (countElement) {
                countElement.textContent = visible === total ?
                    `共 ${total} 条结果` :
                    `显示 ${visible} / ${total} 条结果`;
            }
        }

        // 加载计分单元权重配置
        async function loadUnitWeights() {
            try {
                const response = await fetch('/api/unit-weights');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        renderUnitWeights(data.unit_weights);
                    }
                }
            } catch (error) {
                console.error('加载计分单元权重失败:', error);
            }
        }

        // 渲染计分单元权重配置界面
        function renderUnitWeights(unitWeights) {
            // 渲染趋势分组
            const trendContainer = document.getElementById('trend-units-config');
            trendContainer.innerHTML = '';

            if (unitWeights.trend_group) {
                // 归一化权重为百分比
                const trendWeights = normalizeWeightsToPercentage(unitWeights.trend_group);
                Object.entries(trendWeights).forEach(([unitId, percentage]) => {
                    const unitDiv = createUnitWeightControlWithPercentage(unitId, percentage, 'trend_group');
                    trendContainer.appendChild(unitDiv);
                });
                updateTotalWeightDisplay('trend_group');
            }

            // 渲染震荡分组
            const oscillationContainer = document.getElementById('oscillation-units-config');
            oscillationContainer.innerHTML = '';

            if (unitWeights.oscillation_group) {
                // 归一化权重为百分比
                const oscillationWeights = normalizeWeightsToPercentage(unitWeights.oscillation_group);
                Object.entries(oscillationWeights).forEach(([unitId, percentage]) => {
                    const unitDiv = createUnitWeightControlWithPercentage(unitId, percentage, 'oscillation_group');
                    oscillationContainer.appendChild(unitDiv);
                });
                updateTotalWeightDisplay('oscillation_group');
            }
        }

        // 将权重归一化为百分比
        function normalizeWeightsToPercentage(weights) {
            const totalWeight = Object.values(weights).reduce((sum, weight) => sum + weight, 0);
            const normalizedWeights = {};

            if (totalWeight > 0) {
                Object.entries(weights).forEach(([unitId, weight]) => {
                    normalizedWeights[unitId] = Math.round((weight / totalWeight) * 100);
                });

                // 确保总和为100%
                const currentTotal = Object.values(normalizedWeights).reduce((sum, p) => sum + p, 0);
                if (currentTotal !== 100) {
                    const firstUnit = Object.keys(normalizedWeights)[0];
                    normalizedWeights[firstUnit] += (100 - currentTotal);
                }
            } else {
                // 如果总权重为0，平均分配
                const unitCount = Object.keys(weights).length;
                const averagePercentage = Math.floor(100 / unitCount);
                let remainder = 100 % unitCount;

                Object.keys(weights).forEach(unitId => {
                    normalizedWeights[unitId] = averagePercentage + (remainder > 0 ? 1 : 0);
                    if (remainder > 0) remainder--;
                });
            }

            return normalizedWeights;
        }

        // 创建计分单元权重控制组件（使用百分比）
        function createUnitWeightControlWithPercentage(unitId, percentage, groupId) {
            const div = document.createElement('div');
            div.className = 'mb-3 p-3 border rounded bg-light';

            div.innerHTML = `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <strong class="text-primary">${getUnitDisplayName(unitId)}</strong>
                        <br><small class="text-muted">${getUnitDescription(unitId)}</small>
                    </div>
                    <button class="btn btn-outline-danger btn-sm" onclick="removeUnitFromGroup('${unitId}', '${groupId}')" title="移除此计分单元">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="d-flex align-items-center">
                    <input type="range" class="form-range me-3" min="0" max="100" step="5" value="${percentage}"
                           onchange="updateUnitWeightPercentage('${unitId}', '${groupId}', this.value)"
                           data-unit-id="${unitId}" data-group-id="${groupId}">
                    <span class="badge bg-primary fs-6" id="weight-${unitId}-${groupId}">${percentage}%</span>
                </div>
            `;
            return div;
        }

        // 创建计分单元权重控制组件（兼容旧版本）
        function createUnitWeightControl(unitId, weight, groupId) {
            // 将权重转换为百分比
            const percentage = Math.round(weight * 100);
            return createUnitWeightControlWithPercentage(unitId, percentage, groupId);
        }

        // 获取计分单元显示名称
        function getUnitDisplayName(unitId) {
            const names = {
                'rsi_unit': 'RSI计分单元',
                'macd_unit': 'MACD计分单元',
                'trend_unit': '趋势计分单元',
                'wave_unit': '波段计分单元',
                'mcsi_macd_unit': 'MCSI MACD计分单元',
                'mcsi_mmt_unit': 'MCSI MMT计分单元',
                'mcsi_rsi_unit': 'MCSI RSI计分单元',
                'mcsi_td9_unit': 'MCSI TD9计分单元'
            };
            return names[unitId] || unitId;
        }

        // 获取计分单元描述
        function getUnitDescription(unitId) {
            const descriptions = {
                'rsi_unit': '相对强弱指标',
                'macd_unit': '移动平均收敛发散',
                'trend_unit': '趋势分析指标',
                'wave_unit': '波段分析指标',
                'mcsi_macd_unit': '基于动态阈值的增强版MACD信号检测',
                'mcsi_mmt_unit': '多重复合动量分析，结合轨道分析和背离检测',
                'mcsi_rsi_unit': '增强版RSI，支持日线和周线双时间框架分析',
                'mcsi_td9_unit': 'TD序列计数指标，识别价格序列模式'
            };
            return descriptions[unitId] || '';
        }

        // 更新权重显示（添加模态框中的滑块）
        function updateWeightDisplay() {
            const slider = document.getElementById('unit-weight-slider');
            const display = document.getElementById('weight-display');
            display.textContent = slider.value + '%';
        }

        // 更新计分单元权重百分比
        function updateUnitWeightPercentage(unitId, groupId, percentage) {
            // 更新显示
            document.getElementById(`weight-${unitId}-${groupId}`).textContent = percentage + '%';

            // 只更新总权重显示，不自动调整其他单元
            updateTotalWeightDisplay(groupId);
        }



        // 更新总权重显示
        function updateTotalWeightDisplay(groupId) {
            const container = document.getElementById(`${groupId.replace('_group', '')}-units-config`);
            const sliders = container.querySelectorAll('input[type="range"]');

            let total = 0;
            sliders.forEach(slider => {
                total += parseInt(slider.value);
            });

            const displayId = groupId.replace('_group', '') + '-total-weight';
            const display = document.getElementById(displayId);
            if (display) {
                if (total === 100) {
                    display.textContent = total + '%';
                    display.className = 'badge bg-success text-white';
                } else {
                    display.textContent = total + '% ⚠️';
                    display.className = 'badge bg-warning text-dark';
                    display.title = `权重总和必须为100%，当前为${total}%`;
                }
            }
        }

        // 显示添加计分单元模态框
        function showAddUnitModal() {
            const modal = new bootstrap.Modal(document.getElementById('addUnitModal'));
            modal.show();
        }

        // 添加计分单元到分组
        function addUnitToGroup(groupId) {
            document.getElementById('target-group').value = groupId;
            showAddUnitModal();
        }

        // 确认添加计分单元到分组
        async function addUnitToGroupConfirm() {
            try {
                const groupId = document.getElementById('target-group').value;
                const unitId = document.getElementById('available-units').value;
                const percentage = parseInt(document.getElementById('unit-weight-slider').value);
                const weight = percentage / 100; // 转换为小数

                const response = await fetch('/api/unit-weights', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'add',
                        group_id: groupId,
                        unit_id: unitId,
                        weight: weight
                    })
                });

                const data = await response.json();
                if (data.success) {
                    showAlert('计分单元添加成功！', 'success');
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addUnitModal'));
                    modal.hide();
                    // 重新加载权重配置
                    loadUnitWeights();
                    // 重新加载分组配置
                    loadGroups();
                } else {
                    showAlert('添加失败: ' + (data.message || '未知错误'), 'danger');
                }
            } catch (error) {
                console.error('添加计分单元失败:', error);
                showAlert('添加失败: ' + error.message, 'danger');
            }
        }

        // 从分组中移除计分单元
        async function removeUnitFromGroup(unitId, groupId) {
            if (!confirm('确定要移除这个计分单元吗？')) {
                return;
            }

            try {
                const response = await fetch('/api/unit-weights', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'remove',
                        group_id: groupId,
                        unit_id: unitId
                    })
                });

                const data = await response.json();
                if (data.success) {
                    alert('计分单元移除成功');
                    // 重新加载权重配置
                    loadUnitWeights();
                } else {
                    alert('移除失败: ' + (data.message || '未知错误'));
                }
            } catch (error) {
                console.error('移除计分单元失败:', error);
                alert('移除失败: ' + error.message);
            }
        }

        // 保存计分单元权重配置
        async function saveUnitWeights() {
            try {
                // 收集所有权重配置
                const unitWeights = {
                    trend_group: {},
                    oscillation_group: {}
                };

                // 收集趋势分组权重
                let trendTotal = 0;
                document.querySelectorAll('#trend-units-config input[type="range"]').forEach(input => {
                    const unitId = input.getAttribute('data-unit-id');
                    const percentage = parseInt(input.value);
                    unitWeights.trend_group[unitId] = percentage / 100; // 转换为小数
                    trendTotal += percentage;
                });

                // 收集震荡分组权重
                let oscillationTotal = 0;
                document.querySelectorAll('#oscillation-units-config input[type="range"]').forEach(input => {
                    const unitId = input.getAttribute('data-unit-id');
                    const percentage = parseInt(input.value);
                    unitWeights.oscillation_group[unitId] = percentage / 100; // 转换为小数
                    oscillationTotal += percentage;
                });

                // 验证权重总和
                const trendUnitsCount = Object.keys(unitWeights.trend_group).length;
                const oscillationUnitsCount = Object.keys(unitWeights.oscillation_group).length;

                if (trendUnitsCount > 0 && trendTotal !== 100) {
                    showAlert(`趋势分组权重总和为 ${trendTotal}%，必须等于 100%！`, 'error');
                    return;
                }

                if (oscillationUnitsCount > 0 && oscillationTotal !== 100) {
                    showAlert(`震荡分组权重总和为 ${oscillationTotal}%，必须等于 100%！`, 'error');
                    return;
                }

                const response = await fetch('/api/unit-weights', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'update_all',
                        unit_weights: unitWeights
                    })
                });

                const data = await response.json();
                if (data.success) {
                    // 更新全局权重变量
                    Object.keys(unitWeights.trend_group).forEach(unitId => {
                        weights[`trend_group_${unitId}`] = unitWeights.trend_group[unitId];
                    });
                    Object.keys(unitWeights.oscillation_group).forEach(unitId => {
                        weights[`oscillation_group_${unitId}`] = unitWeights.oscillation_group[unitId];
                    });

                    showAlert('计分单元权重配置已保存！请重新分析以查看权重变化效果。', 'success');

                    // 高亮显示开始分析按钮
                    highlightAnalysisButton();

                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('unitWeightModal'));
                    modal.hide();
                    // 重新加载分组配置
                    loadGroups();
                } else {
                    showAlert('保存失败: ' + (data.message || '未知错误'), 'error');
                }
            } catch (error) {
                console.error('保存计分单元权重失败:', error);
                showAlert('保存失败: ' + error.message, 'danger');
            }
        }

        // 高亮显示开始分析按钮
        function highlightAnalysisButton() {
            const button = document.getElementById('start-analysis');
            if (button) {
                // 添加闪烁效果
                button.classList.add('btn-warning');
                button.classList.remove('btn-success');
                button.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>重新分析';

                // 添加脉冲动画
                button.style.animation = 'pulse 1s infinite';

                // 5秒后恢复正常
                setTimeout(() => {
                    button.classList.remove('btn-warning');
                    button.classList.add('btn-success');
                    button.innerHTML = '<i class="fas fa-play me-2"></i>开始分析';
                    button.style.animation = '';
                }, 5000);
            }
        }

        // 显示提示信息
        function showAlert(message, type = 'info') {
            // 创建提示框
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
