<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单调试页面</title>
    <script src="https://cdn.jsdelivr.net/npm/lightweight-charts@4.1.1/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #131722;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .debug-info {
            background-color: #1e222d;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        
        .chart-container {
            width: 100%;
            height: 400px;
            background-color: #1e222d;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        
        .log {
            background-color: #000;
            color: #0f0;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            border-radius: 5px;
        }
        
        button {
            background-color: #2962ff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        button:hover {
            background-color: #1e53e5;
        }
    </style>
</head>
<body>
    <h1>🔧 简单调试页面</h1>
    
    <div class="debug-info">
        <h3>调试信息</h3>
        <p>股票代码: <span id="stockCode">-</span></p>
        <p>LightweightCharts: <span id="chartsStatus">检查中...</span></p>
        <p>API状态: <span id="apiStatus">未测试</span></p>
    </div>
    
    <div>
        <button onclick="testAPI()">测试API</button>
        <button onclick="testChart()">测试图表</button>
        <button onclick="clearLog()">清除日志</button>
    </div>
    
    <div class="chart-container" id="testChart"></div>
    
    <div class="log" id="logContainer"></div>

    <script>
        let chart = null;
        let logContainer = document.getElementById('logContainer');
        
        // 日志函数
        function log(message) {
            console.log('[Debug]', message);
            const timestamp = new Date().toLocaleTimeString();
            logContainer.innerHTML += `[${timestamp}] ${message}\n`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // 清除日志
        function clearLog() {
            logContainer.innerHTML = '';
        }
        
        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }
        
        // 检查LightweightCharts
        function checkLightweightCharts() {
            if (typeof LightweightCharts === 'undefined') {
                document.getElementById('chartsStatus').textContent = '❌ 未加载';
                document.getElementById('chartsStatus').style.color = 'red';
                log('❌ LightweightCharts库未加载');
                return false;
            } else {
                document.getElementById('chartsStatus').textContent = '✅ 已加载';
                document.getElementById('chartsStatus').style.color = 'green';
                log('✅ LightweightCharts库已加载');
                return true;
            }
        }
        
        // 测试API
        async function testAPI() {
            const stockCode = getUrlParameter('stock') || '08340';
            const limit = getUrlParameter('limit') || '50';
            
            log(`🧪 测试API调用: ${stockCode}`);
            document.getElementById('apiStatus').textContent = '测试中...';
            
            try {
                const apiUrl = `/api/stock_chart/${stockCode}?limit=${limit}`;
                log(`📡 API URL: ${apiUrl}`);
                
                const response = await fetch(apiUrl);
                log(`📡 响应状态: ${response.status}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log(`📊 数据解析成功`);
                
                if (data.success) {
                    log(`✅ API调用成功`);
                    log(`   数据点数: ${data.data_points}`);
                    log(`   日期数量: ${data.dates ? data.dates.length : 0}`);
                    log(`   价格数据: ${data.prices && data.prices.close ? data.prices.close.length : 0}`);
                    
                    document.getElementById('apiStatus').textContent = '✅ 成功';
                    document.getElementById('apiStatus').style.color = 'green';
                    
                    // 保存数据供图表测试使用
                    window.testData = data;
                    
                } else {
                    throw new Error(data.error || data.message || '未知错误');
                }
                
            } catch (error) {
                log(`❌ API调用失败: ${error.message}`);
                document.getElementById('apiStatus').textContent = '❌ 失败';
                document.getElementById('apiStatus').style.color = 'red';
                console.error('API调用详细错误:', error);
            }
        }
        
        // 测试图表
        function testChart() {
            log('🎨 测试图表渲染');
            
            if (!checkLightweightCharts()) {
                return;
            }
            
            if (!window.testData) {
                log('❌ 没有测试数据，请先测试API');
                return;
            }
            
            try {
                // 清除现有图表
                if (chart) {
                    chart.remove();
                    chart = null;
                }
                
                // 创建图表
                const chartContainer = document.getElementById('testChart');
                chart = LightweightCharts.createChart(chartContainer, {
                    width: chartContainer.clientWidth,
                    height: chartContainer.clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                    },
                });
                
                log('✅ 图表容器创建成功');
                
                // 添加K线数据
                const data = window.testData;
                if (data.dates && data.prices) {
                    const candlestickData = data.dates.map((date, i) => ({
                        time: date,
                        open: data.prices.open[i],
                        high: data.prices.high[i],
                        low: data.prices.low[i],
                        close: data.prices.close[i]
                    }));
                    
                    const candlestickSeries = chart.addCandlestickSeries({
                        upColor: '#26a69a',
                        downColor: '#ef5350',
                        borderVisible: false,
                        wickUpColor: '#26a69a',
                        wickDownColor: '#ef5350',
                    });
                    
                    candlestickSeries.setData(candlestickData);
                    log(`✅ K线数据添加成功: ${candlestickData.length} 个数据点`);
                    
                    // 自适应显示
                    chart.timeScale().fitContent();
                    log('✅ 图表自适应完成');
                    
                } else {
                    log('❌ 价格数据不完整');
                }
                
            } catch (error) {
                log(`❌ 图表渲染失败: ${error.message}`);
                console.error('图表渲染详细错误:', error);
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 页面加载完成');
            
            // 获取URL参数
            const stockCode = getUrlParameter('stock') || '08340';
            document.getElementById('stockCode').textContent = stockCode;
            
            log(`📊 股票代码: ${stockCode}`);
            
            // 检查LightweightCharts
            checkLightweightCharts();
            
            // 自动测试API
            setTimeout(() => {
                testAPI();
            }, 1000);
        });
    </script>
</body>
</html>
