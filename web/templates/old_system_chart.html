<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业股票技术分析图表</title>
    <script src="https://cdn.jsdelivr.net/npm/lightweight-charts@4.1.1/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #131722;
            color: #d1d4dc;
            overflow: hidden;
        }

        .header {
            background: #1e222d;
            padding: 12px 20px;
            border-bottom: 1px solid #2a2e39;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .stock-info h1 {
            margin: 0;
            font-size: 20px;
            color: #ffffff;
            font-weight: 600;
        }

        .stock-price {
            font-size: 18px;
            font-weight: bold;
            color: #26a69a;
        }
        
        .chart-container {
            height: calc(100vh - 80px);
            background: #131722;
            position: relative;
        }
        
        .main-chart {
            height: 60%;
            border-bottom: 1px solid #2a2e39;
        }
        
        .sub-charts {
            height: 40%;
            display: flex;
            flex-direction: column;
        }
        
        .sub-chart {
            flex: 1;
            border-bottom: 1px solid #2a2e39;
            position: relative;
        }
        
        .chart-title {
            position: absolute;
            top: 10px;
            left: 15px;
            font-size: 14px;
            font-weight: bold;
            color: #787b86;
            z-index: 10;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #787b86;
            font-size: 16px;
        }
        
        .error {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ef5350;
            text-align: center;
        }
        
        .btn {
            background: #2962ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .btn:hover {
            background: #1e4ba8;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="stock-info">
            <h1 id="stockTitle">加载中...</h1>
        </div>
        <div>
            <span class="stock-price" id="stockPrice">--</span>
            <button class="btn" onclick="location.reload()">刷新</button>
        </div>
    </div>
    
    <div class="chart-container">
        <div class="main-chart" id="mainChart">
            <div class="chart-title">价格 & 移动平均线</div>
            <div class="loading" id="mainLoading">正在加载数据...</div>
        </div>
        
        <div class="sub-charts">
            <div class="sub-chart" id="macdChart">
                <div class="chart-title">MACD</div>
            </div>
            
            <div class="sub-chart" id="rsiChart">
                <div class="chart-title">RSI</div>
            </div>
        </div>
    </div>
    
    <script>
        let mainChart = null;
        let macdChart = null;
        let rsiChart = null;
        
        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }
        
        // 日志函数
        function log(message) {
            console.log('[Chart]', message);
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            const stockCode = getUrlParameter('stock') || '300584';
            const stockName = getUrlParameter('name') || '海辰药业';
            
            log('页面加载完成');
            log('股票代码: ' + stockCode);
            log('股票名称: ' + stockName);
            
            // 检查LightweightCharts
            if (typeof LightweightCharts === 'undefined') {
                showError('LightweightCharts库未加载');
                return;
            }
            
            log('LightweightCharts库正常');
            
            // 更新标题
            document.getElementById('stockTitle').textContent = `${stockName} (${stockCode})`;
            
            // 初始化图表
            initializeCharts();
            
            // 加载数据
            loadStockData(stockCode);
        });
        
        // 初始化图表
        function initializeCharts() {
            log('初始化图表...');
            
            try {
                // 主图表
                const mainContainer = document.getElementById('mainChart');
                mainChart = LightweightCharts.createChart(mainContainer, {
                    width: mainContainer.clientWidth,
                    height: mainContainer.clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    crosshair: {
                        mode: LightweightCharts.CrosshairMode.Normal,
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        timeVisible: true,
                        secondsVisible: false,
                    },
                });
                
                // MACD图表
                const macdContainer = document.getElementById('macdChart');
                macdChart = LightweightCharts.createChart(macdContainer, {
                    width: macdContainer.clientWidth,
                    height: macdContainer.clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        visible: true,
                    },
                });
                
                // RSI图表
                const rsiContainer = document.getElementById('rsiChart');
                rsiChart = LightweightCharts.createChart(rsiContainer, {
                    width: rsiContainer.clientWidth,
                    height: rsiContainer.clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        visible: true,
                    },
                });
                
                // 同步时间轴 - 双向同步
                let isUpdating = false; // 防止循环更新

                // 主图表变化时同步其他图表
                mainChart.timeScale().subscribeVisibleTimeRangeChange(() => {
                    if (isUpdating) return;
                    isUpdating = true;

                    const timeRange = mainChart.timeScale().getVisibleRange();
                    if (timeRange) {
                        macdChart.timeScale().setVisibleRange(timeRange);
                        rsiChart.timeScale().setVisibleRange(timeRange);
                    }

                    setTimeout(() => { isUpdating = false; }, 10);
                });

                // MACD图表变化时同步其他图表
                macdChart.timeScale().subscribeVisibleTimeRangeChange(() => {
                    if (isUpdating) return;
                    isUpdating = true;

                    const timeRange = macdChart.timeScale().getVisibleRange();
                    if (timeRange) {
                        mainChart.timeScale().setVisibleRange(timeRange);
                        rsiChart.timeScale().setVisibleRange(timeRange);
                    }

                    setTimeout(() => { isUpdating = false; }, 10);
                });

                // RSI图表变化时同步其他图表
                rsiChart.timeScale().subscribeVisibleTimeRangeChange(() => {
                    if (isUpdating) return;
                    isUpdating = true;

                    const timeRange = rsiChart.timeScale().getVisibleRange();
                    if (timeRange) {
                        mainChart.timeScale().setVisibleRange(timeRange);
                        macdChart.timeScale().setVisibleRange(timeRange);
                    }

                    setTimeout(() => { isUpdating = false; }, 10);
                });
                
                log('图表初始化完成');
                
            } catch (error) {
                log('图表初始化失败: ' + error.message);
                showError('图表初始化失败: ' + error.message);
            }
        }
        
        // 加载股票数据
        async function loadStockData(stockCode) {
            log('开始加载股票数据: ' + stockCode);
            
            try {
                const response = await fetch(`/api/stock_chart/${stockCode}`);
                const data = await response.json();
                
                if (data.success) {
                    log('数据加载成功');
                    log('数据点数: ' + data.dates.length);
                    
                    renderCharts(data);
                    updatePrice(data);
                    
                    document.getElementById('mainLoading').style.display = 'none';
                } else {
                    throw new Error(data.message || '数据加载失败');
                }
            } catch (error) {
                log('数据加载失败: ' + error.message);
                showError('数据加载失败: ' + error.message);
            }
        }
        
        // 渲染图表
        function renderCharts(data) {
            log('开始渲染图表');
            
            try {
                // 准备K线数据
                const candlestickData = data.dates.map((date, index) => ({
                    time: date,
                    open: parseFloat(data.prices.open[index]),
                    high: parseFloat(data.prices.high[index]),
                    low: parseFloat(data.prices.low[index]),
                    close: parseFloat(data.prices.close[index])
                }));
                
                log('K线数据准备完成: ' + candlestickData.length + ' 个数据点');
                
                // 创建K线图
                const candlestickSeries = mainChart.addCandlestickSeries({
                    upColor: '#26a69a',
                    downColor: '#ef5350',
                    borderVisible: false,
                    wickUpColor: '#26a69a',
                    wickDownColor: '#ef5350',
                });
                candlestickSeries.setData(candlestickData);
                log('K线图创建完成');
                
                // 创建MA5线
                const ma5Data = data.dates.map((date, index) => ({
                    time: date,
                    value: parseFloat(data.ma.ma5[index])
                })).filter(item => item.value > 0);
                
                if (ma5Data.length > 0) {
                    const ma5Series = mainChart.addLineSeries({
                        color: '#2962ff',
                        lineWidth: 2,
                    });
                    ma5Series.setData(ma5Data);
                    log('MA5线创建完成: ' + ma5Data.length + ' 个数据点');
                }
                
                // 创建MA20线
                const ma20Data = data.dates.map((date, index) => ({
                    time: date,
                    value: parseFloat(data.ma.ma20[index])
                })).filter(item => item.value > 0);
                
                if (ma20Data.length > 0) {
                    const ma20Series = mainChart.addLineSeries({
                        color: '#ff6d00',
                        lineWidth: 2,
                    });
                    ma20Series.setData(ma20Data);
                    log('MA20线创建完成: ' + ma20Data.length + ' 个数据点');
                }
                
                // 创建MACD图表
                const macdData = data.dates.map((date, index) => ({
                    time: date,
                    value: parseFloat(data.macd.macd[index]) || 0
                }));
                
                const macdSeries = macdChart.addLineSeries({
                    color: '#2962ff',
                    lineWidth: 2,
                });
                macdSeries.setData(macdData);
                
                const signalData = data.dates.map((date, index) => ({
                    time: date,
                    value: parseFloat(data.macd.signal[index]) || 0
                }));
                
                const signalSeries = macdChart.addLineSeries({
                    color: '#ff6d00',
                    lineWidth: 2,
                });
                signalSeries.setData(signalData);
                
                log('MACD图表创建完成');
                
                // 创建RSI图表
                const rsiData = data.dates.map((date, index) => ({
                    time: date,
                    value: parseFloat(data.rsi[index]) || 50
                }));
                
                const rsiSeries = rsiChart.addLineSeries({
                    color: '#9c27b0',
                    lineWidth: 2,
                });
                rsiSeries.setData(rsiData);
                
                // RSI超买超卖线
                const overboughtLine = rsiChart.addLineSeries({
                    color: '#ef5350',
                    lineWidth: 1,
                    lineStyle: LightweightCharts.LineStyle.Dashed,
                });
                overboughtLine.setData(data.dates.map(date => ({ time: date, value: 70 })));
                
                const oversoldLine = rsiChart.addLineSeries({
                    color: '#26a69a',
                    lineWidth: 1,
                    lineStyle: LightweightCharts.LineStyle.Dashed,
                });
                oversoldLine.setData(data.dates.map(date => ({ time: date, value: 30 })));
                
                log('RSI图表创建完成');
                log('所有图表渲染完成');
                
            } catch (error) {
                log('图表渲染失败: ' + error.message);
                showError('图表渲染失败: ' + error.message);
            }
        }
        
        // 更新价格显示
        function updatePrice(data) {
            const currentPrice = parseFloat(data.prices.close[data.prices.close.length - 1]);
            const prevPrice = parseFloat(data.prices.close[data.prices.close.length - 2]);
            
            const priceElement = document.getElementById('stockPrice');
            priceElement.textContent = currentPrice.toFixed(2);
            priceElement.style.color = currentPrice >= prevPrice ? '#26a69a' : '#ef5350';
        }
        
        // 显示错误
        function showError(message) {
            document.getElementById('mainLoading').innerHTML = `
                <div class="error">
                    ⚠️ ${message}<br>
                    <button class="btn" onclick="location.reload()">重新加载</button>
                </div>
            `;
        }
        
        // 窗口大小调整
        window.addEventListener('resize', () => {
            if (mainChart) {
                const mainContainer = document.getElementById('mainChart');
                mainChart.applyOptions({
                    width: mainContainer.clientWidth,
                    height: mainContainer.clientHeight,
                });
            }
            if (macdChart) {
                const macdContainer = document.getElementById('macdChart');
                macdChart.applyOptions({
                    width: macdContainer.clientWidth,
                    height: macdContainer.clientHeight,
                });
            }
            if (rsiChart) {
                const rsiContainer = document.getElementById('rsiChart');
                rsiChart.applyOptions({
                    width: rsiContainer.clientWidth,
                    height: rsiContainer.clientHeight,
                });
            }
        });
    </script>
</body>
</html>
