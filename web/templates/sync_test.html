<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间轴同步测试</title>
    <script src="https://cdn.jsdelivr.net/npm/lightweight-charts@4.1.1/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #131722;
            color: #d1d4dc;
            padding: 20px;
        }

        .chart-container {
            height: calc(100vh - 100px);
            background: #131722;
            position: relative;
        }

        .main-chart {
            height: 60%;
            border-bottom: 1px solid #2a2e39;
        }

        .sub-charts {
            height: 40%;
            display: flex;
            flex-direction: column;
        }

        .sub-chart {
            flex: 1;
            border-bottom: 1px solid #2a2e39;
            position: relative;
        }

        .chart-title {
            position: absolute;
            top: 10px;
            left: 15px;
            font-size: 14px;
            font-weight: bold;
            color: #787b86;
            z-index: 10;
        }

        .status {
            background: #1e222d;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 5px;
        }

        button {
            background: #2962ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }

        button:hover {
            background: #1e4db7;
        }
    </style>
</head>
<body>
    <h1>时间轴同步测试</h1>
    
    <div class="status">
        <div>状态: <span id="status">等待初始化</span></div>
        <div>同步状态: <span id="syncStatus">未启用</span></div>
        <button onclick="testSync()">测试同步</button>
        <button onclick="resetCharts()">重置图表</button>
    </div>
    
    <div class="chart-container">
        <div class="main-chart" id="mainChart">
            <div class="chart-title">主图表</div>
        </div>
        
        <div class="sub-charts">
            <div class="sub-chart" id="macdChart">
                <div class="chart-title">MACD图表</div>
            </div>
            
            <div class="sub-chart" id="rsiChart">
                <div class="chart-title">RSI图表</div>
            </div>
        </div>
    </div>

    <script>
        let mainChart = null;
        let macdChart = null;
        let rsiChart = null;
        let isUpdating = false;

        function updateStatus(message) {
            document.getElementById('status').textContent = message;
            console.log('[Sync Test]', message);
        }

        function updateSyncStatus(message) {
            document.getElementById('syncStatus').textContent = message;
        }

        // 生成测试数据
        function generateTestData() {
            const data = [];
            const startDate = new Date('2024-01-01');
            
            for (let i = 0; i < 100; i++) {
                const date = new Date(startDate);
                date.setDate(date.getDate() + i);
                
                const price = 100 + Math.sin(i * 0.1) * 10 + Math.random() * 5;
                
                data.push({
                    time: date.toISOString().split('T')[0],
                    open: price,
                    high: price + Math.random() * 2,
                    low: price - Math.random() * 2,
                    close: price + (Math.random() - 0.5) * 2,
                    value: 50 + Math.sin(i * 0.2) * 20 // 用于指标图表
                });
            }
            
            return data;
        }

        function initializeCharts() {
            updateStatus('初始化图表...');
            
            try {
                // 主图表
                const mainContainer = document.getElementById('mainChart');
                mainChart = LightweightCharts.createChart(mainContainer, {
                    width: mainContainer.clientWidth,
                    height: mainContainer.clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        timeVisible: true,
                    },
                });

                // MACD图表
                const macdContainer = document.getElementById('macdChart');
                macdChart = LightweightCharts.createChart(macdContainer, {
                    width: macdContainer.clientWidth,
                    height: macdContainer.clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        visible: true,
                    },
                });

                // RSI图表
                const rsiContainer = document.getElementById('rsiChart');
                rsiChart = LightweightCharts.createChart(rsiContainer, {
                    width: rsiContainer.clientWidth,
                    height: rsiContainer.clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        visible: true,
                    },
                });

                // 设置时间轴同步（完全按照旧系统实现）
                setupTimeSync();
                
                updateStatus('图表初始化完成');
                
            } catch (error) {
                updateStatus('图表初始化失败: ' + error.message);
            }
        }

        function setupTimeSync() {
            updateSyncStatus('设置中...');
            
            try {
                // 主图表变化时同步其他图表
                mainChart.timeScale().subscribeVisibleTimeRangeChange(() => {
                    if (isUpdating) return;
                    isUpdating = true;

                    const timeRange = mainChart.timeScale().getVisibleRange();
                    if (timeRange) {
                        macdChart.timeScale().setVisibleRange(timeRange);
                        rsiChart.timeScale().setVisibleRange(timeRange);
                    }

                    setTimeout(() => { isUpdating = false; }, 10);
                });

                // MACD图表变化时同步其他图表
                macdChart.timeScale().subscribeVisibleTimeRangeChange(() => {
                    if (isUpdating) return;
                    isUpdating = true;

                    const timeRange = macdChart.timeScale().getVisibleRange();
                    if (timeRange) {
                        mainChart.timeScale().setVisibleRange(timeRange);
                        rsiChart.timeScale().setVisibleRange(timeRange);
                    }

                    setTimeout(() => { isUpdating = false; }, 10);
                });

                // RSI图表变化时同步其他图表
                rsiChart.timeScale().subscribeVisibleTimeRangeChange(() => {
                    if (isUpdating) return;
                    isUpdating = true;

                    const timeRange = rsiChart.timeScale().getVisibleRange();
                    if (timeRange) {
                        mainChart.timeScale().setVisibleRange(timeRange);
                        macdChart.timeScale().setVisibleRange(timeRange);
                    }

                    setTimeout(() => { isUpdating = false; }, 10);
                });
                
                updateSyncStatus('已启用');
                
            } catch (error) {
                updateSyncStatus('设置失败: ' + error.message);
            }
        }

        function testSync() {
            updateStatus('加载测试数据...');
            
            try {
                const testData = generateTestData();
                
                // 添加K线数据
                const candlestickSeries = mainChart.addCandlestickSeries({
                    upColor: '#26a69a',
                    downColor: '#ef5350',
                    borderVisible: false,
                    wickUpColor: '#26a69a',
                    wickDownColor: '#ef5350',
                });
                candlestickSeries.setData(testData);

                // 添加MACD线
                const macdSeries = macdChart.addLineSeries({
                    color: '#2962ff',
                    lineWidth: 2,
                });
                macdSeries.setData(testData.map(d => ({ time: d.time, value: d.value })));

                // 添加RSI线
                const rsiSeries = rsiChart.addLineSeries({
                    color: '#9c27b0',
                    lineWidth: 2,
                });
                rsiSeries.setData(testData.map(d => ({ time: d.time, value: d.value + 10 })));

                updateStatus('测试数据加载完成，可以测试同步功能');
                
            } catch (error) {
                updateStatus('测试失败: ' + error.message);
            }
        }

        function resetCharts() {
            if (mainChart) mainChart.remove();
            if (macdChart) macdChart.remove();
            if (rsiChart) rsiChart.remove();
            
            document.getElementById('mainChart').innerHTML = '<div class="chart-title">主图表</div>';
            document.getElementById('macdChart').innerHTML = '<div class="chart-title">MACD图表</div>';
            document.getElementById('rsiChart').innerHTML = '<div class="chart-title">RSI图表</div>';
            
            updateStatus('图表已重置');
            updateSyncStatus('未启用');
            
            initializeCharts();
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof LightweightCharts === 'undefined') {
                updateStatus('LightweightCharts库未加载');
                return;
            }
            
            initializeCharts();
        });

        // 窗口大小调整
        window.addEventListener('resize', () => {
            if (mainChart) {
                const container = document.getElementById('mainChart');
                mainChart.applyOptions({
                    width: container.clientWidth,
                    height: container.clientHeight,
                });
            }
            if (macdChart) {
                const container = document.getElementById('macdChart');
                macdChart.applyOptions({
                    width: container.clientWidth,
                    height: container.clientHeight,
                });
            }
            if (rsiChart) {
                const container = document.getElementById('rsiChart');
                rsiChart.applyOptions({
                    width: container.clientWidth,
                    height: container.clientHeight,
                });
            }
        });
    </script>
</body>
</html>
