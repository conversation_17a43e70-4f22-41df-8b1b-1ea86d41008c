<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图表调试页面</title>
    <script src="https://cdn.jsdelivr.net/npm/lightweight-charts@4.1.1/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #131722;
            color: #d1d4dc;
        }

        .container {
            padding: 20px;
        }

        .chart-section {
            margin-bottom: 30px;
        }

        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #ffffff;
        }

        .chart-container {
            width: 100%;
            height: 300px;
            border: 1px solid #2a2e39;
            background: #131722;
        }

        .info-panel {
            background: #1e222d;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .info-item {
            margin-bottom: 5px;
        }

        button {
            background: #2962ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        button:hover {
            background: #1e4db7;
        }

        .status {
            color: #26a69a;
            font-weight: bold;
        }

        .error {
            color: #ef5350;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>图表调试页面</h1>
        
        <div class="info-panel">
            <div class="info-item">状态: <span id="status" class="status">等待加载</span></div>
            <div class="info-item">股票代码: <span id="stockCode">other_801080_si_申万电子</span></div>
            <div class="info-item">数据点数: <span id="dataPoints">--</span></div>
            <div class="info-item">RSI有效值: <span id="rsiValid">--</span></div>
            <div class="info-item">MACD有效值: <span id="macdValid">--</span></div>
        </div>

        <div>
            <button onclick="loadData()">加载数据</button>
            <button onclick="createCharts()">创建图表</button>
            <button onclick="clearCharts()">清空图表</button>
        </div>

        <div class="chart-section">
            <div class="chart-title">价格图表</div>
            <div class="chart-container" id="priceChart"></div>
        </div>

        <div class="chart-section">
            <div class="chart-title">MACD图表</div>
            <div class="chart-container" id="macdChart"></div>
        </div>

        <div class="chart-section">
            <div class="chart-title">RSI图表</div>
            <div class="chart-container" id="rsiChart"></div>
        </div>
    </div>

    <script>
        let chartData = null;
        let priceChart = null;
        let macdChart = null;
        let rsiChart = null;

        function updateStatus(message, isError = false) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = isError ? 'error' : 'status';
            console.log('[Debug]', message);
        }

        async function loadData() {
            updateStatus('正在加载数据...');
            
            try {
                const stockCode = document.getElementById('stockCode').textContent;
                const response = await fetch(`/api/stock_chart/${stockCode}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.message || '数据加载失败');
                }
                
                chartData = data;
                
                // 更新信息面板
                document.getElementById('dataPoints').textContent = data.data_points;
                
                // 分析RSI数据
                const rsiNonDefault = data.rsi.filter(x => x !== 50.0).length;
                document.getElementById('rsiValid').textContent = `${rsiNonDefault}/${data.rsi.length}`;
                
                // 分析MACD数据
                const macdNonZero = data.macd.macd.filter(x => x !== 0).length;
                document.getElementById('macdValid').textContent = `${macdNonZero}/${data.macd.macd.length}`;
                
                updateStatus('数据加载成功');
                
                console.log('数据详情:', {
                    dates: data.dates.length,
                    prices: Object.keys(data.prices),
                    rsiSample: data.rsi.slice(13, 18),
                    macdSample: data.macd.macd.slice(-5),
                    signalSample: data.macd.signal.slice(-5)
                });
                
            } catch (error) {
                updateStatus(`数据加载失败: ${error.message}`, true);
                console.error('加载错误:', error);
            }
        }

        function clearCharts() {
            if (priceChart) {
                priceChart.remove();
                priceChart = null;
            }
            if (macdChart) {
                macdChart.remove();
                macdChart = null;
            }
            if (rsiChart) {
                rsiChart.remove();
                rsiChart = null;
            }
            
            document.getElementById('priceChart').innerHTML = '';
            document.getElementById('macdChart').innerHTML = '';
            document.getElementById('rsiChart').innerHTML = '';
            
            updateStatus('图表已清空');
        }

        function createCharts() {
            if (!chartData) {
                updateStatus('请先加载数据', true);
                return;
            }
            
            updateStatus('正在创建图表...');
            
            try {
                clearCharts();
                
                // 创建价格图表
                const priceContainer = document.getElementById('priceChart');
                priceChart = LightweightCharts.createChart(priceContainer, {
                    width: priceContainer.clientWidth,
                    height: priceContainer.clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        timeVisible: true,
                    },
                });

                // 添加K线数据
                const candlestickData = chartData.dates.map((date, index) => ({
                    time: date,
                    open: parseFloat(chartData.prices.open[index]),
                    high: parseFloat(chartData.prices.high[index]),
                    low: parseFloat(chartData.prices.low[index]),
                    close: parseFloat(chartData.prices.close[index])
                }));

                const candlestickSeries = priceChart.addCandlestickSeries({
                    upColor: '#26a69a',
                    downColor: '#ef5350',
                    borderVisible: false,
                    wickUpColor: '#26a69a',
                    wickDownColor: '#ef5350',
                });
                candlestickSeries.setData(candlestickData);

                // 创建MACD图表
                const macdContainer = document.getElementById('macdChart');
                macdChart = LightweightCharts.createChart(macdContainer, {
                    width: macdContainer.clientWidth,
                    height: macdContainer.clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        timeVisible: true,
                    },
                });

                // 添加MACD线
                const macdData = chartData.dates.map((date, index) => ({
                    time: date,
                    value: parseFloat(chartData.macd.macd[index]) || 0
                }));

                const macdSeries = macdChart.addLineSeries({
                    color: '#2962ff',
                    lineWidth: 2,
                });
                macdSeries.setData(macdData);

                // 添加信号线
                const signalData = chartData.dates.map((date, index) => ({
                    time: date,
                    value: parseFloat(chartData.macd.signal[index]) || 0
                }));

                const signalSeries = macdChart.addLineSeries({
                    color: '#ff6d00',
                    lineWidth: 2,
                });
                signalSeries.setData(signalData);

                // 创建RSI图表
                const rsiContainer = document.getElementById('rsiChart');
                rsiChart = LightweightCharts.createChart(rsiContainer, {
                    width: rsiContainer.clientWidth,
                    height: rsiContainer.clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        timeVisible: true,
                    },
                });

                // 添加RSI线
                const rsiData = chartData.dates.map((date, index) => ({
                    time: date,
                    value: parseFloat(chartData.rsi[index]) || 50
                }));

                const rsiSeries = rsiChart.addLineSeries({
                    color: '#9c27b0',
                    lineWidth: 2,
                });
                rsiSeries.setData(rsiData);

                // 添加超买超卖线
                const overboughtLine = rsiChart.addLineSeries({
                    color: '#ef5350',
                    lineWidth: 1,
                    lineStyle: LightweightCharts.LineStyle.Dashed,
                });
                overboughtLine.setData(chartData.dates.map(date => ({ time: date, value: 70 })));

                const oversoldLine = rsiChart.addLineSeries({
                    color: '#26a69a',
                    lineWidth: 1,
                    lineStyle: LightweightCharts.LineStyle.Dashed,
                });
                oversoldLine.setData(chartData.dates.map(date => ({ time: date, value: 30 })));

                updateStatus('图表创建成功');
                
            } catch (error) {
                updateStatus(`图表创建失败: ${error.message}`, true);
                console.error('图表创建错误:', error);
            }
        }

        // 窗口大小调整
        window.addEventListener('resize', () => {
            if (priceChart) {
                const container = document.getElementById('priceChart');
                priceChart.applyOptions({
                    width: container.clientWidth,
                    height: container.clientHeight,
                });
            }
            if (macdChart) {
                const container = document.getElementById('macdChart');
                macdChart.applyOptions({
                    width: container.clientWidth,
                    height: container.clientHeight,
                });
            }
            if (rsiChart) {
                const container = document.getElementById('rsiChart');
                rsiChart.applyOptions({
                    width: container.clientWidth,
                    height: container.clientHeight,
                });
            }
        });

        // 页面加载时自动加载数据
        window.addEventListener('load', () => {
            loadData();
        });
    </script>
</body>
</html>
