<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业股票技术分析图表</title>
    <script src="https://cdn.jsdelivr.net/npm/lightweight-charts@4.1.1/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #131722;
            color: #d1d4dc;
            overflow: hidden;
        }

        .header {
            background: #1e222d;
            padding: 12px 20px;
            border-bottom: 1px solid #2a2e39;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .stock-info h1 {
            margin: 0;
            font-size: 20px;
            color: #ffffff;
            font-weight: 600;
        }

        .stock-price {
            font-size: 18px;
            font-weight: bold;
            color: #26a69a;
            margin-right: 15px;
        }

        .refresh-btn {
            background: #2962ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }

        .refresh-btn:hover {
            background: #1e53e5;
        }

        .chart-container {
            height: calc(100vh - 60px);
            display: flex;
            flex-direction: column;
        }

        /* 早期重复样式已删除 */

        .sub-chart:last-child {
            border-bottom: none;
        }

        .chart-title {
            position: absolute;
            top: 8px;
            left: 12px;
            font-size: 12px;
            color: #787b86;
            z-index: 10;
            pointer-events: none;
            font-weight: 500;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #787b86;
            font-size: 14px;
        }

        .error {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ef5350;
            font-size: 14px;
            text-align: center;
        }

        .status-info {
            position: absolute;
            top: 8px;
            right: 12px;
            font-size: 11px;
            color: #787b86;
            z-index: 10;
        }

        .main-chart {
            height: 30%;
            border-bottom: 1px solid #2a2e39;
        }

        .sub-charts {
            height: 70%;
            display: flex;
            flex-direction: column;
        }

        .sub-chart {
            height: 12.5%; /* 8个子图表，每个占1/8 */
            border-bottom: 1px solid #2a2e39;
            position: relative;
            min-height: 80px;
        }

        .chart-title {
            position: absolute;
            top: 10px;
            left: 15px;
            font-size: 14px;
            font-weight: bold;
            color: #787b86;
            z-index: 10;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #787b86;
            font-size: 16px;
        }

        .error {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ef5350;
            text-align: center;
        }

        .btn {
            background: #2962ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }

        .btn:hover {
            background: #1e4ba8;
        }


    </style>
</head>
<body>
    <div class="header">
        <div class="stock-info">
            <h1 id="stockTitle">加载中...</h1>
        </div>
        <div class="controls">
            <span class="stock-price" id="stockPrice">--</span>
            <select id="data-limit-select" class="form-select form-select-sm me-2" style="width: 120px; display: inline-block;">
                <option value="100">100天</option>
                <option value="250">250天</option>
                <option value="500">500天</option>
                <option value="1000" selected>1000天</option>
                <option value="2000">2000天</option>
            </select>
            <button class="refresh-btn" id="refreshBtn" onclick="refreshData()">
                刷新
            </button>
        </div>
    </div>

    <div class="chart-container">
        <div class="main-chart" id="mainChart">
            <div class="chart-title">K线图 & 移动平均线</div>
            <div class="status-info" id="mainStatus"></div>
            <div class="loading" id="mainLoading">正在加载数据...</div>
        </div>

        <div class="sub-charts">
            <!-- 8个指标垂直排列：交替显示统一接口和权威版本 -->
            
            <!-- MACD指标组 -->
            <div class="sub-chart" id="mcsiMacdChart">
                <div class="chart-title">MCSI MACD统一接口</div>
                <div class="status-info" id="mcsiMacdStatus"></div>
            </div>
            
            <div class="sub-chart" id="authorityMacdChart">
                <div class="chart-title">权威MCSI MACD（Pine Script完整版）</div>
                <div class="status-info" id="authorityMacdStatus"></div>
            </div>

            <!-- MMT指标组 -->
            <div class="sub-chart" id="mcsiMmtChart">
                <div class="chart-title">MCSI MMT统一接口</div>
                <div class="status-info" id="mcsiMmtStatus"></div>
            </div>
            
            <div class="sub-chart" id="authorityMmtChart">
                <div class="chart-title">权威MCSI MMT（Pine Script完整版）</div>
                <div class="status-info" id="authorityMmtStatus"></div>
            </div>

            <!-- RSI指标组 -->
            <div class="sub-chart" id="mcsiRsiChart">
                <div class="chart-title">MCSI RSI统一接口</div>
                <div class="status-info" id="mcsiRsiStatus"></div>
            </div>
            
            <div class="sub-chart" id="authorityRsiChart">
                <div class="chart-title">权威MCSI RSI（Pine Script完整版）</div>
                <div class="status-info" id="authorityRsiStatus"></div>
            </div>

            <!-- TTM指标组 -->
            <div class="sub-chart" id="mcsiTtmChart">
                <div class="chart-title">MCSI TTM统一接口</div>
                <div class="status-info" id="mcsiTtmStatus"></div>
            </div>
            
            <div class="sub-chart" id="authorityTtmChart">
                <div class="chart-title">权威MCSI TTM（Pine Script完整版）</div>
                <div class="status-info" id="authorityTtmStatus"></div>
            </div>
        </div>
    </div>
    
    <script>
        let mainChart = null;
        let mcsiMacdChart = null;
        let mcsiMmtChart = null;
        let mcsiRsiChart = null;
        let mcsiTtmChart = null;
        // 权威版本图表
        let authorityMacdChart = null;
        let authorityMmtChart = null;
        let authorityRsiChart = null;
        let authorityTtmChart = null;
        let currentStock = null;

        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 日志函数
        function log(message) {
            console.log('[Chart]', message);
        }

        // 显示错误
        function showError(message, containerId = 'mainChart') {
            const container = document.getElementById(containerId);
            const loading = container.querySelector('.loading');
            if (loading) loading.style.display = 'none';

            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            container.appendChild(errorDiv);
        }

        // 更新状态信息
        function updateStatus(containerId, message) {
            const statusElement = document.getElementById(containerId);
            if (statusElement) {
                statusElement.textContent = message;
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            const stockCode = getUrlParameter('stock') || '300584';
            const stockName = getUrlParameter('name') || '海辰药业';
            const dataLimit = getUrlParameter('limit') || '1000';

            currentStock = { code: stockCode, name: stockName };

            // 设置数据量选择器的初始值
            const dataLimitSelect = document.getElementById('data-limit-select');
            if (dataLimitSelect) {
                dataLimitSelect.value = dataLimit;
            }

            log('页面加载完成');
            log('股票代码: ' + stockCode);
            log('股票名称: ' + stockName);
            log('数据量: ' + dataLimit);

            // 检查LightweightCharts
            if (typeof LightweightCharts === 'undefined') {
                showError('LightweightCharts库未加载');
                return;
            }

            log('LightweightCharts库正常');

            // 更新标题
            document.getElementById('stockTitle').textContent = `${stockName} (${stockCode})`;

            // 初始化图表
            initializeCharts();

            // 加载数据
            loadStockData(stockCode);
        });

        // 刷新数据
        function refreshData() {
            if (currentStock) {
                log('刷新数据...');
                loadStockData(currentStock.code);
            }
        }
        
        // 初始化图表
        function initializeCharts() {
            log('初始化图表...');

            // 重置时间轴同步标志
            timeAxisSyncSetup = false;

            try {
                // 主图表 - K线和移动平均线
                mainChart = LightweightCharts.createChart(document.getElementById('mainChart'), {
                    width: document.getElementById('mainChart').clientWidth,
                    height: document.getElementById('mainChart').clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        fixLeftEdge: true,
                        fixRightEdge: false, // 允许右侧扩展
                        rightOffset: 50, // 增加右侧留白空间
                        barSpacing: 6, // 设置K线间距
                        minBarSpacing: 0.5, // 最小K线间距
                        visible: false, // 隐藏主图表的时间轴
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                    },
                });

                log('主图表初始化完成');

            } catch (error) {
                log('图表初始化失败: ' + error.message);
                showError('图表初始化失败: ' + error.message);
            }
        }

        // 加载股票数据
        async function loadStockData(stockCode) {
            try {
                log('开始加载股票数据: ' + stockCode);
                updateStatus('mainStatus', '加载中...');

                // 获取数据量参数
                const dataLimit = document.getElementById('data-limit-select').value;
                log('数据量参数: ' + dataLimit);

                const apiUrl = `/api/stock_chart/${stockCode}?limit=${dataLimit}`;
                log('API URL: ' + apiUrl);

                const response = await fetch(apiUrl);
                log('API响应状态: ' + response.status);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                log('API响应数据: ' + JSON.stringify(data).substring(0, 200) + '...');

                if (data.success) {
                    log('数据加载成功，数据点数: ' + data.data_points);
                    log('日期数组长度: ' + data.dates.length);
                    log('价格数据: ' + JSON.stringify(data.prices).substring(0, 100) + '...');

                    renderCharts(data);

                    // 更新价格显示
                    if (data.prices && data.prices.close && data.prices.close.length > 0) {
                        const latestPrice = data.prices.close[data.prices.close.length - 1];
                        document.getElementById('stockPrice').textContent = latestPrice.toFixed(2);
                        log('最新价格: ' + latestPrice);
                    }
                } else {
                    const errorMsg = data.message || data.error || '未知错误';
                    log('数据加载失败: ' + errorMsg);
                    showError('数据加载失败: ' + errorMsg);
                }
            } catch (error) {
                log('加载失败: ' + error.message);
                console.error('详细错误:', error);
                showError('网络错误: ' + error.message);
            }
        }

        // 渲染图表
        function renderCharts(data) {
            try {
                log('开始渲染图表...');
                log('数据验证: dates=' + (data.dates ? data.dates.length : 'null') +
                    ', prices=' + (data.prices ? 'ok' : 'null') +
                    ', ma=' + (data.ma ? 'ok' : 'null') +
                    ', macd=' + (data.macd ? 'ok' : 'null') +
                    ', rsi=' + (data.rsi ? data.rsi.length : 'null') +
                    ', mcsi_indicators=' + (data.mcsi_indicators ? 'ok' : 'null'));

                // 清理现有图表（确保完全重新初始化）
                if (mainChart) {
                    log('清理现有主图表');
                    mainChart.remove();
                    mainChart = null;
                }

                if (mcsiMacdChart) {
                    log('清理现有MCSI MACD图表');
                    mcsiMacdChart.remove();
                    mcsiMacdChart = null;
                }
                if (mcsiMmtChart) {
                    log('清理现有MCSI MMT图表');
                    mcsiMmtChart.remove();
                    mcsiMmtChart = null;
                }
                if (mcsiRsiChart) {
                    log('清理现有MCSI RSI图表');
                    mcsiRsiChart.remove();
                    mcsiRsiChart = null;
                }
                if (mcsiTtmChart) {
                    log('清理现有MCSI TTM图表');
                    mcsiTtmChart.remove();
                    mcsiTtmChart = null;
                }
                
                // 清理权威版本图表
                if (authorityMacdChart) {
                    log('清理现有权威MACD图表');
                    authorityMacdChart.remove();
                    authorityMacdChart = null;
                }
                if (authorityMmtChart) {
                    log('清理现有权威MMT图表');
                    authorityMmtChart.remove();
                    authorityMmtChart = null;
                }
                if (authorityRsiChart) {
                    log('清理现有权威RSI图表');
                    authorityRsiChart.remove();
                    authorityRsiChart = null;
                }
                if (authorityTtmChart) {
                    log('清理现有权威TTM图表');
                    authorityTtmChart.remove();
                    authorityTtmChart = null;
                }

                // 重新初始化图表
                log('重新初始化图表...');
                initializeCharts();

                // 渲染主图表
                log('渲染主图表...');
                renderMainChart(data);

                // 渲染8个MCSI指标图表（垂直排列）
                if (data.mcsi_indicators) {
                    // 按垂直顺序渲染：统一接口和权威版本交替显示
                    
                    // MACD指标组
                    log('渲染MCSI MACD图表...');
                    renderMCSIMACDChart(data);
                    log('渲染权威MCSI MACD图表...');
                    renderAuthorityMACDChart(data);

                    // MMT指标组
                    log('渲染MCSI MMT图表...');
                    renderMCSIMMTChart(data);
                    log('渲染权威MCSI MMT图表...');
                    renderAuthorityMMTChart(data);

                    // RSI指标组
                    log('渲染MCSI RSI图表...');
                    renderMCSIRSIChart(data);
                    log('渲染权威MCSI RSI图表...');
                    renderAuthorityRSIChart(data);
                    
                    // TTM指标组
                    log('渲染MCSI TTM图表...');
                    renderMCSITTMChart(data);
                    log('渲染权威MCSI TTM图表...');
                    renderAuthorityTTMChart(data);
                }

                log('图表渲染完成');

                // 重新设置时间轴同步
                setTimeout(() => {
                    log('设置MCSI指标时间轴同步...');
                    setupTimeAxisSync();
                    performInitialSync();
                }, 100);

            } catch (error) {
                log('图表渲染失败: ' + error.message);
                console.error('图表渲染详细错误:', error);
                showError('图表渲染失败: ' + error.message);
            }
        }

        // 渲染主图表（K线 + 移动平均线）
        function renderMainChart(data) {
            if (!mainChart || !data.dates || !data.prices) {
                log('主图表数据不完整');
                return;
            }

            // 隐藏加载提示
            const loading = document.getElementById('mainLoading');
            if (loading) loading.style.display = 'none';

            // K线数据
            const candleData = data.dates.map((date, i) => ({
                time: date,
                open: data.prices.open[i],
                high: data.prices.high[i],
                low: data.prices.low[i],
                close: data.prices.close[i]
            }));

            const candleSeries = mainChart.addCandlestickSeries({
                upColor: '#26a69a',
                downColor: '#ef5350',
                borderVisible: false,
                wickUpColor: '#26a69a',
                wickDownColor: '#ef5350',
            });
            candleSeries.setData(candleData);
            log('K线数据设置完成: ' + candleData.length + ' 个数据点');

            // 移动平均线
            if (data.ma) {
                // MA5 - 蓝色
                if (data.ma.ma5) {
                    const ma5Data = data.dates.map((date, i) => ({
                        time: date,
                        value: data.ma.ma5[i]
                    })).filter(item => item.value !== null && item.value !== undefined && item.value > 0);

                    if (ma5Data.length > 0) {
                        const ma5Series = mainChart.addLineSeries({
                            color: '#2196f3',
                            lineWidth: 2,
                            title: 'MA5'
                        });
                        ma5Series.setData(ma5Data);
                        log('MA5数据点: ' + ma5Data.length);
                    }
                }

                // MA20 - 橙色
                if (data.ma.ma20) {
                    const ma20Data = data.dates.map((date, i) => ({
                        time: date,
                        value: data.ma.ma20[i]
                    })).filter(item => item.value !== null && item.value !== undefined && item.value > 0);

                    if (ma20Data.length > 0) {
                        const ma20Series = mainChart.addLineSeries({
                            color: '#ff9800',
                            lineWidth: 2,
                            title: 'MA20'
                        });
                        ma20Series.setData(ma20Data);
                        log('MA20数据点: ' + ma20Data.length);
                    }
                }

                // MA50 - 粉色
                if (data.ma.ma50) {
                    const ma50Data = data.dates.map((date, i) => ({
                        time: date,
                        value: data.ma.ma50[i]
                    })).filter(item => item.value !== null && item.value !== undefined && item.value > 0);

                    if (ma50Data.length > 0) {
                        const ma50Series = mainChart.addLineSeries({
                            color: '#e91e63',
                            lineWidth: 2,
                            title: 'MA50'
                        });
                        ma50Series.setData(ma50Data);
                        log('MA50数据点: ' + ma50Data.length);
                    }
                }

                // MA200 - 紫色
                if (data.ma.ma200) {
                    const ma200Data = data.dates.map((date, i) => ({
                        time: date,
                        value: data.ma.ma200[i]
                    })).filter(item => item.value !== null && item.value !== undefined && item.value > 0);

                    if (ma200Data.length > 0) {
                        const ma200Series = mainChart.addLineSeries({
                            color: '#9c27b0',
                            lineWidth: 2,
                            title: 'MA200'
                        });
                        ma200Series.setData(ma200Data);
                        log('MA200数据点: ' + ma200Data.length);
                    }
                }
            }

            updateStatus('mainStatus', `K线: ${candleData.length}点`);
        }





        // 时间轴同步状态标志
        let timeAxisSyncSetup = false;

        // 设置时间轴同步 - 9个图表垂直排列（1主图+8个MCSI指标）
        function setupTimeAxisSync() {
            // 检查所有图表是否初始化
            const requiredCharts = [
                { chart: mainChart, name: 'mainChart' },
                { chart: mcsiMacdChart, name: 'mcsiMacdChart' },
                { chart: authorityMacdChart, name: 'authorityMacdChart' },
                { chart: mcsiMmtChart, name: 'mcsiMmtChart' },
                { chart: authorityMmtChart, name: 'authorityMmtChart' },
                { chart: mcsiRsiChart, name: 'mcsiRsiChart' },
                { chart: authorityRsiChart, name: 'authorityRsiChart' },
                { chart: mcsiTtmChart, name: 'mcsiTtmChart' },
                { chart: authorityTtmChart, name: 'authorityTtmChart' }
            ];
            
            const missingCharts = requiredCharts.filter(item => !item.chart);
            if (missingCharts.length > 0) {
                log('以下图表未初始化，跳过时间轴同步: ' + missingCharts.map(item => item.name).join(', '));
                return;
            }

            // 避免重复设置
            if (timeAxisSyncSetup) {
                log('时间轴同步已设置，跳过重复设置');
                return;
            }

            log('设置9个图表垂直排列时间轴同步...');
            timeAxisSyncSetup = true;

            // 简化的同步函数
            function syncAllCharts(masterChart) {
                const masterLogicalRange = masterChart.timeScale().getVisibleLogicalRange();

                if (masterLogicalRange) {
                    const charts = [
                        mainChart, mcsiMacdChart, authorityMacdChart, mcsiMmtChart, authorityMmtChart,
                        mcsiRsiChart, authorityRsiChart, mcsiTtmChart, authorityTtmChart
                    ];
                    charts.forEach(chart => {
                        if (chart && chart !== masterChart) {
                            chart.timeScale().setVisibleLogicalRange(masterLogicalRange);
                        }
                    });
                }
            }

            // 设置简化的时间轴同步
            const charts = [
                mainChart, mcsiMacdChart, authorityMacdChart, mcsiMmtChart, authorityMmtChart,
                mcsiRsiChart, authorityRsiChart, mcsiTtmChart, authorityTtmChart
            ];

            charts.forEach(chart => {
                if (chart) {
                    chart.timeScale().subscribeVisibleLogicalRangeChange(() => {
                        syncAllCharts(chart);
                    });
                }
            });

            log('9个图表垂直排列时间轴同步设置完成');
        }

        // 执行初始同步
        function performInitialSync() {
            if (!mainChart) {
                log('主图表未初始化，跳过初始同步');
                return;
            }

            try {
                log('执行MCSI指标初始同步');

                // 让所有图表自适应内容（垂直排列顺序）
                const charts = [
                    mainChart, mcsiMacdChart, authorityMacdChart, mcsiMmtChart, authorityMmtChart,
                    mcsiRsiChart, authorityRsiChart, mcsiTtmChart, authorityTtmChart
                ];
                charts.forEach(chart => {
                    if (chart) {
                        chart.timeScale().fitContent();
                    }
                });

                log('初始同步完成');
            } catch (error) {
                log('初始同步失败: ' + error.message);
            }
        }

        // 🔒 安全的MCSI MACD图表 - 仅显示评分线
        function renderMCSIMACDChart(data) {
            if (!data.mcsi_indicators || !data.mcsi_indicators.mcsi_macd) {
                log('MCSI MACD数据不存在');
                updateStatus('mcsiMacdStatus', '无数据');
                return;
            }

            try {
                const mcsiMacdData = data.mcsi_indicators.mcsi_macd;

                // 检查是否有可用的.so文件数据
                if (!mcsiMacdData.available) {
                    log('MCSI MACD高级单元不可用');
                    updateStatus('mcsiMacdStatus', '⚠️ 仅限.so版本');
                    return;
                }

                // 初始化MCSI MACD图表
                mcsiMacdChart = LightweightCharts.createChart(document.getElementById('mcsiMacdChart'), {
                    width: document.getElementById('mcsiMacdChart').clientWidth,
                    height: document.getElementById('mcsiMacdChart').clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        visible: false,
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                    },
                });

                // 🔒 仅显示MACD评分线 - 不暴露算法细节
                if (mcsiMacdData.macd_score) {
                    const scoreData = data.dates.map((date, i) => ({
                        time: date,
                        value: mcsiMacdData.macd_score[i]
                    })).filter(item => item.value !== null && item.value !== undefined && !isNaN(item.value));

                    const scoreSeries = mcsiMacdChart.addLineSeries({
                        color: '#00ff00', // 绿色评分线
                        lineWidth: 3,
                        title: 'MCSI MACD Score'
                    });
                    scoreSeries.setData(scoreData);
                }

                // 添加零轴线作为参考
                const zeroLineData = data.dates.map(date => ({
                    time: date,
                    value: 0
                }));
                const zeroLineSeries = mcsiMacdChart.addLineSeries({
                    color: '#808080',
                    lineWidth: 1,
                    lineStyle: 2, // 虚线
                    title: 'Zero Line'
                });
                zeroLineSeries.setData(zeroLineData);

                updateStatus('mcsiMacdStatus', '🔒 MCSI MACD (安全模式)');

            } catch (error) {
                log('MCSI MACD图表渲染失败: ' + error.message);
                updateStatus('mcsiMacdStatus', '渲染失败');
            }
        }

        // 辅助函数：获取MACD柱状图颜色
        function getMacdHistogramColor(colorName) {
            const colorMap = {
                'aqua': '#00ffff',
                'blue': '#0000ff',
                'red': '#ff0000',
                'maroon': '#800000',
                'gray': '#808080'
            };
            return colorMap[colorName] || '#808080';
        }

        // 🔒 安全的MCSI MMT图表 - 仅显示评分线
        function renderMCSIMMTChart(data) {
            if (!data.mcsi_indicators || !data.mcsi_indicators.mcsi_mmt) {
                log('MCSI MMT数据不存在');
                updateStatus('mcsiMmtStatus', '无数据');
                return;
            }

            try {
                const mcsiMmtData = data.mcsi_indicators.mcsi_mmt;

                // 检查是否有可用的.so文件数据
                if (!mcsiMmtData.available) {
                    log('MCSI MMT高级单元不可用');
                    updateStatus('mcsiMmtStatus', '⚠️ 仅限.so版本');
                    return;
                }

                // 初始化MCSI MMT图表
                mcsiMmtChart = LightweightCharts.createChart(document.getElementById('mcsiMmtChart'), {
                    width: document.getElementById('mcsiMmtChart').clientWidth,
                    height: document.getElementById('mcsiMmtChart').clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        visible: false,
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                    },
                });

                // 🔒 仅显示MMT评分线 - 不暴露CSI缓冲区、轨道、背离等算法细节
                if (mcsiMmtData.mmt_score) {
                    const mmtScoreData = data.dates.map((date, i) => ({
                        time: date,
                        value: mcsiMmtData.mmt_score[i]
                    })).filter(item => item.value !== null && item.value !== undefined && !isNaN(item.value));

                    const mmtScoreSeries = mcsiMmtChart.addLineSeries({
                        color: '#ffff00', // 黄色评分线
                        lineWidth: 3,
                        title: 'MCSI MMT Score'
                    });
                    mmtScoreSeries.setData(mmtScoreData);
                }

                // 添加零轴线作为参考
                const zeroLineData = data.dates.map(date => ({
                    time: date,
                    value: 0
                }));
                const zeroLineSeries = mcsiMmtChart.addLineSeries({
                    color: '#808080',
                    lineWidth: 1,
                    lineStyle: 2, // 虚线
                    title: 'Zero Line'
                });
                zeroLineSeries.setData(zeroLineData);

                updateStatus('mcsiMmtStatus', '🔒 MCSI MMT (安全模式)');

            } catch (error) {
                log('MCSI MMT图表渲染失败: ' + error.message);
                updateStatus('mcsiMmtStatus', '渲染失败');
            }
        }

        // 🔒 安全的MCSI RSI图表 - 仅显示评分线
        function renderMCSIRSIChart(data) {
            if (!data.mcsi_indicators || !data.mcsi_indicators.mcsi_rsi) {
                log('MCSI RSI数据不存在');
                updateStatus('mcsiRsiStatus', '无数据');
                return;
            }

            try {
                const mcsiRsiData = data.mcsi_indicators.mcsi_rsi;

                // 检查是否有可用的.so文件数据
                if (!mcsiRsiData.available) {
                    log('MCSI RSI高级单元不可用');
                    updateStatus('mcsiRsiStatus', '⚠️ 仅限.so版本');
                    return;
                }

                // 初始化MCSI RSI图表
                mcsiRsiChart = LightweightCharts.createChart(document.getElementById('mcsiRsiChart'), {
                    width: document.getElementById('mcsiRsiChart').clientWidth,
                    height: document.getElementById('mcsiRsiChart').clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        visible: false,
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                    },
                });

                // 🔒 仅显示RSI评分线 - 不暴露日周线CRSI、上下轨等算法细节
                if (mcsiRsiData.rsi_score) {
                    const rsiScoreData = data.dates.map((date, i) => ({
                        time: date,
                        value: mcsiRsiData.rsi_score[i]
                    })).filter(item => item.value !== null && item.value !== undefined && !isNaN(item.value));

                    const rsiScoreSeries = mcsiRsiChart.addLineSeries({
                        color: '#ff6600', // 橙色评分线
                        lineWidth: 3,
                        title: 'MCSI RSI Score'
                    });
                    rsiScoreSeries.setData(rsiScoreData);
                }

                // 添加零轴线作为参考
                const zeroLineData = data.dates.map(date => ({
                    time: date,
                    value: 0
                }));
                const zeroLineSeries = mcsiRsiChart.addLineSeries({
                    color: '#808080',
                    lineWidth: 1,
                    lineStyle: 2, // 虚线
                    title: 'Zero Line'
                });
                zeroLineSeries.setData(zeroLineData);

                updateStatus('mcsiRsiStatus', '🔒 MCSI RSI (安全模式)');

            } catch (error) {
                log('MCSI RSI图表渲染失败: ' + error.message);
                updateStatus('mcsiRsiStatus', '渲染失败');
            }
        }

        // 🔒 安全的MCSI TTM图表 - 仅显示评分线
        function renderMCSITTMChart(data) {
            if (!data.mcsi_indicators || !data.mcsi_indicators.mcsi_ttm) {
                log('MCSI TTM数据不存在');
                updateStatus('mcsiTtmStatus', '无数据');
                return;
            }

            try {
                const mcsiTtmData = data.mcsi_indicators.mcsi_ttm;

                // 检查是否有可用的.so文件数据
                if (!mcsiTtmData.available) {
                    log('MCSI TTM高级单元不可用');
                    updateStatus('mcsiTtmStatus', '⚠️ 仅限.so版本');
                    return;
                }

                // 初始化MCSI TTM图表
                mcsiTtmChart = LightweightCharts.createChart(document.getElementById('mcsiTtmChart'), {
                    width: document.getElementById('mcsiTtmChart').clientWidth,
                    height: document.getElementById('mcsiTtmChart').clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        visible: true, // TTM图表显示时间轴
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                    },
                });

                // 🔒 仅显示TTM评分线 - 不暴露计数器、形态识别等算法细节
                if (mcsiTtmData.ttm_score) {
                    const ttmScoreData = data.dates.map((date, i) => ({
                        time: date,
                        value: mcsiTtmData.ttm_score[i]
                    })).filter(item => item.value !== null && item.value !== undefined && !isNaN(item.value));

                    const ttmScoreSeries = mcsiTtmChart.addLineSeries({
                        color: '#9966ff', // 紫色评分线
                        lineWidth: 3,
                        title: 'MCSI TTM Score'
                    });
                    ttmScoreSeries.setData(ttmScoreData);
                }

                // 添加零轴线作为参考
                const zeroLineData = data.dates.map(date => ({
                    time: date,
                    value: 0
                }));
                const zeroLineSeries = mcsiTtmChart.addLineSeries({
                    color: '#808080',
                    lineWidth: 1,
                    lineStyle: 2, // 虚线
                    title: 'Zero Line'
                });
                zeroLineSeries.setData(zeroLineData);

                updateStatus('mcsiTtmStatus', '🔒 MCSI TTM (安全模式)');

            } catch (error) {
                log('MCSI TTM图表渲染失败: ' + error.message);
                updateStatus('mcsiTtmStatus', '渲染失败');
            }
        }

        // 🔧 权威版本MCSI MACD图表
        function renderAuthorityMACDChart(data) {
            if (!data.mcsi_indicators || !data.mcsi_indicators.authority_mcsi_macd) {
                log('权威MCSI MACD数据不存在');
                updateStatus('authorityMacdStatus', '无数据');
                return;
            }

            try {
                const authMacdData = data.mcsi_indicators.authority_mcsi_macd;

                if (!authMacdData.available) {
                    log('权威MCSI MACD单元不可用');
                    updateStatus('authorityMacdStatus', '⚠️ 权威版本不可用');
                    return;
                }

                // 初始化权威MACD图表
                authorityMacdChart = LightweightCharts.createChart(document.getElementById('authorityMacdChart'), {
                    width: document.getElementById('authorityMacdChart').clientWidth,
                    height: document.getElementById('authorityMacdChart').clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        visible: false,
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                    },
                });

                // 🎨 Pine Script原生元素 - MACD线、信号线、柱状图
                const pineData = authMacdData.pine_data;
                if (pineData) {
                    log('渲染权威MACD Pine Script原生元素...');
                    
                    // MACD线（快线-慢线）
                    if (pineData.macd) {
                        const macdLineData = data.dates.slice(-150).map((date, i) => ({
                            time: date,
                            value: pineData.macd[i]
                        })).filter(item => item.value !== null && !isNaN(item.value));
                        
                        const macdLineSeries = authorityMacdChart.addLineSeries({
                            color: '#2196F3', // 蓝色MACD线
                            lineWidth: 2,
                            title: 'MACD线'
                        });
                        macdLineSeries.setData(macdLineData);
                    }
                    
                    // 信号线（MACD的移动平均）
                    if (pineData.signal) {
                        const signalLineData = data.dates.slice(-150).map((date, i) => ({
                            time: date,
                            value: pineData.signal[i]
                        })).filter(item => item.value !== null && !isNaN(item.value));
                        
                        const signalLineSeries = authorityMacdChart.addLineSeries({
                            color: '#FF9800', // 橙色信号线
                            lineWidth: 2,
                            title: '信号线'
                        });
                        signalLineSeries.setData(signalLineData);
                    }
                    
                    // 柱状图（MACD - 信号线）
                    if (pineData.histogram) {
                        const histogramData = data.dates.slice(-150).map((date, i) => ({
                            time: date,
                            value: pineData.histogram[i]
                        })).filter(item => item.value !== null && !isNaN(item.value));
                        
                        const histogramSeries = authorityMacdChart.addHistogramSeries({
                            color: '#4CAF50', // 绿色柱状图
                            priceFormat: {
                                type: 'volume',
                            },
                            title: '柱状图'
                        });
                        histogramSeries.setData(histogramData);
                    }
                    
                    // 动态阈值上下轨
                    if (pineData.dynamic_threshold) {
                        const upperThresholdData = data.dates.slice(-150).map((date, i) => ({
                            time: date,
                            value: pineData.dynamic_threshold[i]
                        })).filter(item => item.value !== null && !isNaN(item.value));
                        
                        const lowerThresholdData = data.dates.slice(-150).map((date, i) => ({
                            time: date,
                            value: -pineData.dynamic_threshold[i]
                        })).filter(item => item.value !== null && !isNaN(item.value));
                        
                        // 上阈值线
                        const upperThresholdSeries = authorityMacdChart.addLineSeries({
                            color: '#FF5722',
                            lineWidth: 1,
                            lineStyle: 2, // 虚线
                            title: '动态上阈值'
                        });
                        upperThresholdSeries.setData(upperThresholdData);
                        
                        // 下阈值线
                        const lowerThresholdSeries = authorityMacdChart.addLineSeries({
                            color: '#FF5722',
                            lineWidth: 1,
                            lineStyle: 2, // 虚线
                            title: '动态下阈值'
                        });
                        lowerThresholdSeries.setData(lowerThresholdData);
                    }
                }

                // 权威MACD评分线（主要指标）
                if (authMacdData.macd_score) {
                    const scoreData = data.dates.map((date, i) => ({
                        time: date,
                        value: authMacdData.macd_score[i]
                    })).filter(item => item.value !== null && item.value !== undefined && !isNaN(item.value));

                    const scoreSeries = authorityMacdChart.addLineSeries({
                        color: '#00cc00', // 深绿色评分线
                        lineWidth: 4, // 加粗主要指标
                        title: '权威MCSI MACD评分'
                    });
                    scoreSeries.setData(scoreData);
                }

                // 添加零轴线
                const zeroLineData = data.dates.map(date => ({
                    time: date,
                    value: 0
                }));
                const zeroLineSeries = authorityMacdChart.addLineSeries({
                    color: '#808080',
                    lineWidth: 1,
                    lineStyle: 2,
                    title: 'Zero Line'
                });
                zeroLineSeries.setData(zeroLineData);

                updateStatus('authorityMacdStatus', '🎨 权威MCSI MACD（完整版）');

            } catch (error) {
                log('权威MCSI MACD图表渲染失败: ' + error.message);
                updateStatus('authorityMacdStatus', '渲染失败');
            }
        }

        // 🔧 权威版本MCSI MMT图表
        function renderAuthorityMMTChart(data) {
            if (!data.mcsi_indicators || !data.mcsi_indicators.authority_mcsi_mmt) {
                log('权威MCSI MMT数据不存在');
                updateStatus('authorityMmtStatus', '无数据');
                return;
            }

            try {
                const authMmtData = data.mcsi_indicators.authority_mcsi_mmt;

                if (!authMmtData.available) {
                    log('权威MCSI MMT单元不可用');
                    updateStatus('authorityMmtStatus', '⚠️ 权威版本不可用');
                    return;
                }

                // 初始化权威MMT图表
                authorityMmtChart = LightweightCharts.createChart(document.getElementById('authorityMmtChart'), {
                    width: document.getElementById('authorityMmtChart').clientWidth,
                    height: document.getElementById('authorityMmtChart').clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        visible: false,
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                    },
                });

                // 🎨 Pine Script原生元素 - CSI缓冲区、上下轨道、背离信号
                const pineData = authMmtData.pine_data;
                if (pineData) {
                    log('渲染权威MMT Pine Script原生元素...');
                    
                    // CSI缓冲区（主震荡器）
                    if (pineData.csi_buffer) {
                        const csiBufferData = data.dates.slice(-150).map((date, i) => ({
                            time: date,
                            value: pineData.csi_buffer[i]
                        })).filter(item => item.value !== null && !isNaN(item.value));
                        
                        const csiBufferSeries = authorityMmtChart.addLineSeries({
                            color: '#03DAC6', // 青绿色CSI缓冲区
                            lineWidth: 2,
                            title: 'CSI缓冲区'
                        });
                        csiBufferSeries.setData(csiBufferData);
                    }
                    
                    // 上轨道线
                    if (pineData.high_band) {
                        const highBandData = data.dates.slice(-150).map((date, i) => ({
                            time: date,
                            value: pineData.high_band[i]
                        })).filter(item => item.value !== null && !isNaN(item.value));
                        
                        const highBandSeries = authorityMmtChart.addLineSeries({
                            color: '#E91E63', // 红色上轨道
                            lineWidth: 1,
                            lineStyle: 2, // 虚线
                            title: '上轨道'
                        });
                        highBandSeries.setData(highBandData);
                    }
                    
                    // 下轨道线
                    if (pineData.low_band) {
                        const lowBandData = data.dates.slice(-150).map((date, i) => ({
                            time: date,
                            value: pineData.low_band[i]
                        })).filter(item => item.value !== null && !isNaN(item.value));
                        
                        const lowBandSeries = authorityMmtChart.addLineSeries({
                            color: '#4CAF50', // 绿色下轨道
                            lineWidth: 1,
                            lineStyle: 2, // 虚线
                            title: '下轨道'
                        });
                        lowBandSeries.setData(lowBandData);
                    }
                }

                // 权威MMT评分线（主要指标）
                if (authMmtData.mmt_score) {
                    const mmtScoreData = data.dates.map((date, i) => ({
                        time: date,
                        value: authMmtData.mmt_score[i]
                    })).filter(item => item.value !== null && item.value !== undefined && !isNaN(item.value));

                    const mmtScoreSeries = authorityMmtChart.addLineSeries({
                        color: '#cccc00', // 深黄色评分线
                        lineWidth: 4, // 加粗主要指标
                        title: '权威MCSI MMT评分'
                    });
                    mmtScoreSeries.setData(mmtScoreData);
                }

                // 添加零轴线
                const zeroLineData = data.dates.map(date => ({
                    time: date,
                    value: 0
                }));
                const zeroLineSeries = authorityMmtChart.addLineSeries({
                    color: '#808080',
                    lineWidth: 1,
                    lineStyle: 2,
                    title: 'Zero Line'
                });
                zeroLineSeries.setData(zeroLineData);

                updateStatus('authorityMmtStatus', '🎨 权威MCSI MMT（完整版）');

            } catch (error) {
                log('权威MCSI MMT图表渲染失败: ' + error.message);
                updateStatus('authorityMmtStatus', '渲染失败');
            }
        }

        // 🔧 权威版本MCSI RSI图表
        function renderAuthorityRSIChart(data) {
            if (!data.mcsi_indicators || !data.mcsi_indicators.authority_mcsi_rsi) {
                log('权威MCSI RSI数据不存在');
                updateStatus('authorityRsiStatus', '无数据');
                return;
            }

            try {
                const authRsiData = data.mcsi_indicators.authority_mcsi_rsi;

                if (!authRsiData.available) {
                    log('权威MCSI RSI单元不可用');
                    updateStatus('authorityRsiStatus', '⚠️ 权威版本不可用');
                    return;
                }

                // 初始化权威RSI图表
                authorityRsiChart = LightweightCharts.createChart(document.getElementById('authorityRsiChart'), {
                    width: document.getElementById('authorityRsiChart').clientWidth,
                    height: document.getElementById('authorityRsiChart').clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        visible: false,
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                    },
                });

                // 🎨 Pine Script原生元素 - 日周线RSI、上下带、信号标记
                const pineData = authRsiData.pine_data;
                if (pineData) {
                    log('渲染权威RSI Pine Script原生元素...');
                    
                    // 日线CRSI
                    if (pineData.crsi_daily) {
                        const dailyCrsiData = data.dates.slice(-150).map((date, i) => ({
                            time: date,
                            value: pineData.crsi_daily[i]
                        })).filter(item => item.value !== null && !isNaN(item.value));
                        
                        const dailyCrsiSeries = authorityRsiChart.addLineSeries({
                            color: '#FF6B35', // 橙红色日线RSI
                            lineWidth: 2,
                            title: '日线CRSI'
                        });
                        dailyCrsiSeries.setData(dailyCrsiData);
                    }
                    
                    // 周线CRSI
                    if (pineData.crsi_weekly) {
                        const weeklyCrsiData = data.dates.slice(-150).map((date, i) => ({
                            time: date,
                            value: pineData.crsi_weekly[i]
                        })).filter(item => item.value !== null && !isNaN(item.value));
                        
                        const weeklyCrsiSeries = authorityRsiChart.addLineSeries({
                            color: '#9C27B0', // 紫色周线RSI
                            lineWidth: 2,
                            title: '周线CRSI'
                        });
                        weeklyCrsiSeries.setData(weeklyCrsiData);
                    }
                    
                    // 日线上下带
                    if (pineData.ub_daily && pineData.db_daily) {
                        const upperBandData = data.dates.slice(-150).map((date, i) => ({
                            time: date,
                            value: pineData.ub_daily[i]
                        })).filter(item => item.value !== null && !isNaN(item.value));
                        
                        const lowerBandData = data.dates.slice(-150).map((date, i) => ({
                            time: date,
                            value: pineData.db_daily[i]
                        })).filter(item => item.value !== null && !isNaN(item.value));
                        
                        // 上带
                        const upperBandSeries = authorityRsiChart.addLineSeries({
                            color: '#F44336',
                            lineWidth: 1,
                            lineStyle: 2, // 虚线
                            title: '日线上带'
                        });
                        upperBandSeries.setData(upperBandData);
                        
                        // 下带
                        const lowerBandSeries = authorityRsiChart.addLineSeries({
                            color: '#4CAF50',
                            lineWidth: 1,
                            lineStyle: 2, // 虚线
                            title: '日线下带'
                        });
                        lowerBandSeries.setData(lowerBandData);
                    }
                    
                    // 周线上下带
                    if (pineData.ub_weekly && pineData.db_weekly) {
                        const upperWeeklyData = data.dates.slice(-150).map((date, i) => ({
                            time: date,
                            value: pineData.ub_weekly[i]
                        })).filter(item => item.value !== null && !isNaN(item.value));
                        
                        const lowerWeeklyData = data.dates.slice(-150).map((date, i) => ({
                            time: date,
                            value: pineData.db_weekly[i]
                        })).filter(item => item.value !== null && !isNaN(item.value));
                        
                        // 周线上带
                        const upperWeeklySeries = authorityRsiChart.addLineSeries({
                            color: '#FF5722',
                            lineWidth: 1,
                            lineStyle: 3, // 点线
                            title: '周线上带'
                        });
                        upperWeeklySeries.setData(upperWeeklyData);
                        
                        // 周线下带
                        const lowerWeeklySeries = authorityRsiChart.addLineSeries({
                            color: '#8BC34A',
                            lineWidth: 1,
                            lineStyle: 3, // 点线
                            title: '周线下带'
                        });
                        lowerWeeklySeries.setData(lowerWeeklyData);
                    }
                }

                // 权威RSI评分线（主要指标）
                if (authRsiData.rsi_score) {
                    const rsiScoreData = data.dates.map((date, i) => ({
                        time: date,
                        value: authRsiData.rsi_score[i]
                    })).filter(item => item.value !== null && item.value !== undefined && !isNaN(item.value));

                    const rsiScoreSeries = authorityRsiChart.addLineSeries({
                        color: '#cc6600', // 深橙色评分线
                        lineWidth: 4, // 加粗主要指标
                        title: '权威MCSI RSI评分'
                    });
                    rsiScoreSeries.setData(rsiScoreData);
                }

                // 添加零轴线
                const zeroLineData = data.dates.map(date => ({
                    time: date,
                    value: 0
                }));
                const zeroLineSeries = authorityRsiChart.addLineSeries({
                    color: '#808080',
                    lineWidth: 1,
                    lineStyle: 2,
                    title: 'Zero Line'
                });
                zeroLineSeries.setData(zeroLineData);

                updateStatus('authorityRsiStatus', '🎨 权威MCSI RSI（完整版）');

            } catch (error) {
                log('权威MCSI RSI图表渲染失败: ' + error.message);
                updateStatus('authorityRsiStatus', '渲染失败');
            }
        }

        // 🔧 权威版本MCSI TTM图表
        function renderAuthorityTTMChart(data) {
            if (!data.mcsi_indicators || !data.mcsi_indicators.authority_mcsi_ttm) {
                log('权威MCSI TTM数据不存在');
                updateStatus('authorityTtmStatus', '无数据');
                return;
            }

            try {
                const authTtmData = data.mcsi_indicators.authority_mcsi_ttm;

                if (!authTtmData.available) {
                    log('权威MCSI TTM单元不可用');
                    updateStatus('authorityTtmStatus', '⚠️ 权威版本不可用');
                    return;
                }

                // 初始化权威TTM图表
                authorityTtmChart = LightweightCharts.createChart(document.getElementById('authorityTtmChart'), {
                    width: document.getElementById('authorityTtmChart').clientWidth,
                    height: document.getElementById('authorityTtmChart').clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        visible: true, // TTM图表显示时间轴
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                    },
                });

                // 🎨 Pine Script原生元素 - TD序列、形态识别、信号标记
                const pineData = authTtmData.pine_data;
                if (pineData) {
                    log('渲染权威TTM Pine Script原生元素...');
                    
                    // TD上升序列
                    if (pineData.td_up) {
                        const tdUpData = data.dates.slice(-150).map((date, i) => ({
                            time: date,
                            value: pineData.td_up[i]
                        })).filter(item => item.value !== null && !isNaN(item.value) && item.value > 0);
                        
                        const tdUpSeries = authorityTtmChart.addLineSeries({
                            color: '#4CAF50', // 绿色TD上升
                            lineWidth: 1,
                            lineStyle: 3, // 点线
                            title: 'TD上升序列'
                        });
                        tdUpSeries.setData(tdUpData);
                    }
                    
                    // TD下降序列
                    if (pineData.td_down) {
                        const tdDownData = data.dates.slice(-150).map((date, i) => ({
                            time: date,
                            value: -pineData.td_down[i] // 负值显示
                        })).filter(item => item.value !== null && !isNaN(item.value) && item.value < 0);
                        
                        const tdDownSeries = authorityTtmChart.addLineSeries({
                            color: '#F44336', // 红色TD下降
                            lineWidth: 1,
                            lineStyle: 3, // 点线
                            title: 'TD下降序列'
                        });
                        tdDownSeries.setData(tdDownData);
                    }
                    
                    // TD上升计数器
                    if (pineData.td_up_count) {
                        const tdUpCountData = data.dates.slice(-150).map((date, i) => ({
                            time: date,
                            value: pineData.td_up_count[i]
                        })).filter(item => item.value !== null && !isNaN(item.value) && item.value > 0);
                        
                        const tdUpCountSeries = authorityTtmChart.addHistogramSeries({
                            color: '#81C784', // 浅绿色柱状图
                            priceFormat: {
                                type: 'volume',
                            },
                            title: 'TD上升计数'
                        });
                        tdUpCountSeries.setData(tdUpCountData);
                    }
                    
                    // TD下降计数器
                    if (pineData.td_down_count) {
                        const tdDownCountData = data.dates.slice(-150).map((date, i) => ({
                            time: date,
                            value: -pineData.td_down_count[i] // 负值显示
                        })).filter(item => item.value !== null && !isNaN(item.value) && item.value < 0);
                        
                        const tdDownCountSeries = authorityTtmChart.addHistogramSeries({
                            color: '#E57373', // 浅红色柱状图
                            priceFormat: {
                                type: 'volume',
                            },
                            title: 'TD下降计数'
                        });
                        tdDownCountSeries.setData(tdDownCountData);
                    }
                }

                // 权威TTM评分线（主要指标）
                if (authTtmData.ttm_score) {
                    const ttmScoreData = data.dates.map((date, i) => ({
                        time: date,
                        value: authTtmData.ttm_score[i]
                    })).filter(item => item.value !== null && item.value !== undefined && !isNaN(item.value));

                    const ttmScoreSeries = authorityTtmChart.addLineSeries({
                        color: '#6633ff', // 深紫色评分线
                        lineWidth: 4, // 加粗主要指标
                        title: '权威MCSI TTM评分'
                    });
                    ttmScoreSeries.setData(ttmScoreData);
                }

                // 添加零轴线
                const zeroLineData = data.dates.map(date => ({
                    time: date,
                    value: 0
                }));
                const zeroLineSeries = authorityTtmChart.addLineSeries({
                    color: '#808080',
                    lineWidth: 1,
                    lineStyle: 2,
                    title: 'Zero Line'
                });
                zeroLineSeries.setData(zeroLineData);

                updateStatus('authorityTtmStatus', '🎨 权威MCSI TTM（完整版）');

            } catch (error) {
                log('权威MCSI TTM图表渲染失败: ' + error.message);
                updateStatus('authorityTtmStatus', '渲染失败');
            }
        }

        // 强制调整所有MCSI图表以显示完整内容
        function fitChartsToContent() {
            if (!mainChart) {
                log('主图表未初始化，跳过内容适配');
                return;
            }

            try {
                log('开始调整MCSI图表内容适配...');

                // 主图表适配内容
                mainChart.timeScale().fitContent();
                log('主图表内容适配完成');

                // 延迟同步MCSI图表，确保主图表适配完成
                setTimeout(() => {
                    const mainLogicalRange = mainChart.timeScale().getVisibleLogicalRange();
                    if (mainLogicalRange) {
                        if (mcsiMacdChart) mcsiMacdChart.timeScale().setVisibleLogicalRange(mainLogicalRange);
                        if (mcsiMmtChart) mcsiMmtChart.timeScale().setVisibleLogicalRange(mainLogicalRange);
                        if (mcsiRsiChart) mcsiRsiChart.timeScale().setVisibleLogicalRange(mainLogicalRange);
                        if (mcsiTtmChart) mcsiTtmChart.timeScale().setVisibleLogicalRange(mainLogicalRange);
                        if (authorityMacdChart) authorityMacdChart.timeScale().setVisibleLogicalRange(mainLogicalRange);
                        if (authorityMmtChart) authorityMmtChart.timeScale().setVisibleLogicalRange(mainLogicalRange);
                        if (authorityRsiChart) authorityRsiChart.timeScale().setVisibleLogicalRange(mainLogicalRange);
                        if (authorityTtmChart) authorityTtmChart.timeScale().setVisibleLogicalRange(mainLogicalRange);
                        log('所有9个图表垂直排列内容适配完成');
                    }
                }, 50);

            } catch (error) {
                log('MCSI图表内容适配失败: ' + error.message);
            }
        }

        // 数据量选择器事件
        document.getElementById('data-limit-select').addEventListener('change', function() {
            const newDataLimit = this.value;
            log('数据量已更改为: ' + newDataLimit + '天');

            if (currentStock) {
                // 显示加载状态
                updateStatus('mainStatus', `正在重新分析（${newDataLimit}天数据）...`);
                updateStatus('macdStatus', '重新计算中...');
                updateStatus('rsiStatus', '重新计算中...');
                updateStatus('mcsiMacdStatus', '重新计算中...');
                updateStatus('mcsiMmtStatus', '重新计算中...');
                updateStatus('mcsiRsiStatus', '重新计算中...');
                updateStatus('mcsiTtmStatus', '重新计算中...');

                // 重新加载数据（这会触发重新分析和图表重绘）
                loadStockData(currentStock.code);
            }
        });

        // 窗口大小调整 - 9个图表垂直排列（1主图+8个MCSI指标）
        window.addEventListener('resize', function() {
            if (mainChart) {
                mainChart.applyOptions({
                    width: document.getElementById('mainChart').clientWidth,
                    height: document.getElementById('mainChart').clientHeight
                });
            }
            // 统一接口版本
            if (mcsiMacdChart) {
                mcsiMacdChart.applyOptions({
                    width: document.getElementById('mcsiMacdChart').clientWidth,
                    height: document.getElementById('mcsiMacdChart').clientHeight
                });
            }
            if (mcsiMmtChart) {
                mcsiMmtChart.applyOptions({
                    width: document.getElementById('mcsiMmtChart').clientWidth,
                    height: document.getElementById('mcsiMmtChart').clientHeight
                });
            }
            if (mcsiRsiChart) {
                mcsiRsiChart.applyOptions({
                    width: document.getElementById('mcsiRsiChart').clientWidth,
                    height: document.getElementById('mcsiRsiChart').clientHeight
                });
            }
            if (mcsiTtmChart) {
                mcsiTtmChart.applyOptions({
                    width: document.getElementById('mcsiTtmChart').clientWidth,
                    height: document.getElementById('mcsiTtmChart').clientHeight
                });
            }
            // 权威版本
            if (authorityMacdChart) {
                authorityMacdChart.applyOptions({
                    width: document.getElementById('authorityMacdChart').clientWidth,
                    height: document.getElementById('authorityMacdChart').clientHeight
                });
            }
            if (authorityMmtChart) {
                authorityMmtChart.applyOptions({
                    width: document.getElementById('authorityMmtChart').clientWidth,
                    height: document.getElementById('authorityMmtChart').clientHeight
                });
            }
            if (authorityRsiChart) {
                authorityRsiChart.applyOptions({
                    width: document.getElementById('authorityRsiChart').clientWidth,
                    height: document.getElementById('authorityRsiChart').clientHeight
                });
            }
            if (authorityTtmChart) {
                authorityTtmChart.applyOptions({
                    width: document.getElementById('authorityTtmChart').clientWidth,
                    height: document.getElementById('authorityTtmChart').clientHeight
                });
            }
        });
    </script>
</body>
</html>
