<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业股票技术分析图表</title>
    <script src="https://cdn.jsdelivr.net/npm/lightweight-charts@4.1.1/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #131722;
            color: #d1d4dc;
            overflow: hidden;
        }
        
        .header {
            background: #1e222d;
            padding: 12px 20px;
            border-bottom: 1px solid #2a2e39;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }
        
        .stock-info h1 {
            margin: 0;
            font-size: 20px;
            color: #ffffff;
            font-weight: 600;
        }
        
        .stock-price {
            font-size: 18px;
            font-weight: bold;
            color: #26a69a;
            margin-right: 15px;
        }
        
        .refresh-btn {
            background: #2962ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        
        .refresh-btn:hover {
            background: #1e53e5;
        }
        
        .chart-container {
            height: calc(100vh - 60px);
            display: flex;
            flex-direction: column;
        }
        
        .main-chart {
            height: 60%;
            position: relative;
            border-bottom: 1px solid #2a2e39;
        }
        
        .sub-charts {
            height: 40%;
            display: flex;
            flex-direction: column;
        }
        
        .sub-chart {
            height: 50%;
            position: relative;
            border-bottom: 1px solid #2a2e39;
        }
        
        .sub-chart:last-child {
            border-bottom: none;
        }
        
        .chart-title {
            position: absolute;
            top: 8px;
            left: 12px;
            font-size: 12px;
            color: #787b86;
            z-index: 10;
            pointer-events: none;
            font-weight: 500;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #787b86;
            font-size: 14px;
        }
        
        .error {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ef5350;
            font-size: 14px;
            text-align: center;
        }
        
        .status-info {
            position: absolute;
            top: 8px;
            right: 12px;
            font-size: 11px;
            color: #787b86;
            z-index: 10;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="stock-info">
            <h1 id="stockTitle">加载中...</h1>
        </div>
        <div class="controls">
            <span class="stock-price" id="stockPrice">--</span>
            <select id="data-limit-select" class="form-select form-select-sm me-2" style="width: 120px; display: inline-block;">
                <option value="100">100天</option>
                <option value="250">250天</option>
                <option value="500" selected>500天</option>
                <option value="1000">1000天</option>
                <option value="2000">2000天</option>
            </select>
            <button class="refresh-btn" id="refreshBtn" onclick="refreshData()">
                刷新
            </button>
        </div>
    </div>
    
    <div class="chart-container">
        <div class="main-chart" id="mainChart">
            <div class="chart-title">K线图 & 移动平均线</div>
            <div class="status-info" id="mainStatus"></div>
            <div class="loading" id="mainLoading">正在加载数据...</div>
        </div>
        
        <div class="sub-charts">
            <div class="sub-chart" id="macdChart">
                <div class="chart-title">MACD</div>
                <div class="status-info" id="macdStatus"></div>
            </div>
            
            <div class="sub-chart" id="rsiChart">
                <div class="chart-title">RSI</div>
                <div class="status-info" id="rsiStatus"></div>
            </div>
        </div>
    </div>
    
    <script>
        let mainChart = null;
        let macdChart = null;
        let rsiChart = null;
        let currentStock = null;
        
        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }
        
        // 日志函数
        function log(message) {
            console.log('[Chart]', message);
        }
        
        // 显示错误
        function showError(message, containerId = 'mainChart') {
            const container = document.getElementById(containerId);
            const loading = container.querySelector('.loading');
            if (loading) loading.style.display = 'none';
            
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            container.appendChild(errorDiv);
        }
        
        // 更新状态信息
        function updateStatus(containerId, message) {
            const statusElement = document.getElementById(containerId);
            if (statusElement) {
                statusElement.textContent = message;
            }
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            const stockCode = getUrlParameter('stock') || '300584';
            const stockName = getUrlParameter('name') || '海辰药业';
            const dataLimit = getUrlParameter('limit') || '500';

            currentStock = { code: stockCode, name: stockName };

            // 设置数据量选择器的初始值
            const dataLimitSelect = document.getElementById('data-limit-select');
            if (dataLimitSelect) {
                dataLimitSelect.value = dataLimit;
            }

            log('页面加载完成');
            log('股票代码: ' + stockCode);
            log('股票名称: ' + stockName);
            log('数据量: ' + dataLimit);
            
            // 检查LightweightCharts
            if (typeof LightweightCharts === 'undefined') {
                showError('LightweightCharts库未加载');
                return;
            }
            
            log('LightweightCharts库正常');
            
            // 更新标题
            document.getElementById('stockTitle').textContent = `${stockName} (${stockCode})`;
            
            // 初始化图表
            initializeCharts();
            
            // 加载数据
            loadStockData(stockCode);
        });
        
        // 刷新数据
        function refreshData() {
            if (currentStock) {
                log('刷新数据...');
                loadStockData(currentStock.code);
            }
        }
        
        // 初始化图表
        function initializeCharts() {
            log('初始化图表...');

            // 重置时间轴同步标志
            timeAxisSyncSetup = false;

            try {
                // 主图表 - K线和移动平均线
                mainChart = LightweightCharts.createChart(document.getElementById('mainChart'), {
                    width: document.getElementById('mainChart').clientWidth,
                    height: document.getElementById('mainChart').clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        fixLeftEdge: true,
                        fixRightEdge: false, // 允许右侧扩展
                        rightOffset: 50, // 增加右侧留白空间
                        barSpacing: 6, // 设置K线间距
                        minBarSpacing: 0.5, // 最小K线间距
                        visible: false, // 隐藏主图表的时间轴
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                    },
                });
                
                log('主图表初始化完成');
                
            } catch (error) {
                log('图表初始化失败: ' + error.message);
                showError('图表初始化失败: ' + error.message);
            }
        }
        
        // 加载股票数据
        async function loadStockData(stockCode) {
            try {
                log('开始加载股票数据: ' + stockCode);
                updateStatus('mainStatus', '加载中...');

                // 获取数据量参数
                const dataLimit = document.getElementById('data-limit-select').value;
                const response = await fetch(`/api/stock_chart/${stockCode}?limit=${dataLimit}`);
                const data = await response.json();

                if (data.success) {
                    log('数据加载成功');
                    renderCharts(data);

                    // 更新价格显示
                    if (data.prices && data.prices.close && data.prices.close.length > 0) {
                        const latestPrice = data.prices.close[data.prices.close.length - 1];
                        document.getElementById('stockPrice').textContent = latestPrice.toFixed(2);
                    }
                } else {
                    log('数据加载失败: ' + (data.message || '未知错误'));
                    showError('数据加载失败: ' + (data.message || '未知错误'));
                }
            } catch (error) {
                log('加载失败: ' + error.message);
                showError('网络错误: ' + error.message);
            }
        }

        // 渲染图表
        function renderCharts(data) {
            try {
                log('开始渲染图表...');

                // 清理现有图表（确保完全重新初始化）
                if (mainChart) {
                    log('清理现有主图表');
                    mainChart.remove();
                    mainChart = null;
                }
                if (macdChart) {
                    log('清理现有MACD图表');
                    macdChart.remove();
                    macdChart = null;
                }
                if (rsiChart) {
                    log('清理现有RSI图表');
                    rsiChart.remove();
                    rsiChart = null;
                }

                // 重新初始化图表
                initializeCharts();

                // 渲染主图表
                renderMainChart(data);

                // 渲染MACD图表
                renderMACDChart(data);

                // 渲染RSI图表
                renderRSIChart(data);

                log('图表渲染完成');

                // 重新设置时间轴同步
                setTimeout(() => {
                    setupTimeAxisSync();
                    // 执行一次初始同步，确保所有图表显示相同范围
                    performInitialSync();
                }, 100);

            } catch (error) {
                log('图表渲染失败: ' + error.message);
                showError('图表渲染失败: ' + error.message);
            }
        }

        // 渲染主图表（K线 + 移动平均线）
        function renderMainChart(data) {
            if (!mainChart || !data.dates || !data.prices) {
                log('主图表数据不完整');
                return;
            }

            // 隐藏加载提示
            const loading = document.getElementById('mainLoading');
            if (loading) loading.style.display = 'none';

            // K线数据
            const candleData = data.dates.map((date, i) => ({
                time: date,
                open: data.prices.open[i],
                high: data.prices.high[i],
                low: data.prices.low[i],
                close: data.prices.close[i]
            }));

            const candleSeries = mainChart.addCandlestickSeries({
                upColor: '#26a69a',
                downColor: '#ef5350',
                borderVisible: false,
                wickUpColor: '#26a69a',
                wickDownColor: '#ef5350',
            });
            candleSeries.setData(candleData);
            log('K线数据设置完成: ' + candleData.length + ' 个数据点');

            // 移动平均线
            if (data.ma) {
                // MA5 - 蓝色
                if (data.ma.ma5) {
                    const ma5Data = data.dates.map((date, i) => ({
                        time: date,
                        value: data.ma.ma5[i]
                    })).filter(item => item.value !== null && item.value !== undefined && item.value > 0);

                    if (ma5Data.length > 0) {
                        const ma5Series = mainChart.addLineSeries({
                            color: '#2196f3',
                            lineWidth: 2,
                            title: 'MA5'
                        });
                        ma5Series.setData(ma5Data);
                        log('MA5数据点: ' + ma5Data.length);
                    }
                }

                // MA20 - 橙色
                if (data.ma.ma20) {
                    const ma20Data = data.dates.map((date, i) => ({
                        time: date,
                        value: data.ma.ma20[i]
                    })).filter(item => item.value !== null && item.value !== undefined && item.value > 0);

                    if (ma20Data.length > 0) {
                        const ma20Series = mainChart.addLineSeries({
                            color: '#ff9800',
                            lineWidth: 2,
                            title: 'MA20'
                        });
                        ma20Series.setData(ma20Data);
                        log('MA20数据点: ' + ma20Data.length);
                    }
                }

                // MA50 - 粉色
                if (data.ma.ma50) {
                    const ma50Data = data.dates.map((date, i) => ({
                        time: date,
                        value: data.ma.ma50[i]
                    })).filter(item => item.value !== null && item.value !== undefined && item.value > 0);

                    if (ma50Data.length > 0) {
                        const ma50Series = mainChart.addLineSeries({
                            color: '#e91e63',
                            lineWidth: 2,
                            title: 'MA50'
                        });
                        ma50Series.setData(ma50Data);
                        log('MA50数据点: ' + ma50Data.length);
                    }
                }

                // MA200 - 紫色
                if (data.ma.ma200) {
                    const ma200Data = data.dates.map((date, i) => ({
                        time: date,
                        value: data.ma.ma200[i]
                    })).filter(item => item.value !== null && item.value !== undefined && item.value > 0);

                    if (ma200Data.length > 0) {
                        const ma200Series = mainChart.addLineSeries({
                            color: '#9c27b0',
                            lineWidth: 2,
                            title: 'MA200'
                        });
                        ma200Series.setData(ma200Data);
                        log('MA200数据点: ' + ma200Data.length);
                    }
                }
            }

            updateStatus('mainStatus', `K线: ${candleData.length}点`);
        }

        // 渲染MACD图表
        function renderMACDChart(data) {
            if (!data.macd) {
                log('MACD数据不存在');
                updateStatus('macdStatus', '无数据');
                return;
            }

            try {
                // 初始化MACD图表
                macdChart = LightweightCharts.createChart(document.getElementById('macdChart'), {
                    width: document.getElementById('macdChart').clientWidth,
                    height: document.getElementById('macdChart').clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        fixLeftEdge: true,
                        fixRightEdge: false, // 允许右侧扩展
                        rightOffset: 50, // 增加右侧留白空间
                        barSpacing: 6, // 设置K线间距
                        minBarSpacing: 0.5, // 最小K线间距
                        visible: false, // 隐藏MACD图表的时间轴
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                    },
                    // MACD图表启用交互功能
                });

                // MACD线 - 蓝色
                if (data.macd.macd) {
                    const macdData = data.dates.map((date, i) => ({
                        time: date,
                        value: data.macd.macd[i]
                    })).filter(item => item.value !== null && item.value !== undefined);

                    if (macdData.length > 0) {
                        const macdSeries = macdChart.addLineSeries({
                            color: '#2196f3',
                            lineWidth: 2,
                            title: 'MACD'
                        });
                        macdSeries.setData(macdData);
                        log('MACD线数据点: ' + macdData.length);
                    }
                }

                // 信号线 - 橙色
                if (data.macd.signal) {
                    const signalData = data.dates.map((date, i) => ({
                        time: date,
                        value: data.macd.signal[i]
                    })).filter(item => item.value !== null && item.value !== undefined);

                    if (signalData.length > 0) {
                        const signalSeries = macdChart.addLineSeries({
                            color: '#ff9800',
                            lineWidth: 2,
                            title: 'Signal'
                        });
                        signalSeries.setData(signalData);
                        log('MACD信号线数据点: ' + signalData.length);
                    }
                }

                // MACD柱状图
                if (data.macd.histogram) {
                    const histogramData = data.dates.map((date, i) => ({
                        time: date,
                        value: data.macd.histogram[i],
                        color: data.macd.histogram[i] >= 0 ? '#26a69a' : '#ef5350'
                    })).filter(item => item.value !== null && item.value !== undefined);

                    if (histogramData.length > 0) {
                        const histogramSeries = macdChart.addHistogramSeries({
                            color: '#26a69a',
                            priceFormat: {
                                type: 'volume',
                            },
                            priceScaleId: '',
                            scaleMargins: {
                                top: 0.8,
                                bottom: 0,
                            },
                        });
                        histogramSeries.setData(histogramData);
                        log('MACD柱状图数据点: ' + histogramData.length);
                    }
                }

                updateStatus('macdStatus', 'MACD已加载');

            } catch (error) {
                log('MACD图表渲染失败: ' + error.message);
                updateStatus('macdStatus', '渲染失败');
            }
        }

        // 渲染RSI图表
        function renderRSIChart(data) {
            if (!data.rsi) {
                log('RSI数据不存在');
                updateStatus('rsiStatus', '无数据');
                return;
            }

            try {
                // 初始化RSI图表
                rsiChart = LightweightCharts.createChart(document.getElementById('rsiChart'), {
                    width: document.getElementById('rsiChart').clientWidth,
                    height: document.getElementById('rsiChart').clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        fixLeftEdge: true,
                        fixRightEdge: false, // 允许右侧扩展
                        rightOffset: 50, // 增加右侧留白空间
                        barSpacing: 6, // 设置K线间距
                        minBarSpacing: 0.5, // 最小K线间距
                        visible: true, // 只有RSI图表显示时间轴
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                        scaleMargins: {
                            top: 0.1,
                            bottom: 0.1,
                        },
                    },
                    // RSI图表启用交互功能
                });

                // RSI线 - 紫色
                const rsiData = data.dates.map((date, i) => ({
                    time: date,
                    value: data.rsi[i]
                })).filter(item => item.value !== null && item.value !== undefined);

                if (rsiData.length > 0) {
                    const rsiSeries = rsiChart.addLineSeries({
                        color: '#9c27b0',
                        lineWidth: 2,
                        title: 'RSI'
                    });
                    rsiSeries.setData(rsiData);
                    log('RSI数据点: ' + rsiData.length);
                }

                // 超买线 (70)
                const overboughtData = data.dates.map(date => ({
                    time: date,
                    value: 70
                }));
                const overboughtSeries = rsiChart.addLineSeries({
                    color: '#ef5350',
                    lineWidth: 1,
                    lineStyle: 2, // 虚线
                    title: '超买线(70)'
                });
                overboughtSeries.setData(overboughtData);

                // 超卖线 (30)
                const oversoldData = data.dates.map(date => ({
                    time: date,
                    value: 30
                }));
                const oversoldSeries = rsiChart.addLineSeries({
                    color: '#26a69a',
                    lineWidth: 1,
                    lineStyle: 2, // 虚线
                    title: '超卖线(30)'
                });
                oversoldSeries.setData(oversoldData);

                updateStatus('rsiStatus', 'RSI已加载');

                // 设置时间轴同步
                setupTimeAxisSync();

            } catch (error) {
                log('RSI图表渲染失败: ' + error.message);
                updateStatus('rsiStatus', '渲染失败');
            }
        }

        // 时间轴同步状态标志
        let timeAxisSyncSetup = false;

        // 设置时间轴同步
        function setupTimeAxisSync() {
            if (!mainChart || !macdChart || !rsiChart) {
                log('图表未完全初始化，跳过时间轴同步');
                return;
            }

            // 避免重复设置
            if (timeAxisSyncSetup) {
                log('时间轴同步已设置，跳过重复设置');
                return;
            }

            log('设置时间轴同步...');
            timeAxisSyncSetup = true;

            let isUpdating = false; // 防止循环更新
            let activeChart = null; // 记录当前活跃的图表

            // 统一同步函数 - 优化版本
            function syncChartsFromMaster(masterChart, chartName) {
                if (activeChart !== chartName) return;

                const masterTimeScale = masterChart.timeScale();
                const masterLogicalRange = masterTimeScale.getVisibleLogicalRange();

                if (masterLogicalRange) {
                    // 立即同步到其他图表，不使用防冲突机制以提高响应速度
                    if (masterChart !== mainChart) {
                        mainChart.timeScale().setVisibleLogicalRange(masterLogicalRange);
                    }
                    if (masterChart !== macdChart) {
                        macdChart.timeScale().setVisibleLogicalRange(masterLogicalRange);
                    }
                    if (masterChart !== rsiChart) {
                        rsiChart.timeScale().setVisibleLogicalRange(masterLogicalRange);
                    }
                    log(`${chartName}图表同步完成`);
                }
            }

            // 设置图表交互检测
            function setupChartInteractionDetection() {
                // 主图表交互检测
                const mainChartElement = document.getElementById('mainChart');
                mainChartElement.addEventListener('mousedown', () => {
                    activeChart = 'main';
                    log('主图表成为活跃图表');
                });
                mainChartElement.addEventListener('wheel', () => {
                    activeChart = 'main';
                    log('主图表缩放操作');
                });

                // MACD图表交互检测
                const macdChartElement = document.getElementById('macdChart');
                macdChartElement.addEventListener('mousedown', () => {
                    activeChart = 'macd';
                    log('MACD图表成为活跃图表');
                });
                macdChartElement.addEventListener('wheel', () => {
                    activeChart = 'macd';
                    log('MACD图表缩放操作');
                });

                // RSI图表交互检测
                const rsiChartElement = document.getElementById('rsiChart');
                rsiChartElement.addEventListener('mousedown', () => {
                    activeChart = 'rsi';
                    log('RSI图表成为活跃图表');
                });
                rsiChartElement.addEventListener('wheel', () => {
                    activeChart = 'rsi';
                    log('RSI图表缩放操作');
                });

                // 鼠标释放时清除活跃状态
                document.addEventListener('mouseup', () => {
                    setTimeout(() => {
                        activeChart = null;
                        log('清除活跃图表状态');
                    }, 100);
                });
            }

            // 调用交互检测设置
            setupChartInteractionDetection();

            // 主图表事件监听 - 优化版本
            mainChart.timeScale().subscribeVisibleTimeRangeChange(() => {
                syncChartsFromMaster(mainChart, 'main');
            });

            mainChart.timeScale().subscribeVisibleLogicalRangeChange(() => {
                syncChartsFromMaster(mainChart, 'main');
            });

            // MACD图表事件监听 - 优化版本
            macdChart.timeScale().subscribeVisibleTimeRangeChange(() => {
                syncChartsFromMaster(macdChart, 'macd');
            });

            macdChart.timeScale().subscribeVisibleLogicalRangeChange(() => {
                syncChartsFromMaster(macdChart, 'macd');
            });

            // RSI图表事件监听 - 优化版本
            rsiChart.timeScale().subscribeVisibleTimeRangeChange(() => {
                syncChartsFromMaster(rsiChart, 'rsi');
            });

            rsiChart.timeScale().subscribeVisibleLogicalRangeChange(() => {
                syncChartsFromMaster(rsiChart, 'rsi');
            });

            log('时间轴同步设置完成');
        }

        // 执行初始同步，确保所有图表显示相同的时间范围
        function performInitialSync() {
            if (!mainChart || !macdChart || !rsiChart) {
                log('图表未完全初始化，跳过初始同步');
                return;
            }

            try {
                // 检查数据长度一致性
                log('=== 数据长度检查 ===');
                log(`K线数据长度: ${window.currentData ? window.currentData.dates.length : 'N/A'}`);
                log(`MACD数据长度: ${window.currentData && window.currentData.macd ? window.currentData.macd.macd.filter(v => v !== null).length : 'N/A'}`);
                log(`RSI数据长度: ${window.currentData && window.currentData.rsi ? window.currentData.rsi.filter(v => v !== null).length : 'N/A'}`);

                // 获取主图表的可见范围
                const mainRange = mainChart.timeScale().getVisibleRange();
                const mainLogicalRange = mainChart.timeScale().getVisibleLogicalRange();

                log('=== 主图表范围信息 ===');
                log(`主图表时间范围: ${mainRange ? `${mainRange.from} - ${mainRange.to}` : 'N/A'}`);
                log(`主图表逻辑范围: ${mainLogicalRange ? `${mainLogicalRange.from} - ${mainLogicalRange.to}` : 'N/A'}`);

                if (mainLogicalRange) {
                    // 使用逻辑范围同步，保持留白
                    macdChart.timeScale().setVisibleLogicalRange(mainLogicalRange);
                    rsiChart.timeScale().setVisibleLogicalRange(mainLogicalRange);
                    log(`执行初始逻辑范围同步: ${mainLogicalRange.from} - ${mainLogicalRange.to}`);
                } else {
                    // 如果主图表没有逻辑范围，尝试自动调整
                    log('主图表没有逻辑范围，尝试自动调整');
                    mainChart.timeScale().fitContent();
                    setTimeout(() => {
                        const newLogicalRange = mainChart.timeScale().getVisibleLogicalRange();
                        if (newLogicalRange) {
                            // 使用逻辑范围同步，保持留白
                            macdChart.timeScale().setVisibleLogicalRange(newLogicalRange);
                            rsiChart.timeScale().setVisibleLogicalRange(newLogicalRange);
                            log(`自动调整后逻辑范围同步: ${newLogicalRange.from} - ${newLogicalRange.to}`);
                        }
                    }, 50);
                }
            } catch (error) {
                log('初始同步失败: ' + error.message);
            }
        }

        // 数据量选择器事件
        document.getElementById('data-limit-select').addEventListener('change', function() {
            const newDataLimit = this.value;
            log('数据量已更改为: ' + newDataLimit + '天');

            if (currentStock) {
                // 显示加载状态
                updateStatus('mainStatus', `正在重新分析（${newDataLimit}天数据）...`);
                updateStatus('macdStatus', '重新计算中...');
                updateStatus('rsiStatus', '重新计算中...');

                // 重新加载数据（这会触发重新分析和图表重绘）
                loadStockData(currentStock.code);
            }
        });

        // 窗口大小调整
        window.addEventListener('resize', function() {
            if (mainChart) {
                mainChart.applyOptions({
                    width: document.getElementById('mainChart').clientWidth,
                    height: document.getElementById('mainChart').clientHeight
                });
            }
            if (macdChart) {
                macdChart.applyOptions({
                    width: document.getElementById('macdChart').clientWidth,
                    height: document.getElementById('macdChart').clientHeight
                });
            }
            if (rsiChart) {
                rsiChart.applyOptions({
                    width: document.getElementById('rsiChart').clientWidth,
                    height: document.getElementById('rsiChart').clientHeight
                });
            }
        });
    </script>
</body>
</html>
