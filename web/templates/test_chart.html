<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图表测试页面</title>
    <script src="https://cdn.jsdelivr.net/npm/lightweight-charts@4.1.1/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #131722;
            color: #d1d4dc;
        }
        .test-container {
            margin-bottom: 20px;
        }
        .chart-container {
            width: 100%;
            height: 400px;
            border: 1px solid #2a2e39;
            margin-bottom: 20px;
        }
        .log-container {
            background: #1e222d;
            padding: 10px;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background: #2962ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #1e4db7;
        }
    </style>
</head>
<body>
    <h1>图表数据测试</h1>
    
    <div class="test-container">
        <button onclick="testAPI()">测试API数据</button>
        <button onclick="loadChart()">加载图表</button>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div class="chart-container" id="chartContainer">
        <div style="text-align: center; padding-top: 180px;">点击"加载图表"开始测试</div>
    </div>
    
    <div class="log-container" id="logContainer">
        <div>日志输出将显示在这里...</div>
    </div>

    <script>
        let chart = null;
        
        function log(message) {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            logContainer.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log('[Test]', message);
        }
        
        function clearLog() {
            document.getElementById('logContainer').innerHTML = '<div>日志已清空...</div>';
        }
        
        async function testAPI() {
            log('开始测试API...');
            
            try {
                const stockCode = 'other_801080_si_申万电子';
                const response = await fetch(`/api/stock_chart/${stockCode}`);
                log(`API响应状态: ${response.status}`);
                
                const data = await response.json();
                log(`API返回成功: ${data.success}`);
                
                if (data.success) {
                    log(`数据点数量: ${data.data_points}`);
                    log(`日期数量: ${data.dates.length}`);
                    log(`价格数据: ${data.prices ? '✓' : '✗'}`);
                    log(`MA数据: ${data.ma ? '✓' : '✗'}`);
                    log(`MACD数据: ${data.macd ? '✓' : '✗'}`);
                    log(`RSI数据: ${data.rsi ? '✓' : '✗'}`);
                    
                    if (data.rsi) {
                        const rsiNon50 = data.rsi.filter(x => x !== 50.0).length;
                        log(`RSI非50值数量: ${rsiNon50}/${data.rsi.length}`);
                        log(`RSI样本值: [${data.rsi.slice(13, 18).map(x => x.toFixed(2)).join(', ')}]`);
                    }
                    
                    if (data.macd) {
                        log(`MACD样本值: [${data.macd.macd.slice(-5).map(x => x.toFixed(2)).join(', ')}]`);
                        log(`Signal样本值: [${data.macd.signal.slice(-5).map(x => x.toFixed(2)).join(', ')}]`);
                    }
                    
                    // 保存数据供图表使用
                    window.testData = data;
                    log('API测试完成，数据已保存');
                } else {
                    log(`API错误: ${data.message || '未知错误'}`);
                }
            } catch (error) {
                log(`API测试失败: ${error.message}`);
            }
        }
        
        async function loadChart() {
            if (!window.testData) {
                log('请先测试API获取数据');
                return;
            }
            
            log('开始创建图表...');
            
            try {
                const container = document.getElementById('chartContainer');
                container.innerHTML = '';
                
                // 创建图表
                chart = LightweightCharts.createChart(container, {
                    width: container.clientWidth,
                    height: container.clientHeight,
                    layout: {
                        background: { color: '#131722' },
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2a2e39' },
                        horzLines: { color: '#2a2e39' },
                    },
                    crosshair: {
                        mode: LightweightCharts.CrosshairMode.Normal,
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                    },
                    timeScale: {
                        borderColor: '#2a2e39',
                        timeVisible: true,
                        secondsVisible: false,
                    },
                });
                
                const data = window.testData;
                
                // 创建K线数据
                const candlestickData = data.dates.map((date, index) => ({
                    time: date,
                    open: parseFloat(data.prices.open[index]),
                    high: parseFloat(data.prices.high[index]),
                    low: parseFloat(data.prices.low[index]),
                    close: parseFloat(data.prices.close[index])
                }));
                
                const candlestickSeries = chart.addCandlestickSeries({
                    upColor: '#26a69a',
                    downColor: '#ef5350',
                    borderVisible: false,
                    wickUpColor: '#26a69a',
                    wickDownColor: '#ef5350',
                });
                candlestickSeries.setData(candlestickData);
                log(`K线数据已添加: ${candlestickData.length} 个数据点`);
                
                // 创建RSI线
                const rsiData = data.dates.map((date, index) => ({
                    time: date,
                    value: parseFloat(data.rsi[index]) || 50
                }));
                
                const rsiSeries = chart.addLineSeries({
                    color: '#9c27b0',
                    lineWidth: 2,
                    priceScaleId: 'rsi',
                });
                rsiSeries.setData(rsiData);
                log(`RSI数据已添加: ${rsiData.length} 个数据点`);
                
                // 创建MACD线
                const macdData = data.dates.map((date, index) => ({
                    time: date,
                    value: parseFloat(data.macd.macd[index]) || 0
                }));
                
                const macdSeries = chart.addLineSeries({
                    color: '#2962ff',
                    lineWidth: 2,
                    priceScaleId: 'macd',
                });
                macdSeries.setData(macdData);
                log(`MACD数据已添加: ${macdData.length} 个数据点`);
                
                log('图表创建完成！');
                
            } catch (error) {
                log(`图表创建失败: ${error.message}`);
                console.error('详细错误:', error);
            }
        }
        
        // 窗口大小调整
        window.addEventListener('resize', () => {
            if (chart) {
                const container = document.getElementById('chartContainer');
                chart.applyOptions({
                    width: container.clientWidth,
                    height: container.clientHeight,
                });
            }
        });
    </script>
</body>
</html>
