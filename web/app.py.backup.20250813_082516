#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新的Web应用
基于新架构的Web界面，支持分组配置和权重调整
"""

from flask import Flask, render_template, jsonify, request, send_from_directory
from flask_cors import CORS
import json
import logging
from datetime import datetime
import pandas as pd
import numpy as np
from core.composite.scorer import NewCompositeScorer
from core.data.data_loader import DataLoader
from core.data.csv_data_loader import CSVDataLoader
from core.data.hybrid_data_loader import HybridDataLoader
from core.indicators import MCSIMACDIndicator, MCSIMMTIndicator, MCSIRSIIndicator, MCSITD9Indicator
from core.config.market_config import market_detector, get_market_aggregator, analyze_market_data
from config.settings import settings

# 尝试导入高级MCSI单元
try:
    from core.scoring_units.mcsi_premium_units import (
        MCSIPremiumRSIUnit, MCSIPremiumMACDUnit, MCSIPremiumMMTUnit, MCSIPremiumTTMUnit,
        MCSI_PREMIUM_AVAILABLE
    )
    if MCSI_PREMIUM_AVAILABLE:
        logging.info("✅ 高级MCSI单元已导入到Web服务")
        mcsi_rsi_unit = MCSIPremiumRSIUnit()
        mcsi_macd_unit = MCSIPremiumMACDUnit() 
        mcsi_mmt_unit = MCSIPremiumMMTUnit()
        mcsi_ttm_unit = MCSIPremiumTTMUnit()
    else:
        logging.warning("⚠️ 高级MCSI单元不可用，使用基础指标")
        mcsi_rsi_unit = None
        mcsi_macd_unit = None
        mcsi_mmt_unit = None
        mcsi_ttm_unit = None
except ImportError as e:
    logging.warning(f"⚠️ 无法导入高级MCSI单元: {e}")
    MCSI_PREMIUM_AVAILABLE = False
    mcsi_rsi_unit = None
    mcsi_macd_unit = None
    mcsi_mmt_unit = None
    mcsi_ttm_unit = None

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s - %(message)s'
)

app = Flask(__name__, template_folder='templates')
CORS(app)

# 全局变量
scorer = None
hybrid_data_loader = None  # 混合数据加载器（优先数据库，备用CSV）
analysis_status = {
    'is_running': False,
    'progress': 0,
    'total': 0,
    'current_stock': '',
    'message': '就绪'
}

def init_scorer():
    """初始化评分器和混合数据加载器"""
    global scorer, hybrid_data_loader
    try:
        scorer = NewCompositeScorer()
        hybrid_data_loader = HybridDataLoader(prefer_database=True)
        # 设置评分器使用混合数据加载器
        scorer.set_hybrid_data_loader(hybrid_data_loader)

        # 记录数据源状态
        status = hybrid_data_loader.get_data_source_status()
        logging.info(f"混合数据加载器初始化成功，可用数据源: {status['active_sources']}")
        logging.info(f"数据库可用: {status['database_available']}, CSV可用: {status['csv_available']}")
        return True
    except Exception as e:
        logging.error(f"初始化失败: {str(e)}")
        return False

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/professional_chart.html')
def professional_chart():
    """专业图表页面"""
    return render_template('professional_chart.html')

@app.route('/simple_debug.html')
def simple_debug():
    """简单调试页面"""
    return render_template('simple_debug.html')

@app.route('/test_chart.html')
def test_chart():
    """图表测试页面"""
    return render_template('test_chart.html')

@app.route('/debug_chart.html')
def debug_chart():
    """图表调试页面"""
    return render_template('debug_chart.html')

@app.route('/sync_test.html')
def sync_test():
    """时间轴同步测试页面"""
    return render_template('sync_test.html')

@app.route('/old_system_chart.html')
def old_system_chart():
    """旧系统图表页面"""
    return render_template('old_system_chart.html')

@app.route('/test_results.html')
def test_results():
    """测试结果页面"""
    return send_from_directory('.', 'test_results.html')

@app.route('/debug_results.html')
def debug_results():
    """调试结果页面"""
    return send_from_directory('.', 'debug_results.html')

@app.route('/api/groups')
def get_groups():
    """获取所有分组信息"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500
        
        groups = scorer.list_groups()
        weights = scorer.get_group_weights()
        
        return jsonify({
            'groups': groups,
            'weights': weights,
            'status': 'success'
        })
    except Exception as e:
        logging.error(f"获取分组信息失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/groups/weights', methods=['POST'])
def update_group_weights():
    """更新分组权重"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500
        
        data = request.get_json()
        weights = data.get('weights', {})
        
        scorer.update_group_weights(weights)
        scorer.save_config()
        
        return jsonify({
            'message': '分组权重更新成功',
            'weights': scorer.get_group_weights(),
            'status': 'success'
        })
    except Exception as e:
        logging.error(f"更新分组权重失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/groups/create', methods=['POST'])
def create_custom_group():
    """创建自定义分组"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500
        
        data = request.get_json()
        group_id = data.get('group_id')
        name = data.get('name')
        description = data.get('description', '')
        weight = data.get('weight', 1.0)
        custom_config = data.get('config', {})
        
        if not group_id or not name:
            return jsonify({'error': '分组ID和名称不能为空'}), 400
        
        success = scorer.create_custom_group(
            group_id, name, description, weight, custom_config
        )
        
        if success:
            scorer.save_config()
            return jsonify({
                'message': f'自定义分组 {name} 创建成功',
                'group_id': group_id,
                'status': 'success'
            })
        else:
            return jsonify({'error': '创建自定义分组失败'}), 500
            
    except Exception as e:
        logging.error(f"创建自定义分组失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/groups/<group_id>', methods=['DELETE'])
def remove_group(group_id):
    """删除分组"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500
        
        success = scorer.remove_group(group_id)
        
        if success:
            scorer.save_config()
            return jsonify({
                'message': f'分组 {group_id} 删除成功',
                'status': 'success'
            })
        else:
            return jsonify({'error': '删除分组失败'}), 500
            
    except Exception as e:
        logging.error(f"删除分组失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/scoring_units')
def get_scoring_units():
    """获取可用的计分单元类型"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500
        
        units = scorer.get_available_scoring_units()
        
        return jsonify({
            'scoring_units': units,
            'status': 'success'
        })
    except Exception as e:
        logging.error(f"获取计分单元失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/analyze', methods=['POST'])
def start_analysis():
    """开始分析"""
    global analysis_status

    try:
        # 确保评分器已初始化
        global scorer
        if not scorer:
            logging.info("评分器未初始化，尝试重新初始化...")
            if not init_scorer():
                return jsonify({'error': '评分器初始化失败'}), 500

        if analysis_status['is_running']:
            return jsonify({'error': '分析正在进行中'}), 400

        data = request.get_json() or {}

        # 获取分析参数
        data_limit = data.get('data_limit', 500)
        force_refresh = data.get('force_refresh', True)

        logging.info(f"开始分析 - 数据量: {data_limit}天, 强制刷新: {force_refresh}")

        # 获取股票列表
        stock_list = data.get('stocks', [])

        if not stock_list:
            # 使用混合数据加载器获取股票列表
            global hybrid_data_loader
            if not hybrid_data_loader:
                hybrid_data_loader = HybridDataLoader(prefer_database=True)

            all_stocks = hybrid_data_loader.get_stock_list()
            # 改进过滤逻辑：只过滤掉中国指数，保留其他所有类型
            stock_files = [stock_info for stock_info in all_stocks if stock_info['category'] != '中国指数']

            # 验证数据有效性，过滤掉空表
            logging.info(f"开始验证 {len(stock_files)} 只股票的数据有效性...")
            valid_stock_files = []

            for i, stock_info in enumerate(stock_files):
                table_name = stock_info['table_name']

                # 使用混合数据加载器验证数据
                try:
                    test_data = hybrid_data_loader.get_stock_data(table_name, limit=1)
                    if test_data is not None and not test_data.empty:
                        valid_stock_files.append(stock_info)
                    else:
                        logging.debug(f"过滤空表: {stock_info['symbol']} - {stock_info['name']}")
                except Exception as e:
                    logging.debug(f"过滤错误表: {stock_info['symbol']} - {stock_info['name']}: {str(e)}")

                # 每10个股票输出一次进度
                if (i + 1) % 10 == 0:
                    logging.info(f"已验证 {i + 1}/{len(stock_files)} 只股票，有效: {len(valid_stock_files)} 只")

            logging.info(f"数据验证完成：{len(stock_files)} 只股票中有 {len(valid_stock_files)} 只有效")

            # 使用验证后的股票列表
            stock_list = [{'code': stock_info['symbol'], 'name': stock_info['name'], 'table_name': stock_info['table_name']} for stock_info in valid_stock_files]

        if not stock_list:
            return jsonify({'error': '没有找到可分析的股票'}), 400

        # 开始分析前清空旧结果
        scorer.clear_history()

        # 开始分析
        analysis_status['is_running'] = True
        analysis_status['total'] = len(stock_list)
        analysis_status['progress'] = 0
        analysis_status['message'] = f'开始分析 {len(stock_list)} 只股票，数据量: {data_limit}天...'

        # 批量评分（传递数据量参数）- 添加进度更新
        results = []
        for i, stock_info in enumerate(stock_list):
            stock_code = stock_info.get('code', '')
            stock_name = stock_info.get('name', '')
            table_name = stock_info.get('table_name', '')

            if stock_code and table_name:
                # 更新进度
                analysis_status['progress'] = i
                analysis_status['current_stock'] = stock_code
                analysis_status['message'] = f'正在分析 {stock_code} ({i+1}/{len(stock_list)})'

                # 使用table_name进行评分，确保与验证阶段使用相同的查找逻辑
                result = scorer.score_stock(table_name, stock_name, data_limit=data_limit)
                # 但在结果中保留原始的stock_code用于显示
                if result:
                    result.stock_code = stock_code  # 覆盖为显示用的代码
                results.append(result)

        # 更新状态
        analysis_status['is_running'] = False
        analysis_status['progress'] = analysis_status['total']
        analysis_status['message'] = f'分析完成，共处理 {len(results)} 只股票 (数据量: {data_limit}天)'
        
        return jsonify({
            'message': '分析完成',
            'results_count': len(results),
            'status': 'success'
        })
        
    except Exception as e:
        analysis_status['is_running'] = False
        analysis_status['message'] = f'分析失败: {str(e)}'
        logging.error(f"分析失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/results')
def get_results():
    """获取分析结果"""
    try:
        # 确保评分器已初始化
        global scorer
        if not scorer:
            logging.info("评分器未初始化，尝试重新初始化...")
            if not init_scorer():
                return jsonify({'error': '评分器初始化失败'}), 500
        
        results = scorer.get_latest_results(100)  # 获取最新100个结果

        # 获取混合数据加载器来解析分类信息
        global hybrid_data_loader
        if not hybrid_data_loader:
            hybrid_data_loader = HybridDataLoader(prefer_database=True)

        # 转换为字典格式并添加分组分数
        results_data = []
        for result in results:
            result_dict = result.to_dict()

            # 提取分组分数
            group_results = result_dict.get('group_results', {})
            trend_score = 0.0
            oscillation_score = 0.0

            if 'trend_group' in group_results:
                trend_score = group_results['trend_group'].get('weighted_score', 0.0)

            if 'oscillation_group' in group_results:
                oscillation_score = group_results['oscillation_group'].get('weighted_score', 0.0)

            # 添加分组分数到结果中
            result_dict['trend_score'] = trend_score
            result_dict['oscillation_score'] = oscillation_score

            # 获取股票分类信息 - 使用混合数据加载器
            stock_code = result_dict['stock_code']
            all_stocks = hybrid_data_loader.get_stock_list()
            category = '其他'

            # 查找对应的股票信息
            for stock_info in all_stocks:
                if stock_info['symbol'] == stock_code or stock_info['table_name'] == stock_code:
                    category = stock_info['category']
                    break

            # 添加旧系统兼容字段
            result_dict['symbol'] = result_dict['stock_code']  # 旧系统期望symbol字段
            result_dict['name'] = result_dict['stock_name']    # 旧系统期望name字段
            result_dict['category'] = category  # 添加正确的分类字段
            result_dict['trend_grade'] = result_dict['grade']  # 旧系统期望trend_grade字段
            result_dict['rsi_score'] = oscillation_score  # RSI分数映射到震荡分数
            result_dict['rsi_value'] = 50.0  # 默认RSI值
            result_dict['macd_score'] = trend_score  # MACD分数映射到趋势分数
            result_dict['macd_value'] = 0.0  # 默认MACD值
            result_dict['data_count'] = 250  # 默认数据点数量

            results_data.append(result_dict)

        # 按分数排序
        results_data.sort(key=lambda x: x['composite_score'], reverse=True)
        
        # 兼容旧系统的API格式
        return jsonify({
            'success': True,
            'data': {
                'items': results_data,  # 旧系统期望的是items字段
                'total': len(results_data),
                'page': 1,
                'size': len(results_data),
                'pages': 1,
                'pagination': {
                    'page': 1,
                    'pages': 1,
                    'per_page': len(results_data),
                    'total': len(results_data)
                }
            }
        })
        
    except Exception as e:
        logging.error(f"获取结果失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/status')
def get_status():
    """获取分析状态"""
    return jsonify(analysis_status)

@app.route('/api/stock_chart/<path:stock_code>')
def get_stock_chart_data(stock_code):
    """获取股票图表数据 - 完全兼容旧系统格式"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500

        # URL解码股票代码
        from urllib.parse import unquote
        stock_code = unquote(stock_code)
        logging.info(f"请求股票图表数据: {stock_code}")

        # 获取limit参数，默认500
        limit = request.args.get('limit', 500, type=int)

        # 获取股票数据 - 使用混合数据加载器
        global hybrid_data_loader
        if not hybrid_data_loader:
            hybrid_data_loader = HybridDataLoader(prefer_database=True)

        # 首先获取所有可用的股票
        all_stocks = hybrid_data_loader.get_stock_list()

        # 查找匹配的股票 - 支持多种匹配方式
        stock_info = None
        for info in all_stocks:
            # 1. 精确匹配symbol
            if info['symbol'] == stock_code:
                stock_info = info
                break
            # 2. 精确匹配table_name
            elif info['table_name'] == stock_code:
                stock_info = info
                break

        if not stock_info:
            # 如果还没找到，尝试直接使用stock_code查找
            if hybrid_data_loader.check_stock_exists(stock_code):
                logging.info(f"直接使用 {stock_code} 查找股票数据")
                stock_data = hybrid_data_loader.get_stock_data(stock_code, limit=limit)
            else:
                logging.warning(f"股票 {stock_code} 未找到，可用股票: {[s['symbol'] for s in all_stocks[:5]]}")
                logging.warning(f"可用表名: {[s['table_name'] for s in all_stocks[:5]]}")
                return jsonify({'error': f'股票 {stock_code} 未找到'}), 404
        else:
            # 使用找到的股票信息获取数据
            stock_data = hybrid_data_loader.get_stock_data(stock_info['table_name'], limit=limit)

        if stock_data is None or stock_data.empty:
            return jsonify({'error': '股票数据不存在'}), 404

        # 使用新系统的计分单元计算指标
        from core.scoring_units.macd_scoring_unit import MACDCalculator
        from core.scoring_units.rsi_scoring_unit import RSICalculator

        # 计算技术指标
        macd_series = MACDCalculator.calculate_macd(stock_data['close'])
        rsi_series = RSICalculator.calculate_rsi(stock_data['close'])

        # 尝试使用高级MCSI单元生成图表数据
        if MCSI_PREMIUM_AVAILABLE and mcsi_macd_unit and mcsi_mmt_unit:
            try:
                # 使用高级MCSI单元
                mcsi_macd_result = mcsi_macd_unit.score_with_validation(stock_data)
                mcsi_mmt_result = mcsi_mmt_unit.score_with_validation(stock_data)
                
                # 从结果中提取指标数据
                mcsi_macd_data = mcsi_macd_result.metadata.get('indicator_data', {})
                mcsi_mmt_data = mcsi_mmt_result.metadata.get('indicator_data', {})
                
                logging.info("✅ 使用高级MCSI计分单元生成图表数据")
            except Exception as e:
                logging.warning(f"⚠️ 高级MCSI单元失败，回退到基础指标: {e}")
                # 回退到基础指标
                mcsi_macd_indicator = MCSIMACDIndicator()
                mcsi_mmt_indicator = MCSIMMTIndicator()
                
                mcsi_macd_data = mcsi_macd_indicator.calculate(stock_data['close'].values)
                mcsi_mmt_data = mcsi_mmt_indicator.calculate(
                    stock_data['close'].values,
                    stock_data['high'].values,
                    stock_data['low'].values
                )
        else:
            # 使用基础MCSI指标
            mcsi_macd_indicator = MCSIMACDIndicator()
            mcsi_mmt_indicator = MCSIMMTIndicator()
            
            mcsi_macd_data = mcsi_macd_indicator.calculate(stock_data['close'].values)
            mcsi_mmt_data = mcsi_mmt_indicator.calculate(
                stock_data['close'].values,
                stock_data['high'].values,
                stock_data['low'].values
            )
            logging.info("✅ 使用基础MCSI指标生成图表数据")
        # 准备完整的数据字典给RSI指标
        try:
            # 检查是否有timestamp或date列，优先使用实际日期数据
            if 'timestamp' in stock_data.columns:
                # 数据库数据，使用timestamp列
                dates = stock_data['timestamp'].dt.strftime('%Y-%m-%d').tolist()
            elif 'date' in stock_data.columns:
                # CSV数据，使用date列  
                dates = stock_data['date'].tolist()
            else:
                # 创建虚拟日期序列（回退方案）
                dates = [f"2024-01-{i+1:02d}" for i in range(len(stock_data))]

            # 暂时跳过市场检测以修复图表显示问题
            # TODO: 修复analyze_market_data函数中的时间差转换问题
            # market_analysis = analyze_market_data(dates, stock_code)
            # market_type = market_analysis['recommended_type']
            market_type = 'stock'  # 默认使用股票类型

            app.logger.info(f"图表数据生成: {stock_code} (类型: {market_type})")

            full_data = {
                'date': dates,
                'open': stock_data['open'].values,
                'high': stock_data['high'].values,
                'low': stock_data['low'].values,
                'close': stock_data['close'].values,
                'volume': stock_data.get('volume', [1] * len(stock_data)).values if 'volume' in stock_data.columns else [1] * len(stock_data),
                'market_type': market_type  # 添加市场类型信息
            }
        except Exception as e:
            app.logger.warning(f"数据驱动检测失败，使用简化模式: {e}")
            app.logger.warning(f"  - 异常类型: {type(e).__name__}")
            app.logger.warning(f"  - 股票代码: {stock_code}")
            app.logger.warning(f"  - 数据列: {list(stock_data.columns) if hasattr(stock_data, 'columns') else 'N/A'}")
            app.logger.warning(f"  - 数据形状: {stock_data.shape if hasattr(stock_data, 'shape') else 'N/A'}")
            # 回退到原来的方式
            full_data = stock_data['close'].values

        # 继续使用高级MCSI单元处理RSI和TTM
        if MCSI_PREMIUM_AVAILABLE and mcsi_rsi_unit and mcsi_ttm_unit:
            try:
                # 使用高级RSI和TTM单元
                mcsi_rsi_result = mcsi_rsi_unit.score_with_validation(stock_data)
                mcsi_ttm_result = mcsi_ttm_unit.score_with_validation(stock_data)
                
                # 处理RSI和TTM数据
                mcsi_rsi_metadata = mcsi_rsi_result.metadata
                mcsi_ttm_metadata = mcsi_ttm_result.metadata
                
                # 构建RSI数据结构
                if 'indicator_data' in mcsi_rsi_metadata:
                    mcsi_rsi_data = mcsi_rsi_metadata['indicator_data']
                else:
                    mcsi_rsi_data = {
                        'crsi_daily': mcsi_rsi_metadata.get('crsi_daily'),
                        'db_daily': mcsi_rsi_metadata.get('db_daily'),
                        'ub_daily': mcsi_rsi_metadata.get('ub_daily'),
                        'crsi_weekly': mcsi_rsi_metadata.get('crsi_weekly'),
                        'db_weekly': mcsi_rsi_metadata.get('db_weekly'),
                        'ub_weekly': mcsi_rsi_metadata.get('ub_weekly'),
                        'daily_signals': mcsi_rsi_metadata.get('daily_signals', []),
                        'weekly_signals': mcsi_rsi_metadata.get('weekly_signals', []),
                        'rsi_score': mcsi_rsi_metadata.get('rsi_score'),
                        'background_colors': mcsi_rsi_metadata.get('background_colors', [])
                    }
                
                # 构建TTM数据结构
                if 'indicator_data' in mcsi_ttm_metadata:
                    mcsi_ttm_data = mcsi_ttm_metadata['indicator_data']
                else:
                    mcsi_ttm_data = {
                        'ttm_score': mcsi_ttm_metadata.get('ttm_score'),
                        'background_colors': mcsi_ttm_metadata.get('background_colors', []),
                        'up_shapes': mcsi_ttm_metadata.get('up_shapes'),
                        'down_shapes': mcsi_ttm_metadata.get('down_shapes'),
                        # 兼容性字段
                        'td_up_count': mcsi_ttm_metadata.get('td_up_count', []),
                        'td_down_count': mcsi_ttm_metadata.get('td_down_count', []),
                        'td9_score': mcsi_ttm_metadata.get('ttm_score')  # 使用ttm_score作为td9_score
                    }
                
                logging.info("✅ 使用高级MCSI RSI和TTM单元生成图表数据")
            except Exception as e:
                logging.warning(f"⚠️ 高级RSI/TTM单元失败，回退到基础指标: {e}")
                # 回退到基础指标
                mcsi_rsi_indicator = MCSIRSIIndicator()
                mcsi_td9_indicator = MCSITD9Indicator()
                
                mcsi_rsi_data = mcsi_rsi_indicator.calculate(full_data)
                mcsi_ttm_data = mcsi_td9_indicator.calculate(stock_data['close'].values)  # 暂时用TD9作为TTM备份
        else:
            # 回退到基础指标
            mcsi_rsi_indicator = MCSIRSIIndicator()
            mcsi_td9_indicator = MCSITD9Indicator()
            
            mcsi_rsi_data = mcsi_rsi_indicator.calculate(full_data)
            mcsi_ttm_data = mcsi_td9_indicator.calculate(stock_data['close'].values)  # 暂时用TD9作为TTM备份
            logging.info("✅ 使用基础MCSI RSI和TD9指标生成图表数据")

        # 计算移动平均线
        ma5 = stock_data['close'].rolling(window=5).mean()
        ma20 = stock_data['close'].rolling(window=20).mean()
        ma50 = stock_data['close'].rolling(window=50).mean()
        ma200 = stock_data['close'].rolling(window=200).mean()

        # 准备旧系统格式的数据
        dates = []
        prices = {
            'open': [],
            'high': [],
            'low': [],
            'close': []
        }
        ma = {
            'ma5': [],
            'ma20': [],
            'ma50': [],
            'ma200': []
        }
        macd = {
            'macd': [],
            'signal': [],
            'histogram': []
        }
        rsi = []

        # 填充数据
        for i in range(len(stock_data)):
            # 日期 - 智能处理时间列（支持timestamp或date列）
            if 'date' in stock_data.columns:
                real_timestamp = stock_data.iloc[i]['date']
            elif 'timestamp' in stock_data.columns:
                real_timestamp = stock_data.iloc[i]['timestamp']
            else:
                # 如果没有时间列，使用索引生成虚拟日期
                real_timestamp = f"2024-01-{i+1:02d}"

            if isinstance(real_timestamp, str):
                # 如果是字符串，转换为datetime
                real_timestamp = pd.to_datetime(real_timestamp)

            if hasattr(real_timestamp, 'strftime'):
                dates.append(real_timestamp.strftime('%Y-%m-%d'))
            else:
                dates.append(str(real_timestamp))

            # 价格数据
            prices['open'].append(float(stock_data.iloc[i]['open']))
            prices['high'].append(float(stock_data.iloc[i]['high']))
            prices['low'].append(float(stock_data.iloc[i]['low']))
            prices['close'].append(float(stock_data.iloc[i]['close']))

            # MA数据
            ma['ma5'].append(float(ma5.iloc[i]) if not pd.isna(ma5.iloc[i]) else 0)
            ma['ma20'].append(float(ma20.iloc[i]) if not pd.isna(ma20.iloc[i]) else 0)
            ma['ma50'].append(float(ma50.iloc[i]) if not pd.isna(ma50.iloc[i]) else 0)
            ma['ma200'].append(float(ma200.iloc[i]) if not pd.isna(ma200.iloc[i]) else 0)

            # MACD数据
            if i < len(macd_series['macd']):
                macd['macd'].append(float(macd_series['macd'].iloc[i]) if not pd.isna(macd_series['macd'].iloc[i]) else 0)
                macd['signal'].append(float(macd_series['signal'].iloc[i]) if not pd.isna(macd_series['signal'].iloc[i]) else 0)
                macd['histogram'].append(float(macd_series['histogram'].iloc[i]) if not pd.isna(macd_series['histogram'].iloc[i]) else 0)
            else:
                macd['macd'].append(0)
                macd['signal'].append(0)
                macd['histogram'].append(0)

            # RSI数据
            if i < len(rsi_series):
                rsi.append(float(rsi_series.iloc[i]) if not pd.isna(rsi_series.iloc[i]) else 50)
            else:
                rsi.append(50)

        # 准备MCSI指标数据 - 添加错误处理
        def safe_tolist(data, default=None):
            """安全转换为列表"""
            try:
                if hasattr(data, 'tolist'):
                    result = data.tolist()
                    # 处理NaN值
                    return [None if (isinstance(x, float) and np.isnan(x)) else x for x in result]
                elif isinstance(data, list):
                    return data
                else:
                    return default or []
            except Exception:
                return default or []

        mcsi_indicators = {
            'mcsi_macd': {
                'macd': safe_tolist(mcsi_macd_data.get('macd', [])),
                'signal': safe_tolist(mcsi_macd_data.get('signal', [])),
                'histogram': safe_tolist(mcsi_macd_data.get('histogram', [])),
                'dynamic_threshold': safe_tolist(mcsi_macd_data.get('dynamic_threshold', [])),
                'macd_score': safe_tolist(mcsi_macd_data.get('macd_score', [])),
                'hist_colors': mcsi_macd_data.get('hist_colors', []) if isinstance(mcsi_macd_data.get('hist_colors', []), list) else []
            },
            'mcsi_mmt': {
                'csi_buffer': safe_tolist(mcsi_mmt_data.get('csi_buffer', [])),
                'high_band': safe_tolist(mcsi_mmt_data.get('high_band', [])),
                'low_band': safe_tolist(mcsi_mmt_data.get('low_band', [])),
                'channel_score': safe_tolist(mcsi_mmt_data.get('channel_score', [])),
                'divergence_score': safe_tolist(mcsi_mmt_data.get('divergence_score', [])),
                'mmt_score': safe_tolist(mcsi_mmt_data.get('mmt_score', [])),
                'bull_div': safe_tolist(mcsi_mmt_data.get('bull_div', [])),
                'bear_div': safe_tolist(mcsi_mmt_data.get('bear_div', [])),
                'hidden_bull_div': safe_tolist(mcsi_mmt_data.get('hidden_bull_div', [])),
                'hidden_bear_div': safe_tolist(mcsi_mmt_data.get('hidden_bear_div', []))
            },
            'mcsi_rsi': {
                'crsi_daily': safe_tolist(mcsi_rsi_data.get('crsi_daily', [])),
                'db_daily': safe_tolist(mcsi_rsi_data.get('db_daily', [])),
                'ub_daily': safe_tolist(mcsi_rsi_data.get('ub_daily', [])),
                'crsi_weekly': safe_tolist(mcsi_rsi_data.get('crsi_weekly', [])),
                'db_weekly': safe_tolist(mcsi_rsi_data.get('db_weekly', [])),
                'ub_weekly': safe_tolist(mcsi_rsi_data.get('ub_weekly', [])),
                'daily_signals': mcsi_rsi_data.get('daily_signals', []) if isinstance(mcsi_rsi_data.get('daily_signals', []), list) else [],
                'weekly_signals': mcsi_rsi_data.get('weekly_signals', []) if isinstance(mcsi_rsi_data.get('weekly_signals', []), list) else [],
                'rsi_score': safe_tolist(mcsi_rsi_data.get('rsi_score', [])),
                'background_colors': mcsi_rsi_data.get('background_colors', []) if isinstance(mcsi_rsi_data.get('background_colors', []), list) else []
            },
            'mcsi_ttm': {
                'ttm_score': safe_tolist(mcsi_ttm_data.get('ttm_score', mcsi_ttm_data.get('td9_score', []))),
                'background_colors': mcsi_ttm_data.get('background_colors', []) if isinstance(mcsi_ttm_data.get('background_colors', []), list) else [],
                'up_shapes': safe_tolist(mcsi_ttm_data.get('up_shapes', [])),
                'down_shapes': safe_tolist(mcsi_ttm_data.get('down_shapes', [])),
                # 兼容性字段，支持旧系统
                'td_up_count': safe_tolist(mcsi_ttm_data.get('td_up_count', [])),
                'td_down_count': safe_tolist(mcsi_ttm_data.get('td_down_count', [])),
                'td9_score': safe_tolist(mcsi_ttm_data.get('ttm_score', mcsi_ttm_data.get('td9_score', [])))
            }
        }

        # 返回旧系统兼容格式 - 数据嵌套在data字段中
        return jsonify({
            'success': True,
            'stock_code': stock_code,
            'data_points': len(stock_data),
            'dates': dates,
            'prices': prices,
            'ma': ma,
            'macd': macd,
            'rsi': rsi,
            'mcsi_indicators': mcsi_indicators
        })

    except Exception as e:
        logging.error(f"获取图表数据失败: {str(e)}")
        return jsonify({'success': False, 'message': f'获取图表数据失败: {str(e)}'})

@app.route('/api/config')
def get_config():
    """获取配置信息"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500
        
        config = scorer.get_config()
        groups = scorer.list_groups()
        weights = scorer.get_group_weights()
        
        return jsonify({
            'config': config,
            'groups': groups,
            'weights': weights,
            'status': 'success'
        })
    except Exception as e:
        logging.error(f"获取配置失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/config', methods=['POST'])
def update_config():
    """更新配置"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500
        
        data = request.get_json()
        config = data.get('config', {})
        
        scorer.update_config(config)
        scorer.save_config()
        
        return jsonify({
            'message': '配置更新成功',
            'config': scorer.get_config(),
            'status': 'success'
        })
    except Exception as e:
        logging.error(f"更新配置失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/group-weights', methods=['GET', 'POST'])
def handle_group_weights():
    """处理分组权重配置"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500

        if request.method == 'GET':
            # 获取当前分组权重
            group_weights = scorer.group_manager.group_weights
            return jsonify({
                'success': True,
                'weights': group_weights
            })

        elif request.method == 'POST':
            # 更新分组权重
            data = request.get_json()
            new_weights = data.get('weights', {})

            # 验证权重总和
            total_weight = sum(new_weights.values())
            if abs(total_weight - 1.0) > 0.01:
                return jsonify({
                    'success': False,
                    'message': f'权重总和必须为1.0，当前为{total_weight:.2f}'
                })

            # 更新权重（自动保存）
            scorer.group_manager.set_group_weights(new_weights, auto_save=True)

            # 重新加载配置以确保生效
            scorer.group_manager.load_config()

            return jsonify({
                'success': True,
                'message': '分组权重已更新'
            })

    except Exception as e:
        logging.error(f"处理分组权重失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'处理失败: {str(e)}'
        })

@app.route('/api/unit-weights', methods=['GET', 'POST'])
def handle_unit_weights():
    """处理计分单元权重配置"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500

        if request.method == 'GET':
            # 获取当前计分单元权重
            unit_weights = {}
            for group_id, group in scorer.group_manager.groups.items():
                unit_weights[group_id] = group.get_unit_weights()

            return jsonify({
                'success': True,
                'unit_weights': unit_weights
            })

        elif request.method == 'POST':
            data = request.get_json()
            action = data.get('action')

            if action == 'add':
                # 添加计分单元到分组
                group_id = data.get('group_id')
                unit_id = data.get('unit_id')
                weight = data.get('weight', 1.0)

                if group_id not in scorer.group_manager.groups:
                    return jsonify({
                        'success': False,
                        'message': f'分组 {group_id} 不存在'
                    })

                # 创建计分单元实例
                unit_instance = create_scoring_unit(unit_id)
                if not unit_instance:
                    return jsonify({
                        'success': False,
                        'message': f'无法创建计分单元 {unit_id}'
                    })

                # 添加到分组
                group = scorer.group_manager.groups[group_id]
                group.add_scoring_unit(unit_instance, weight)

                # 保存配置
                scorer.group_manager.save_config()

                return jsonify({
                    'success': True,
                    'message': f'计分单元 {unit_id} 已添加到分组 {group_id}'
                })

            elif action == 'remove':
                # 从分组中移除计分单元
                group_id = data.get('group_id')
                unit_id = data.get('unit_id')

                if group_id in scorer.group_manager.groups:
                    group = scorer.group_manager.groups[group_id]
                    group.remove_scoring_unit(unit_id)
                    scorer.group_manager.save_config()

                return jsonify({
                    'success': True,
                    'message': f'计分单元 {unit_id} 已从分组 {group_id} 移除'
                })

            elif action == 'update_all':
                # 批量更新计分单元权重
                unit_weights = data.get('unit_weights', {})

                for group_id, weights in unit_weights.items():
                    if group_id in scorer.group_manager.groups:
                        group = scorer.group_manager.groups[group_id]
                        group.set_unit_weights(weights, notify_manager=True)

                # 重新加载配置以确保生效
                scorer.group_manager.load_config()

                return jsonify({
                    'success': True,
                    'message': '计分单元权重已更新'
                })

            else:
                return jsonify({
                    'success': False,
                    'message': f'未知操作: {action}'
                })

    except Exception as e:
        logging.error(f"处理计分单元权重失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'处理失败: {str(e)}'
        })

@app.route('/api/weight-config-info')
def get_weight_config_info():
    """获取权重配置信息"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500

        # 获取权重配置管理器信息
        config_info = scorer.group_manager.weight_config_manager.get_config_info()

        return jsonify({
            'success': True,
            'config_info': config_info
        })

    except Exception as e:
        logging.error(f"获取权重配置信息失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取失败: {str(e)}'
        })

@app.route('/api/weight-config-history')
def get_weight_config_history():
    """获取权重配置历史记录"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500

        # 获取配置历史记录
        history = scorer.group_manager.weight_config_manager.config_history

        return jsonify({
            'success': True,
            'history': history[-10:]  # 返回最近10条记录
        })

    except Exception as e:
        logging.error(f"获取权重配置历史失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取失败: {str(e)}'
        })

@app.route('/api/weight-config-restore', methods=['POST'])
def restore_weight_config():
    """恢复权重配置"""
    try:
        if not scorer:
            return jsonify({'error': '评分器未初始化'}), 500

        data = request.get_json()
        history_index = data.get('index', -1)

        # 恢复配置
        success = scorer.group_manager.weight_config_manager.restore_from_history(history_index)

        if success:
            # 重新加载配置
            scorer.group_manager.load_config()

            return jsonify({
                'success': True,
                'message': '权重配置已恢复'
            })
        else:
            return jsonify({
                'success': False,
                'message': '恢复权重配置失败'
            })

    except Exception as e:
        logging.error(f"恢复权重配置失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'恢复失败: {str(e)}'
        })

def create_scoring_unit(unit_id):
    """创建计分单元实例"""
    try:
        if unit_id == 'rsi_unit':
            from core.scoring_units.rsi_scoring_unit import RSIScoringUnit
            return RSIScoringUnit()
        elif unit_id == 'macd_unit':
            from core.scoring_units.macd_scoring_unit import MACDScoringUnit
            return MACDScoringUnit()
        elif unit_id == 'trend_unit':
            from core.scoring_units.trend_scoring_unit import TrendScoringUnit
            return TrendScoringUnit()
        elif unit_id == 'wave_unit':
            from core.scoring_units.wave_scoring_unit import WaveScoringUnit
            return WaveScoringUnit()
        elif unit_id == 'mcsi_macd_unit':
            from core.scoring_units.mcsi_adapter import MCSIMACDScoringUnit
            return MCSIMACDScoringUnit()
        elif unit_id == 'mcsi_mmt_unit':
            from core.scoring_units.mcsi_adapter import MCSIMMTScoringUnit
            return MCSIMMTScoringUnit()
        elif unit_id == 'mcsi_rsi_unit':
            from core.scoring_units.mcsi_adapter import MCSIRSIScoringUnit
            return MCSIRSIScoringUnit()
        elif unit_id == 'mcsi_td9_unit':
            from core.scoring_units.mcsi_adapter import MCSITD9ScoringUnit
            return MCSITD9ScoringUnit()
        else:
            return None
    except Exception as e:
        logging.error(f"创建计分单元失败: {str(e)}")
        return None

if __name__ == '__main__':
    # 初始化评分器
    if init_scorer():
        logging.info("启动新的Web应用...")
        app.run(
            host='0.0.0.0',
            port=50505,
            debug=True
        )
    else:
        logging.error("评分器初始化失败，无法启动Web应用")
