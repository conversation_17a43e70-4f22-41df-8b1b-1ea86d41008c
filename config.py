#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
趋势分析配置文件
"""

import os

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 5433,
    'database': 'fintech_db',
    'user': 'postgres',
    'password': 'robot2025'
}

# 服务配置
SERVICE_CONFIG = {
    'host': '0.0.0.0',
    'port': 5008,
    'debug': True
}

# 移动平均线配置
MA_PERIODS = {
    'short': 5,      # 5日线 (周线)
    'medium': 20,    # 20日线 (月线)
    'long': 50,      # 50日线 (季度线)
    'ultra_long': 200  # 200日线 (年线)
}

# 趋势评分配置
TREND_SCORING = {
    'base_score': 0,  # 基础分数
    'max_score': 10,  # 最高分数
    'min_score': -10, # 最低分数
    
    # 评分规则权重
    'weights': {
        'ma5_vs_ma20': 1,    # 5日线 vs 20日线
        'ma5_vs_ma50': 1,    # 5日线 vs 50日线  
        'ma5_vs_ma200': 1,   # 5日线 vs 200日线
        'ma20_vs_ma50': 1,   # 20日线 vs 50日线
        'ma20_vs_ma200': 1,  # 20日线 vs 200日线
        'ma50_vs_ma200': 1   # 50日线 vs 200日线
    }
}

# 文件路径配置
PATHS = {
    'market_config': os.path.join('..', 'market_data_collector', 'config', 'markets', 'market_config.yaml'),
    'logs': 'logs',
    'templates': 'templates',
    'static': 'static'
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '[%(asctime)s] %(levelname)s - %(message)s',
    'datefmt': '%Y-%m-%d %H:%M:%S'
}
