WARNING:core.groups.base_group.oscillation_group:⚠️ 无法导入MCSI高级计分单元: No module named 'core.scoring_units.mcsi_premium_units'
🔧 开发模式启动
📊 AI市场分析系统 v2.0 (Debug Mode)
==================================================
Warning: MCSI Premium units import failed: No module named 'core.scoring_units.mcsi_premium_units'
✅ 评分器初始化成功
🌐 启动开发服务器...
📱 本地访问: http://127.0.0.1:50505
📱 网络访问: http://**********:50505
🔄 热重载已启用
==================================================
 * Serving Flask app 'web.app'
 * Debug mode: on
INFO:werkzeug:[31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:50505
 * Running on http://**********:50505
INFO:werkzeug:[33mPress CTRL+C to quit[0m
INFO:werkzeug: * Restarting with stat
WARNING:core.groups.base_group.oscillation_group:⚠️ 无法导入MCSI高级计分单元: No module named 'core.scoring_units.mcsi_premium_units'
WARNING:werkzeug: * Debugger is active!
INFO:werkzeug: * Debugger PIN: 104-214-443
INFO:werkzeug:127.0.0.1 - - [19/Aug/2025 15:28:59] "HEAD / HTTP/1.1" 200 -
INFO:werkzeug: * Detected change in '/home/<USER>/Analyze-system2/web/app.py', reloading
🔧 开发模式启动
📊 AI市场分析系统 v2.0 (Debug Mode)
==================================================
Warning: MCSI Premium units import failed: No module named 'core.scoring_units.mcsi_premium_units'
✅ 评分器初始化成功
🌐 启动开发服务器...
📱 本地访问: http://127.0.0.1:50505
📱 网络访问: http://**********:50505
🔄 热重载已启用
==================================================
INFO:werkzeug: * Restarting with stat
WARNING:core.groups.base_group.oscillation_group:⚠️ 无法导入MCSI高级计分单元: No module named 'core.scoring_units.mcsi_premium_units'
WARNING:werkzeug: * Debugger is active!
INFO:werkzeug: * Debugger PIN: 104-214-443
INFO:werkzeug:192.168.2.50 - - [19/Aug/2025 15:29:33] "GET / HTTP/1.1" 200 -
INFO:werkzeug:192.168.2.50 - - [19/Aug/2025 15:29:37] "GET / HTTP/1.1" 200 -
WARNING:core.groups.group_manager:⚠️ 无法导入MCSI高级计分单元: No module named 'core.scoring_units.mcsi_premium_units'
INFO:werkzeug:192.168.2.50 - - [19/Aug/2025 15:29:38] "GET /api/scoring_units HTTP/1.1" 200 -
INFO:werkzeug:192.168.2.50 - - [19/Aug/2025 15:29:38] "GET /api/groups HTTP/1.1" 200 -
ERROR:core.data.data_loader:获取表 crypto_btc_usdt_比特币 数据失败: (psycopg2.errors.UndefinedColumn) column "period" does not exist
LINE 4:                     WHERE period = 'daily' OR period IS NULL
                                  ^

[SQL: 
                    SELECT timestamp, open, high, low, close, volume
                    FROM "crypto_btc_usdt_比特币"
                    WHERE period = 'daily' OR period IS NULL
                    ORDER BY timestamp DESC
                    LIMIT %(limit)s
                ]
[parameters: {'limit': 1}]
(Background on this error at: https://sqlalche.me/e/20/f405)
WARNING:core.data.csv_data_loader:未找到股票数据文件: crypto_btc_usdt_比特币
WARNING:core.data.hybrid_data_loader:CSV中也未找到 crypto_btc_usdt_比特币 数据
ERROR:core.data.hybrid_data_loader:所有数据源都无法获取 crypto_btc_usdt_比特币 数据
WARNING:core.data.data_loader:表 usstocks_cycu_cycurion_inc 没有数据
WARNING:core.data.csv_data_loader:未找到股票数据文件: usstocks_cycu_cycurion_inc
WARNING:core.data.hybrid_data_loader:CSV中也未找到 usstocks_cycu_cycurion_inc 数据
ERROR:core.data.hybrid_data_loader:所有数据源都无法获取 usstocks_cycu_cycurion_inc 数据
WARNING:core.data.data_loader:表 usstocks_limnw_liminatus_pharma_inc_wt 没有数据
WARNING:core.data.csv_data_loader:未找到股票数据文件: usstocks_limnw_liminatus_pharma_inc_wt
WARNING:core.data.hybrid_data_loader:CSV中也未找到 usstocks_limnw_liminatus_pharma_inc_wt 数据
ERROR:core.data.hybrid_data_loader:所有数据源都无法获取 usstocks_limnw_liminatus_pharma_inc_wt 数据
WARNING:core.scoring_units.base_scoring_unit.trend_unit:数据量不足，需要至少 210 个数据点
WARNING:core.scoring_units.base_scoring_unit.trend_unit:数据量不足，需要至少 210 个数据点
INFO:werkzeug:192.168.2.50 - - [19/Aug/2025 15:29:59] "POST /api/analyze HTTP/1.1" 200 -
INFO:werkzeug:192.168.2.50 - - [19/Aug/2025 15:30:00] "GET /api/status HTTP/1.1" 200 -
INFO:werkzeug:192.168.2.50 - - [19/Aug/2025 15:30:08] "GET /api/results HTTP/1.1" 200 -
INFO:werkzeug:192.168.2.50 - - [19/Aug/2025 15:30:12] "GET /professional_chart.html?stock=XAGUSD&name=现货白银 HTTP/1.1" 200 -
INFO:web.app:图表数据生成: XAGUSD (类型: stock)
INFO:werkzeug:192.168.2.50 - - [19/Aug/2025 15:30:16] "GET /api/stock_chart/XAGUSD?limit=1000 HTTP/1.1" 200 -
INFO:werkzeug: * Detected change in '/home/<USER>/Analyze-system2/check_environment.py', reloading
🔧 开发模式启动
📊 AI市场分析系统 v2.0 (Debug Mode)
==================================================
Warning: MCSI Premium units import failed: No module named 'core.scoring_units.mcsi_premium_units'
✅ 评分器初始化成功
🌐 启动开发服务器...
📱 本地访问: http://127.0.0.1:50505
📱 网络访问: http://**********:50505
🔄 热重载已启用
==================================================
INFO:werkzeug: * Restarting with stat
WARNING:core.groups.base_group.oscillation_group:⚠️ 无法导入MCSI高级计分单元: No module named 'core.scoring_units.mcsi_premium_units'
WARNING:werkzeug: * Debugger is active!
INFO:werkzeug: * Debugger PIN: 104-214-443
🔧 开发模式启动
📊 AI市场分析系统 v2.0 (Debug Mode)
==================================================
Warning: MCSI Premium units import failed: No module named 'core.scoring_units.mcsi_premium_units'
✅ 评分器初始化成功
🌐 启动开发服务器...
📱 本地访问: http://127.0.0.1:50505
📱 网络访问: http://**********:50505
🔄 热重载已启用
==================================================
